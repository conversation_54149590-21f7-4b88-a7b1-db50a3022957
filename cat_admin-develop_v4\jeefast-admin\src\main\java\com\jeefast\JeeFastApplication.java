package com.jeefast;

import java.net.InetAddress;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.cat.job.CachInitJob;
import com.cat.job.MessageJob;
import com.jeefast.common.utils.SpringContextHolder;
import com.jeefast.common.utils.spring.SpringUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class,DruidDataSourceAutoConfigure.class })
////@MapperScan("com.jeefast.**.mapper")
@ComponentScan(basePackages={"com.jeefast","com.cat"})
@MapperScan("com.**.mapper")
public class JeeFastApplication
{
    public static void main(String[] args) throws Exception{
        // System.setProperty("spring.devtools.restart.enabled", "false");
        //SpringApplication.run(JeeFastApplication.class, args);
        //System.out.println("(♥◠‿◠)ﾉﾞ  启动成功   ლ(´ڡ`ლ)ﾞ  \n");

        ConfigurableApplicationContext application = SpringApplication.run(JeeFastApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        System.out.println("\n----------------------------------------------------------\n\t" +
            "Application 工程启动成功 is running! Access URLs:\n\t" +
            "Local: \t\thttp://localhost:" + port + path + "\n\t" +
            "External: \thttp://" + ip + ":" + port + path + "\n" +
            "----------------------------------------------------------\n");
        //启动推送消费者队列
        SpringUtils.getBean(MessageJob.class).run();
        //初始化系统参数到redis
        initSysConfig();
        System.out.println("==========后台启动成功=============");
    }


    //初始化系统配置到redis
    public static void initSysConfig(){
        CachInitJob cachInitJob = SpringContextHolder.getBean(CachInitJob.class);
        cachInitJob.initSysConfig();
    }


}
package com.jeefast.cat.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.CommentInfoBackstage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 评论管理 服务层
 *
 * <AUTHOR>
 * @date 2020-08-17
 */
public interface ICommentInfoBackstageService extends IService<CommentInfoBackstage> {


    List<CamelCaseMap> getList(QueryWrapper qw);


    boolean delete(String ids);
}
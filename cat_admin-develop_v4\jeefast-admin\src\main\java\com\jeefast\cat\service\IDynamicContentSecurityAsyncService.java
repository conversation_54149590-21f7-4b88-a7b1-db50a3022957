package com.jeefast.cat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jeefast.cat.domain.DynamicContentSecurity;
import com.jeefast.cat.domain.DynamicInfoBackstage;

import java.util.List;

/**
 * 动态内容安全审核记录 服务层
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
public interface IDynamicContentSecurityAsyncService extends IService<DynamicContentSecurity> {

    /**
     * 动态内容安全审核方法
     *
     */
    Boolean syncDynamicContentSecurityCheck(DynamicInfoBackstage newDynamicInfo, String oldTitle,String oldContent,
                                            List<String> addDynamicMediaId, List<String> delDynamicMediaId);

}
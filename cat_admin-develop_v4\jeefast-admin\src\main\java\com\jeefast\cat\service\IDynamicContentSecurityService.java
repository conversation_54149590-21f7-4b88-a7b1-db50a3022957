package com.jeefast.cat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jeefast.cat.domain.DynamicContentSecurity;

/**
 * 动态内容安全审核记录 服务层
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
public interface IDynamicContentSecurityService extends IService<DynamicContentSecurity> {
    /**
     * 文字内容安全
     *
     * @param type    1 标题 2 内容
     * @param content
     */
    DynamicContentSecurity textModeration(String type, String content);

    /**
     * 图片内容安全
     *
     * @param imgUrl
     * @return
     */
    DynamicContentSecurity imgModeration(String imgUrl);

    /**
     * 视频内容安全
     *
     * @param videoUrl
     * @return
     */
    DynamicContentSecurity videoModerationCreate(String videoUrl);

    /**
     * 视频内容安全查询 任务 未完成的任务查询检查结果情况
     */
    void videoModerationQueryJob();
}
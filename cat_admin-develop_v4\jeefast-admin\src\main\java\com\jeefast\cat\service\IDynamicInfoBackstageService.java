package com.jeefast.cat.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.DynamicInfoBackstage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jeefast.cat.req.AddDynamicReq;
import com.jeefast.cat.req.EditDynamicReq;
import com.jeefast.cat.req.NewAddOrEditDynamicReq;

import java.util.List;

/**
 * 动态内容 服务层
 *
 * <AUTHOR>
 * @date 2020-08-08
 */
public interface IDynamicInfoBackstageService extends IService<DynamicInfoBackstage> {

    List<CamelCaseMap<String, Object>> dynamicPinnedList(QueryWrapper<DynamicInfoBackstage> queryWrapper);
    List<CamelCaseMap<String, Object>> dynamicList(QueryWrapper<DynamicInfoBackstage> queryWrapper);

    boolean delete(List<String> asList);

    /**
     * 详述: 添加动态和相关媒体信息
     * 开发人员：jingwei.huang
     * 创建时间：2021/2/25 下午8:23
     *
     * @return: boolean
     */
    boolean addSave(AddDynamicReq req);
    boolean newAddSave(NewAddOrEditDynamicReq req);

    /**
     * 驳回动态内容
     *
     * @param reasons 驳回原因
     * @return
     */
    boolean reasonsSub(String dynamicId,String reasons);
    boolean passDynamicId(String dynamicId,String reasons);

    boolean pinned(String dynamicId);

    boolean unPinned(String dynamicId);

    boolean updateInfo(EditDynamicReq req);

    boolean newUpdateInfo(NewAddOrEditDynamicReq req);

    void autoAddVisualView(Integer regionStart,Integer regionEnd,Integer recentDays);
}
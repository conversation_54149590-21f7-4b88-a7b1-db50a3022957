package com.jeefast.cat.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.ExchangeGoodsLogBackstage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 兑换商品日志 服务层
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
public interface IExchangeGoodsLogBackstageService extends IService<ExchangeGoodsLogBackstage> {

    List<CamelCaseMap<String, Object>> logList(QueryWrapper<ExchangeGoodsLogBackstage> queryWrapper);
}
package com.jeefast.cat.service;

import com.alibaba.fastjson.JSONObject;
import com.jeefast.cat.domain.Channel;
import com.jeefast.cat.domain.Hospital;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jeefast.cat.resp.DictResp;

import java.util.List;

/**
 * 医院 服务层
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
public interface IHospitalService extends IService<Hospital> {
    public int changeStatus(Hospital hospital);

    public boolean softDelete(String ids);

    public List<Hospital> processList(List<Hospital> hospitalList);

    public List<DictResp> getHospitalDeptInfo(Long id);
}
package com.jeefast.cat.service;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jeefast.cat.req.AICountReq;
import com.jeefast.cat.req.AIDeptCountReq;
import com.jeefast.cat.resp.AIDeptCountResp;
import com.jeefast.common.json.JSONObject;
import org.springframework.ui.ModelMap;

public interface IStatisticsService {
    public JSONArray count(AICountReq req) throws JsonProcessingException;

    public AIDeptCountResp deptCount(AIDeptCountReq req) throws JsonProcessingException;

    public JSONArray deptTotal(AIDeptCountReq req) throws JsonProcessingException;

    public ModelMap getModelMap(ModelMap mmap);
}

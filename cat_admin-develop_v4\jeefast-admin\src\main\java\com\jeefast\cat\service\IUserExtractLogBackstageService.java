package com.jeefast.cat.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.UserExtractLogBackstage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 用户提现记录 服务层
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
public interface IUserExtractLogBackstageService extends IService<UserExtractLogBackstage> {

    List<CamelCaseMap<String, Object>> infoList(QueryWrapper<UserExtractLogBackstage> queryWrapper);

    /**
     * 审核操作
     * @param id
     * @param status 提现状态:1-审核中,2-进行中,3-完成,4-失败,5-驳回
     * @param reason 驳回原因
     * @param remark 备注
     * @return
     */
    boolean authSave(String id, String status,String reason,String remark);

}
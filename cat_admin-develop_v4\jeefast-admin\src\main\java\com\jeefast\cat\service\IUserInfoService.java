package com.jeefast.cat.service;

import com.jeefast.cat.domain.UserInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jeefast.cat.req.UserRechargeReq;
import com.jeefast.common.core.domain.AjaxResult;

/**
 * 用户信息 服务层
 *
 * <AUTHOR>
 * @date 2019-12-19
 */
public interface IUserInfoService extends IService<UserInfo> {


    /**
     * 详述: 用户积分/余额充值
     * 开发人员：jingwei.huang
     * 创建时间：2021/7/29 9:32 下午
     * @param req
     * @return: com.jeefast.common.core.domain.AjaxResult
     */
    AjaxResult recharge(UserRechargeReq req);


}
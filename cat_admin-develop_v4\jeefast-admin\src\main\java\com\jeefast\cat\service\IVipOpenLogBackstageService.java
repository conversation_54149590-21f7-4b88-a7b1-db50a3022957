package com.jeefast.cat.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.VipOpenLogBackstage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * vip开通记录 服务层
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
public interface IVipOpenLogBackstageService extends IService<VipOpenLogBackstage> {

    List<CamelCaseMap<String, Object>> userList(QueryWrapper<VipOpenLogBackstage> queryWrapper);
}
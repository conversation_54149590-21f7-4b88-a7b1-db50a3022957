package com.jeefast.cat.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jeefast.cat.common.EleAliasInfo;
import com.jeefast.cat.domain.NurseBillModel;
import com.jeefast.cat.req.addEleAliasReq;

import java.math.BigInteger;
import java.util.List;

public interface NurseBillService extends IService<NurseBillModel> {


    List<CamelCaseMap<String, Object>> NurseBillList(QueryWrapper<NurseBillModel> queryWrapper);

    boolean delete(List<String> asList);

//    boolean updateInfo(NurseBillModel req);

    boolean Move(NurseBillModel req, QueryWrapper<NurseBillModel> queryWrapper);

    boolean addSave(addEleAliasReq req);

    EleAliasInfo selectAliasByEleId(BigInteger eleId);


    boolean updateNurseBill(NurseBillModel nurseBill);
}

package com.jeefast.cat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.content.entity.ContentTopicRel;
import com.cat.modules.content.service.IContentTopicRelService;
import com.jeefast.cat.domain.CommentInfoBackstage;
import com.jeefast.cat.domain.TopicInfoBackstage;
import com.jeefast.cat.domain.UserInfo;
import com.jeefast.cat.service.ICommentInfoBackstageService;
import com.jeefast.cat.service.ITopicInfoBackstageService;
import com.jeefast.cat.service.IUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.ArticleInfoBackstageMapper;
import com.jeefast.cat.domain.ArticleInfoBackstage;
import com.jeefast.cat.service.IArticleInfoBackstageService;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 文章 服务层实现
 *
 * <AUTHOR>
 * @date 2020-08-30
 */
@Service
//@DS("slave")去掉多数据源
public class ArticleInfoBackstageServiceImpl extends ServiceImpl<ArticleInfoBackstageMapper, ArticleInfoBackstage> implements IArticleInfoBackstageService {

    @Autowired
    private ArticleInfoBackstageMapper articleInfoMapper;
    @Autowired
    private IContentTopicRelService contentTopicRelService;
    @Autowired
    private ITopicInfoBackstageService topicInfoBackstageService;
    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private ICommentInfoBackstageService commentInfoService;

    @Override
    @Transactional
    public boolean delete(List<String> idList) {
        for(String id:idList){
            ArticleInfoBackstage articleInfo = this.getById(id);
            boolean flag = this.removeById(id);
            //先查出待减少的话题id
            List<ContentTopicRel> topincRelList = contentTopicRelService.list(new QueryWrapper<ContentTopicRel>()
                    .select("topic_id")
                    .eq("business_id",id)
                    .eq("business_type","2")
            );
            contentTopicRelService.remove(new UpdateWrapper<ContentTopicRel>()
                    .eq("business_id",id)
                    .eq("business_type","2")
            );
            if(flag) {
                for(ContentTopicRel topicRel:topincRelList){
                    //减少话题关联数
                    topicInfoBackstageService.update(new UpdateWrapper<TopicInfoBackstage>()
                            .setSql("cite_count=cite_count-1")
                            .eq("topic_id",topicRel.getTopicId())
                    );
                }
                commentInfoService.remove(new UpdateWrapper<CommentInfoBackstage>()
                        .eq("business_id",id).eq("business_type","2")
                );
            }
        }
        return true;
    }
}
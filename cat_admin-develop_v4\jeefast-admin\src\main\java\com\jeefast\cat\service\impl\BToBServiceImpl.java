package com.jeefast.cat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeefast.cat.domain.Channel;
import com.jeefast.cat.domain.Hospital;
import com.jeefast.cat.mapper.ChannelMapper;
import com.jeefast.cat.mapper.HospitalMapper;
import com.jeefast.common.core.text.Convert;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.BToBMapper;
import com.jeefast.cat.domain.BToB;
import com.jeefast.cat.service.IBToBService;
import com.baomidou.dynamic.datasource.annotation.DS;

import javax.annotation.Resource;
import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.List;

/**
 * 外部调用 服务层实现
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@Service
//@DS("slave")去掉多数据源
public class BToBServiceImpl extends ServiceImpl<BToBMapper, BToB> implements IBToBService {

    @Resource
    private BToBMapper bMapper;

    @Resource
    private ChannelMapper channelMapper;

    @Resource
    private HospitalMapper hospitalMapper;

    @Resource
    private IBToBService toBService;

    /**
     * 软删除
     *
     * @param ids
     * @return
     */
    @Override
    public boolean softDelete(String ids) {
        List<String> idList = Arrays.asList(Convert.toStrArray(ids));
        List<BToB> dataList = bMapper.selectList(new QueryWrapper<BToB>().in("user_id", idList));
        if (dataList != null && dataList.size() > 0) {
            dataList.forEach(itm -> {
                itm.setIsDelete(1);
                bMapper.updateById(itm);
            });
        }
        return true;
    }

    /**
     * 保存数据
     *
     * @param bToB
     * @return
     */
    @Override
    public boolean createInfo(BToB bToB) {
        bToB.setIsDelete(0);
        Channel channel = channelMapper.selectById(bToB.getChannelId());
        Hospital hospital = hospitalMapper.selectById(bToB.getHospitalId());
        bToB.setChannelName(channel.getChannelName());
        bToB.setHospitalName(hospital.getHospitalName());
        bMapper.insert(bToB);
        return true;
    }

    @Override
    public boolean updateInfo(BToB bToB) {
        Channel channel = channelMapper.selectById(bToB.getChannelId());
        Hospital hospital = hospitalMapper.selectById(bToB.getHospitalId());
        bToB.setChannelName(channel.getChannelName());
        bToB.setHospitalName(hospital.getHospitalName());
        bMapper.updateById(bToB);
        return true;
    }
}
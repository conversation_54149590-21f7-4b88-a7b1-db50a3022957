package com.jeefast.cat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.BannerInfoBackstageMapper;
import com.jeefast.cat.domain.BannerInfoBackstage;
import com.jeefast.cat.service.IBannerInfoBackstageService;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * 首页banner图 服务层实现
 *
 * <AUTHOR>
 * @date 2020-08-09
 */
@Service
//@DS("slave")去掉多数据源
public class BannerInfoBackstageServiceImpl extends ServiceImpl<BannerInfoBackstageMapper, BannerInfoBackstage> implements IBannerInfoBackstageService {

}
package com.jeefast.cat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeefast.common.constant.UserConstants;
import com.jeefast.common.core.domain.Ztree;
import com.jeefast.common.core.domain.ZtreeExt;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.system.domain.SysDept;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.CategoryBackstageMapper;
import com.jeefast.cat.domain.CategoryBackstage;
import com.jeefast.cat.service.ICategoryBackstageService;
import com.baomidou.dynamic.datasource.annotation.DS;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品类别 服务层实现
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
@Service
//@DS("slave")去掉多数据源
public class CategoryBackstageServiceImpl extends ServiceImpl<CategoryBackstageMapper, CategoryBackstage> implements ICategoryBackstageService {

    @Autowired
    private CategoryBackstageMapper dao;

    @Override
    public CategoryBackstage selectByCategoryId(String categoryId) {
        return dao.selectByCategoryId(categoryId);
    }

    @Override
    public List<ZtreeExt> selectDeptTree(CategoryBackstage category) {
        List<CategoryBackstage> deptList = this.list();
        List<ZtreeExt> ztrees = initZtree(deptList);
        return ztrees;
    }


    /**
     * 对象转部门树
     *
     * @param deptList 部门列表
     * @return 树结构列表
     */
    public List<ZtreeExt> initZtree(List<CategoryBackstage> deptList)
    {
        return initZtree(deptList, null);
    }

    /**
     * 对象转部门树
     *
     * @param deptList 部门列表
     * @param roleDeptList 角色已存在菜单列表
     * @return 树结构列表
     */
    public List<ZtreeExt> initZtree(List<CategoryBackstage> deptList, List<String> roleDeptList)
    {

        List<ZtreeExt> ztrees = new ArrayList<ZtreeExt>();
        boolean isCheck = StringUtils.isNotNull(roleDeptList);
        for (CategoryBackstage dept : deptList)
        {
            if (1==dept.getStatus())
            {
                ZtreeExt ztree = new ZtreeExt();
                ztree.setId(dept.getId());
                ztree.setpId(dept.getParentId());
                ztree.setName(dept.getName());
                ztree.setTitle(dept.getName());
                if (isCheck)
                {
                    ztree.setChecked(roleDeptList.contains(dept.getId() + dept.getName()));
                }
                ztrees.add(ztree);
            }
        }
        return ztrees;
    }
}
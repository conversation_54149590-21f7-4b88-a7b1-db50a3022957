package com.jeefast.cat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeefast.common.core.text.Convert;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.ChannelMapper;
import com.jeefast.cat.domain.Channel;
import com.jeefast.cat.service.IChannelService;
import com.baomidou.dynamic.datasource.annotation.DS;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * AI分诊渠道 服务层实现
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@Service
//@DS("slave")去掉多数据源
public class ChannelServiceImpl extends ServiceImpl<ChannelMapper, Channel> implements IChannelService {

    @Resource
    private ChannelMapper channelMapper;

    @Override
    public int changeStatus(Channel channel) {
        return channelMapper.updateById(channel);
    }

    @Override
    public boolean softDelete(String ids) {
        List<String> idList = Arrays.asList(Convert.toStrArray(ids));
        List<Channel> channelList = channelMapper.selectBatchIds(idList);
        if (channelList != null && channelList.size() > 0) {
            channelList.forEach(itm -> {
                Channel updateChannel = new Channel();
                updateChannel.setId(itm.getId());
                updateChannel.setIsDelete(1);
                channelMapper.updateById(updateChannel);
            });
        }
        return true;
    }
}
package com.jeefast.cat.service.impl;

import cn.hutool.core.map.CamelCaseMap;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.article.entity.ArticleInfo;
import com.cat.modules.comment.entity.CommentInfo;
import com.cat.modules.dynamic.entity.DynamicInfo;
import com.jeefast.cat.domain.ArticleInfoBackstage;
import com.jeefast.cat.domain.DynamicInfoBackstage;
import com.jeefast.cat.service.IArticleInfoBackstageService;
import com.jeefast.cat.service.IDynamicInfoBackstageService;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.CommentInfoBackstageMapper;
import com.jeefast.cat.domain.CommentInfoBackstage;
import com.jeefast.cat.service.ICommentInfoBackstageService;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 评论管理 服务层实现
 *
 * <AUTHOR>
 * @date 2020-08-17
 */
@Service
//@DS("slave")去掉多数据源
public class CommentInfoBackstageServiceImpl extends ServiceImpl<CommentInfoBackstageMapper, CommentInfoBackstage> implements ICommentInfoBackstageService {
    @Autowired
    private CommentInfoBackstageMapper commentInfoBackstageMapper;

    @Autowired
    private IDynamicInfoBackstageService dynamicInfoService;
    @Autowired
    private IArticleInfoBackstageService articleInfoService;

    @Override
    public List<CamelCaseMap> getList(QueryWrapper qw) {
        return commentInfoBackstageMapper.getList(qw);
    }

    @Override
    @Transactional
    public boolean delete(String ids) {
        List<String> idList = Arrays.asList(Convert.toStrArray(ids));
        for (String id : idList) {
            CommentInfoBackstage commentInfo = this.getOne(new QueryWrapper<CommentInfoBackstage>()
                    .eq("comment_id",id)
                    .eq("is_delete","0")
            );
            if(commentInfo==null){
                throw new BusinessException("该评论不存在！");
            }
            String parentIds = commentInfo.getParentIds();
            int delCount = commentInfoBackstageMapper.update(null,new UpdateWrapper<CommentInfoBackstage>()
                    .set("is_delete","1")
                    .in("comment_id", id)
            );
            if(delCount>0) {
                delCount+= commentInfoBackstageMapper.update(null,new UpdateWrapper<CommentInfoBackstage>()
                        .set("is_delete","1")
                        .likeRight("parent_ids", id)
                        .eq("is_delete","0")
                );
                //最顶层回复数+1
                if(StrUtil.isNotEmpty(parentIds) && 32<=parentIds.length()){
                    String tempParentId = parentIds.substring(0,32);
                    this.update(new UpdateWrapper<CommentInfoBackstage>()
                            .setSql("reply_count=reply_count-1")
                            .eq("comment_id",tempParentId)
                    );
                }


                String businessType = commentInfo.getBusinessType();
                String businessId = commentInfo.getBusinessId();
                //评论对象类型（1：动态、2：文章、3：评论）
                if("1".equals(businessType)){
                    dynamicInfoService.update(new UpdateWrapper<DynamicInfoBackstage>()
                            .setSql("comment_count=comment_count-"+delCount)
                            .eq("dynamic_id",businessId)
                    );
                }else if("2".equals(businessType)){
                    articleInfoService.update(new UpdateWrapper<ArticleInfoBackstage>()
                            .setSql("comment_count=comment_count-"+delCount)
                            .eq("article_id",businessId)
                    );
                }
            }
        }
        return true;
    }
}
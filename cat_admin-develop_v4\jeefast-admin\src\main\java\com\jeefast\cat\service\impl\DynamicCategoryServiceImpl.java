package com.jeefast.cat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.DynamicCategoryMapper;
import com.jeefast.cat.domain.DynamicCategory;
import com.jeefast.cat.service.IDynamicCategoryService;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * 动态分类 服务层实现
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Service
//@DS("slave")去掉多数据源
public class DynamicCategoryServiceImpl extends ServiceImpl<DynamicCategoryMapper, DynamicCategory> implements IDynamicCategoryService {

}
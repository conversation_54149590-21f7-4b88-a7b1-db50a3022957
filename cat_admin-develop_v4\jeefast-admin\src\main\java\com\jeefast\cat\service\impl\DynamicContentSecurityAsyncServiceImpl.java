package com.jeefast.cat.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.dynamic.entity.DynamicMedia;
import com.cat.modules.dynamic.mapper.DynamicMediaMapper;
import com.cat.modules.notice.entity.UserPushNotice;
import com.cat.modules.notice.service.IUserPushNoticeService;
import com.jeefast.cat.domain.DynamicContentSecurity;
import com.jeefast.cat.domain.DynamicInfoBackstage;
import com.jeefast.cat.mapper.DynamicContentSecurityMapper;
import com.jeefast.cat.service.IDynamicContentSecurityAsyncService;
import com.jeefast.cat.service.IDynamicContentSecurityService;
import com.jeefast.cat.service.IDynamicInfoBackstageService;
import com.jeefast.common.enums.AuditEnum;
import com.jeefast.common.enums.NoticeTypeEnum;
import com.jeefast.common.enums.YesNoEnum;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 动态内容安全审核记录 服务层实现
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Service
public class DynamicContentSecurityAsyncServiceImpl extends ServiceImpl<DynamicContentSecurityMapper, DynamicContentSecurity> implements IDynamicContentSecurityAsyncService {

    @Autowired
    private IDynamicInfoBackstageService dynamicInfoBackstageService;
    @Autowired
    private DynamicMediaMapper dynamicMediaMapper;
    @Autowired
    private IDynamicContentSecurityService dynamicContentSecurityService;
    @Autowired
    private IUserPushNoticeService userPushNoticeService;

    /**
     * 动态内容安全审核方法
     */
    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncDynamicContentSecurityCheck(DynamicInfoBackstage newDynamicInfo, String oldTitle, String oldContent,
                                                   List<String> addDynamicMediaId, List<String> delDynamicMediaId) {
        if (CollectionUtil.isNotEmpty(delDynamicMediaId)) {
            remove(new LambdaQueryWrapper<DynamicContentSecurity>()
                    .in(DynamicContentSecurity::getRelateId, delDynamicMediaId));
        }


        Boolean noNeedSecurity = YesNoEnum.NO.getCode().equals(newDynamicInfo.getNeedSecurity());
        List<DynamicContentSecurity> toAddContentSecurityList = new ArrayList<>();
        if (!noNeedSecurity && CollectionUtil.isNotEmpty(addDynamicMediaId)) {
            List<DynamicMedia> dynamicMediaList = dynamicMediaMapper.selectList(new LambdaQueryWrapper<DynamicMedia>()
                    .in(DynamicMedia::getDynamicMediaId, addDynamicMediaId));
            //动态类型（1：文字、2：图文、3：视频）
            if ("2".equals(newDynamicInfo.getType())) {
                //下载压缩图片获取长宽高赋值
                for (int i = 0; i < dynamicMediaList.size(); i++) {
                    //不存在的 新增
                    DynamicMedia dynamicMedia = dynamicMediaList.get(i);
                    if (!noNeedSecurity) {
                        DynamicContentSecurity imgContentSecurity = dynamicContentSecurityService.imgModeration(dynamicMedia.getMediaUrl());
                        imgContentSecurity.setDynamicId(newDynamicInfo.getDynamicId());
                        imgContentSecurity.setRelateId(dynamicMedia.getDynamicMediaId());
                        toAddContentSecurityList.add(imgContentSecurity);
                    }
                }
            } else if ("3".equals(newDynamicInfo.getType())) {
                //和原本的不一样才需要处理，一样不需要处理，视频只有一个
                DynamicMedia dynamicMedia = dynamicMediaList.get(0);
                if (!noNeedSecurity) {
                    DynamicContentSecurity videoContentSecurity = dynamicContentSecurityService.videoModerationCreate(dynamicMedia.getMediaUrl());
                    videoContentSecurity.setContent(dynamicMedia.getSmallUrl());
                    videoContentSecurity.setDynamicId(newDynamicInfo.getDynamicId());
                    videoContentSecurity.setRelateId(dynamicMedia.getDynamicMediaId());
                    toAddContentSecurityList.add(videoContentSecurity);
                }

            }
        }

        if (!noNeedSecurity && StringUtils.isNotBlank(newDynamicInfo.getTitle()) && !newDynamicInfo.getTitle().equals(oldTitle)) {
            remove(new LambdaQueryWrapper<DynamicContentSecurity>()
                    .eq(DynamicContentSecurity::getDynamicId, newDynamicInfo.getDynamicId())
                    .eq(DynamicContentSecurity::getType, "1"));
            DynamicContentSecurity titleContentSecurity = dynamicContentSecurityService.textModeration("1", newDynamicInfo.getTitle());
            titleContentSecurity.setDynamicId(newDynamicInfo.getDynamicId());
            toAddContentSecurityList.add(titleContentSecurity);
        }
        if (!noNeedSecurity && StringUtils.isNotBlank(newDynamicInfo.getContent()) && !newDynamicInfo.getContent().equals(oldContent)) {
            remove(new LambdaQueryWrapper<DynamicContentSecurity>()
                    .eq(DynamicContentSecurity::getDynamicId, newDynamicInfo.getDynamicId())
                    .eq(DynamicContentSecurity::getType, "2"));
            DynamicContentSecurity contentContentSecurity = dynamicContentSecurityService.textModeration("2", newDynamicInfo.getContent());
            contentContentSecurity.setDynamicId(newDynamicInfo.getDynamicId());
            toAddContentSecurityList.add(contentContentSecurity);
        }
        if (!noNeedSecurity) {
            if (CollectionUtil.isNotEmpty(toAddContentSecurityList)) {
                //需要审核 肯定不是草稿
                dynamicContentSecurityService.saveOrUpdateBatch(toAddContentSecurityList);
            }
            //可能是不通过的，什么也不该，用原本的信息进行判断
                List<DynamicContentSecurity> dbDynamicContentSecurityList = list(new LambdaQueryWrapper<DynamicContentSecurity>()
                        .eq(DynamicContentSecurity::getDynamicId, newDynamicInfo.getDynamicId()));
                List<DynamicContentSecurity> noVideoList = dbDynamicContentSecurityList.stream().filter(i -> !i.getType().equals("4")).collect(Collectors.toList());
                List<DynamicContentSecurity> passNumList = noVideoList.stream()
                        .filter(i -> i.getAuditStatus().equals(AuditEnum.PASS.getCode())).collect(Collectors.toList());
                if (dbDynamicContentSecurityList.size() == noVideoList.size()) {
                    //没有视频
                    if (passNumList.size() == noVideoList.size()) {
                        //通过
                        newDynamicInfo.setAuditStatus(AuditEnum.PASS.getCode());
                    } else {
                        newDynamicInfo.setAuditStatus(AuditEnum.TO_RE_AUDIT.getCode());
                        noticePush(newDynamicInfo);
                    }
                } else {
                    //有视频
                    //只有一个视频，但是还有标题和内容
                    if (passNumList.size() == noVideoList.size()) {
                        //标题和内容 都通过
                        //看视频的情况
                        List<DynamicContentSecurity> videoList = dbDynamicContentSecurityList.stream().filter(i -> i.getType().equals("4")).collect(Collectors.toList());
                        if(videoList.get(0).getStatus().equals("ERROR")){
                            //出错直接设置为待人工复审
                            newDynamicInfo.setAuditStatus(AuditEnum.TO_RE_AUDIT.getCode());
                            noticePush(newDynamicInfo);
                        }else {
                            newDynamicInfo.setAuditStatus(AuditEnum.WAIT_AUDIT.getCode());
                        }
                    } else {
                        // 标题和和内容 都有问题 那直接到人工复审
                        newDynamicInfo.setAuditStatus(AuditEnum.TO_RE_AUDIT.getCode());
                        noticePush(newDynamicInfo);
                    }

                }
//            }else {
//                //可能是修改，但是没有新增修改图片视频，和表头内容，此时不用处理什么，备注下好理解
//
//            }
        } else {
            //不需要审核
            // ,不是草稿 直接通过
            if (newDynamicInfo.getIsCheck().equals(YesNoEnum.YES.getCode())) {
                newDynamicInfo.setAuditStatus(AuditEnum.PASS.getCode());
            } else {
                //不需要审核，是草稿 IsCheck 为0 ，app查不到
                dynamicContentSecurityService.remove(new LambdaQueryWrapper<DynamicContentSecurity>().
                        eq(DynamicContentSecurity::getDynamicId, newDynamicInfo.getDynamicId()));
                newDynamicInfo.setAuditStatus(AuditEnum.WAIT_AUDIT.getCode());
            }
        }
        return dynamicInfoBackstageService.updateById(newDynamicInfo);
    }

    private void noticePush(DynamicInfoBackstage newDynamicInfo) {
        UserPushNotice notice = new UserPushNotice()
                .setId(IdUtil.fastSimpleUUID())
                .setUserId("000000000")
                .setToUserId(newDynamicInfo.getUserId())
                .setType(NoticeTypeEnum.DYNAMIC_FAIL.getCode())
                .setObjectId(newDynamicInfo.getDynamicId())
                .setPushTitle(newDynamicInfo.getTitle())
                .setPushDesc("AI审核不通过")
                .setExtraStr(null)
                .setIsSend("0")
                .setIsRead("0")
                .setIsDelete("0")
                .setSendCount(0);
        userPushNoticeService.save(notice);
    }
}
package com.jeefast.cat.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeefast.cat.domain.DynamicInfoBackstage;
import com.jeefast.cat.service.IDynamicInfoBackstageService;
import com.jeefast.common.enums.AuditEnum;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.enums.YesNoEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.scm.CloudTencentSCMFileUtil;
import com.tencentcloudapi.ims.v20201229.models.ImageModerationResponse;
import com.tencentcloudapi.tms.v20201229.models.TextModerationResponse;
import com.tencentcloudapi.vm.v20210922.models.CreateVideoModerationTaskResponse;
import com.tencentcloudapi.vm.v20210922.models.DescribeTaskDetailResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.DynamicContentSecurityMapper;
import com.jeefast.cat.domain.DynamicContentSecurity;
import com.jeefast.cat.service.IDynamicContentSecurityService;
import com.baomidou.dynamic.datasource.annotation.DS;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 动态内容安全审核记录 服务层实现
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Service
@Slf4j
//@DS("slave")去掉多数据源
public class DynamicContentSecurityServiceImpl extends ServiceImpl<DynamicContentSecurityMapper, DynamicContentSecurity> implements IDynamicContentSecurityService {

    @Autowired
    private CloudTencentSCMFileUtil cloudTencentSCMFileUtil;
    @Autowired
    private IDynamicInfoBackstageService dynamicInfoBackstageService;
    @Autowired
    private RedisUtil redisUtil;


    /**
     * 文字内容安全
     *
     * @param type    1 标题 2 内容
     * @param content
     * @return DynamicContentSecurity 返回需对对应业务动态内容id做关联并持久化
     */
    @Override
    public DynamicContentSecurity textModeration(String type, String content) {

        String textSecurityBizType = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + "text_security_biz_type"+type);
        DynamicContentSecurity dynamicContentSecurity = new DynamicContentSecurity();
        dynamicContentSecurity.setId(UUID.randomUUID().toString().replace("-", ""));
        TextModerationResponse textModerationResponse = cloudTencentSCMFileUtil.textModeration(content, dynamicContentSecurity.getId(), textSecurityBizType);
        dynamicContentSecurity.setContent(content);
        dynamicContentSecurity.setType(type);
        dynamicContentSecurity.setBizType(textSecurityBizType);
        if (textModerationResponse == null) {
            dynamicContentSecurity.setStatus("ERROR");
            dynamicContentSecurity.setSuggestion("Review");
            dynamicContentSecurity.setAuditStatus(AuditEnum.REJECT.getCode());
        } else {
            dynamicContentSecurity.setStatus("FINISH");
            dynamicContentSecurity.setSuggestion(textModerationResponse.getSuggestion());
            dynamicContentSecurity.setLabels(textModerationResponse.getLabel());
            dynamicContentSecurity.setKeywords(Arrays.toString(textModerationResponse.getKeywords()));
            dynamicContentSecurity.setScore(textModerationResponse.getScore());
            dynamicContentSecurity.setRespContent(TextModerationResponse.toJsonString(textModerationResponse));
            dynamicContentSecurity.setAuditStatus(textModerationResponse.getSuggestion().equals("Pass") ? AuditEnum.PASS.getCode() : AuditEnum.REJECT.getCode());
        }
        return dynamicContentSecurity;
    }

    /**
     * 图片内容安全
     *
     * @param imgUrl
     * @return DynamicContentSecurity 返回需对对应业务动态内容id 和 relateId做关联并持久化
     */
    @Override
    public DynamicContentSecurity imgModeration(String imgUrl) {
        String imgSecurityBizType = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + "img_security_biz_type");
        DynamicContentSecurity dynamicContentSecurity = new DynamicContentSecurity();
        dynamicContentSecurity.setId(UUID.randomUUID().toString().replace("-", ""));
        ImageModerationResponse imageModerationResponse = cloudTencentSCMFileUtil.imgModeration(imgUrl, dynamicContentSecurity.getId(), imgSecurityBizType);
        dynamicContentSecurity.setRelateUrl(imgUrl);
        dynamicContentSecurity.setType("3");
        dynamicContentSecurity.setBizType(imgSecurityBizType);
        if (imageModerationResponse == null) {
            dynamicContentSecurity.setStatus("ERROR");
            dynamicContentSecurity.setSuggestion("Review");
            dynamicContentSecurity.setAuditStatus(AuditEnum.REJECT.getCode());
        } else {
            dynamicContentSecurity.setStatus("FINISH");
            dynamicContentSecurity.setSuggestion(imageModerationResponse.getSuggestion());
            dynamicContentSecurity.setLabels(imageModerationResponse.getLabel());
            dynamicContentSecurity.setKeywords(imageModerationResponse.getExtra());
            dynamicContentSecurity.setScore(imageModerationResponse.getScore());
            dynamicContentSecurity.setRespContent(ImageModerationResponse.toJsonString(imageModerationResponse));
            dynamicContentSecurity.setAuditStatus(imageModerationResponse.getSuggestion().equals("Pass") ? AuditEnum.PASS.getCode() : AuditEnum.REJECT.getCode());
        }
        return dynamicContentSecurity;
    }


    /**
     * 视频内容安全
     *
     * @param videoUrl
     * @return DynamicContentSecurity 返回需对对应业务动态内容id 和 relateId做关联并持久化
     */
    @Override
    public DynamicContentSecurity videoModerationCreate(String videoUrl) {
        String videoSecurityBizType = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + "video_security_biz_type");
        DynamicContentSecurity dynamicContentSecurity = new DynamicContentSecurity();
        dynamicContentSecurity.setId(UUID.randomUUID().toString().replace("-", ""));
        CreateVideoModerationTaskResponse videoModerationTaskResponse = cloudTencentSCMFileUtil.videoModerationCreate(videoUrl, dynamicContentSecurity.getId(), videoSecurityBizType);
        dynamicContentSecurity.setRelateUrl(videoUrl);
        dynamicContentSecurity.setType("4");
        dynamicContentSecurity.setBizType(videoSecurityBizType);
        if (videoModerationTaskResponse == null) {
            dynamicContentSecurity.setStatus("ERROR");
            dynamicContentSecurity.setSuggestion("Review");
            dynamicContentSecurity.setAuditStatus(AuditEnum.REJECT.getCode());
        } else {
            if (!videoModerationTaskResponse.getResults()[0].getCode().equals("OK")) {
                dynamicContentSecurity.setStatus("ERROR");
                dynamicContentSecurity.setSuggestion("Review");
                dynamicContentSecurity.setAuditStatus(AuditEnum.WAIT_AUDIT.getCode());
            } else {
                dynamicContentSecurity.setStatus("RUNNING");
                dynamicContentSecurity.setTaskId(videoModerationTaskResponse.getResults()[0].getTaskId());
                dynamicContentSecurity.setAuditStatus(AuditEnum.WAIT_AUDIT.getCode());
            }
            dynamicContentSecurity.setRespContent(CreateVideoModerationTaskResponse.toJsonString(videoModerationTaskResponse));
        }
        return dynamicContentSecurity;
    }


    /**
     * 视频内容安全查询 任务 未完成的任务查询检查结果情况
     */
    @Override
    public void videoModerationQueryJob() {
        List<DynamicContentSecurity> noFinishVideoTaskList = list(new LambdaQueryWrapper<DynamicContentSecurity>().
                eq(DynamicContentSecurity::getType, "4")
                .in(DynamicContentSecurity::getStatus, "PENDING", "RUNNING"));
        log.info("视频内容安全任务未完成的视频审核任务数量为:{}", noFinishVideoTaskList.size());
        for (DynamicContentSecurity dynamicContentSecurity : noFinishVideoTaskList) {
            DescribeTaskDetailResponse describeTaskDetailResponse = cloudTencentSCMFileUtil.videoModerationQuery(dynamicContentSecurity.getTaskId());
            if (describeTaskDetailResponse == null) {
                dynamicContentSecurity.setStatus("ERROR");
                dynamicContentSecurity.setSuggestion("Review");
                dynamicContentSecurity.setAuditStatus(AuditEnum.REJECT.getCode());
                dynamicInfoBackstageService.update(new LambdaUpdateWrapper<DynamicInfoBackstage>()
                        .set(DynamicInfoBackstage::getAuditStatus, AuditEnum.TO_RE_AUDIT.getCode())
                        .eq(DynamicInfoBackstage::getDynamicId, dynamicContentSecurity.getDynamicId()));
            } else {
                dynamicContentSecurity.setStatus(describeTaskDetailResponse.getStatus());
                dynamicContentSecurity.setSuggestion(describeTaskDetailResponse.getSuggestion());
                dynamicContentSecurity.setLabels(describeTaskDetailResponse.getLabel());
                describeTaskDetailResponse.setMediaInfo(null);
                describeTaskDetailResponse.setAudioText(null);
                describeTaskDetailResponse.setImageSegments(null);
                describeTaskDetailResponse.setSegmentCosUrlList(null);
                dynamicContentSecurity.setRespContent(DescribeTaskDetailResponse.toJsonString(describeTaskDetailResponse));
                if (describeTaskDetailResponse.getStatus().equals("FINISH")) {
                    boolean pass = describeTaskDetailResponse.getSuggestion().equals("Pass");
                    dynamicContentSecurity.setAuditStatus(pass ? AuditEnum.PASS.getCode() : AuditEnum.REJECT.getCode());
                    if (!pass) {
                        dynamicInfoBackstageService.update(new LambdaUpdateWrapper<DynamicInfoBackstage>()
                                .set(DynamicInfoBackstage::getAuditStatus, AuditEnum.TO_RE_AUDIT.getCode())
                                .eq(DynamicInfoBackstage::getDynamicId, dynamicContentSecurity.getDynamicId()));
                    }else {
                        dynamicInfoBackstageService.update(new LambdaUpdateWrapper<DynamicInfoBackstage>()
                                .set(DynamicInfoBackstage::getAuditStatus, AuditEnum.PASS.getCode())
                                .eq(DynamicInfoBackstage::getAuditStatus, AuditEnum.WAIT_AUDIT.getCode())
                                .eq(DynamicInfoBackstage::getDynamicId, dynamicContentSecurity.getDynamicId()));
                    }
                } else if (describeTaskDetailResponse.getStatus().equals("ERROR")
                        || describeTaskDetailResponse.getStatus().equals("CANCELLED")) {
                    dynamicContentSecurity.setStatus("ERROR");
                    dynamicContentSecurity.setSuggestion("Review");
                    dynamicContentSecurity.setAuditStatus(AuditEnum.REJECT.getCode());
                    dynamicInfoBackstageService.update(new LambdaUpdateWrapper<DynamicInfoBackstage>()
                            .set(DynamicInfoBackstage::getAuditStatus, AuditEnum.TO_RE_AUDIT.getCode())
                            .eq(DynamicInfoBackstage::getDynamicId, dynamicContentSecurity.getDynamicId()));
                }
            }
        }
        if (CollectionUtil.isNotEmpty(noFinishVideoTaskList)) {
            updateBatchById(noFinishVideoTaskList);
        }
    }
}
package com.jeefast.cat.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.CamelCaseMap;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.attention.service.IAttentionInfoService;
import com.cat.modules.common.entity.MediaInfo;
import com.cat.modules.content.entity.ContentTopicRel;
import com.cat.modules.content.service.IContentTopicRelService;
import com.cat.modules.dynamic.entity.DynamicMedia;
import com.cat.modules.dynamic.mapper.DynamicMediaMapper;
import com.cat.modules.dynamic.resp.DynamicInfoRespVo;
import com.cat.modules.dynamic.service.IDynamicMediaService;
import com.cat.modules.notice.entity.UserPushNotice;
import com.cat.modules.notice.service.IUserPushNoticeService;
import com.cat.modules.sys.service.ISysMessageInfoService;
import com.cat.modules.topic.service.ITopicInfoService;
import com.cat.modules.user.service.UserService;
import com.cat.util.BlankUtil;
import com.google.common.collect.Lists;
import com.jeefast.cat.domain.CommentInfoBackstage;
import com.jeefast.cat.domain.DynamicContentSecurity;
import com.jeefast.cat.domain.TopicInfoBackstage;
import com.jeefast.cat.domain.UserInfo;
import com.jeefast.cat.req.AddDynamicReq;
import com.jeefast.cat.req.EditDynamicReq;
import com.jeefast.cat.req.NewAddOrEditDynamicReq;
import com.jeefast.cat.service.ICommentInfoBackstageService;
import com.jeefast.cat.service.IDynamicContentSecurityAsyncService;
import com.jeefast.cat.service.IDynamicContentSecurityService;
import com.jeefast.cat.service.ITopicInfoBackstageService;
import com.jeefast.cat.service.IUserInfoService;
import com.jeefast.common.enums.AuditEnum;
import com.jeefast.common.enums.NoticeTypeEnum;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.enums.YesNoEnum;
import com.jeefast.common.exception.BusinessException;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.file.UploadCloudFileUtil;
import com.jeefast.framework.util.ShiroUtils;
import com.jeefast.system.domain.SysRole;
import com.jeefast.system.domain.SysUser;
import com.jeefast.system.domain.SysConfig;
import com.jeefast.system.service.ISysConfigService;
import com.jeefast.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.DynamicInfoBackstageMapper;
import com.jeefast.cat.domain.DynamicInfoBackstage;
import com.jeefast.cat.service.IDynamicInfoBackstageService;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态内容 服务层实现
 *
 * <AUTHOR>
 * @date 2020-08-08
 */
@Service
@Slf4j
//@DS("slave")去掉多数据源
public class DynamicInfoBackstageServiceImpl extends ServiceImpl<DynamicInfoBackstageMapper, DynamicInfoBackstage> implements IDynamicInfoBackstageService {

    @Autowired
    private DynamicInfoBackstageMapper dynamicInfoMapper;
    @Autowired
    private ICommentInfoBackstageService commentInfoService;
    @Autowired
    private IContentTopicRelService contentTopicRelService;
    @Autowired
    private ITopicInfoBackstageService topicInfoBackstageService;
    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private IDynamicMediaService dynamicMediaService;
    @Autowired
    private UploadCloudFileUtil uploadCloudFileUtil;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISysMessageInfoService sysMessageInfoService;
    @Autowired
    private DynamicMediaMapper dynamicMediaMapper;
    @Autowired
    private HotSearchServiceImpl hotSearchService;
    @Autowired
    private ISysDictDataService sysDictDataService;
    @Autowired
    private IDynamicContentSecurityAsyncService dynamicContentSecurityAsyncService;
    @Autowired
    private IDynamicContentSecurityService dynamicContentSecurityService;
    @Autowired
    private ISysConfigService configService;

    @Value("${yueyue.live.finderUserName:sphVAD7Dg5oCNkg}")
    private String finderUserName;

    @Value("${yueyue.live.originMiniId:gh_6471feec127d}")
    private String originMiniId;

    @Autowired
    private IAttentionInfoService attentionInfoService;
    @Autowired
    private UserService userService;

    private final static String RANDOM_ARTICLE_CONTENT = "random.article.content";

    @Autowired
    private ITopicInfoService topicInfoService;
    @Autowired
    private IUserPushNoticeService userPushNoticeService;


    @Override
    public List<CamelCaseMap<String, Object>> dynamicPinnedList(QueryWrapper<DynamicInfoBackstage> queryWrapper) {
        List<CamelCaseMap<String, Object>> camelCaseMaps = dynamicInfoMapper.dynamicList(queryWrapper);
        for (CamelCaseMap<String, Object> camelCaseMap : camelCaseMaps) {
            camelCaseMap.put("isPinned", true);
        }
        return camelCaseMaps;
    }

    @Override
    public List<CamelCaseMap<String, Object>> dynamicList(QueryWrapper<DynamicInfoBackstage> queryWrapper) {
        return dynamicInfoMapper.dynamicList(queryWrapper);
    }

    @Override
    @Transactional
    public boolean delete(List<String> asList) {
        boolean flag = false;
        //文件上传方式(1=本地、2=腾讯云)
        String uploadType = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + "sys.cat.upload.type");
        for (String id : asList) {
            DynamicInfoBackstage dynamicInfo = this.getById(id);
            flag = this.removeById(id);
            commentInfoService.remove(new UpdateWrapper<CommentInfoBackstage>()
                    .eq("business_id", id).eq("business_type", "1")
            );

            //未删除情况下才减少动态数
            if ("0".equals(dynamicInfo.getIsDelete())) {
                //先查出待减少的话题id
                List<ContentTopicRel> topincRelList = contentTopicRelService.list(new QueryWrapper<ContentTopicRel>()
                        .select("topic_id")
                        .eq("business_id", id)
                        .eq("business_type", "1")
                );
                contentTopicRelService.remove(new UpdateWrapper<ContentTopicRel>()
                        .eq("business_id", id)
                        .eq("business_type", "1")
                );
                if (flag) {
                    for (ContentTopicRel topicRel : topincRelList) {
                        //减少话题关联数
                        topicInfoBackstageService.update(new UpdateWrapper<TopicInfoBackstage>()
                                .setSql("cite_count=cite_count-1")
                                .eq("topic_id", topicRel.getTopicId())
                        );
                    }
                }

                userInfoService.update(new UpdateWrapper<UserInfo>()
                        .eq("user_id", dynamicInfo.getUserId())
                        .setSql("dynamic_count=dynamic_count-1")
                );
                //文件上传方式(1=本地、2=腾讯云)
                if ("2".equals(uploadType)) {
                    //查询动态文件列表
                    List<DynamicMedia> dynamicMedia = dynamicMediaService.list(new QueryWrapper<DynamicMedia>()
                            .eq("dynamic_id", id)
                    );
                    //删除云上文件
                    dynamicMedia.forEach(item -> {
                        try {
                            uploadCloudFileUtil.delFile(item.getMediaUrl());
                            uploadCloudFileUtil.delFile(item.getSmallUrl());
                        } catch (Exception e) {
                            log.error("删除云上文件失败：", e);
                        }
                    });
                }
                //删除媒体记录
                dynamicMediaService.remove(new UpdateWrapper<DynamicMedia>()
                        .eq("dynamic_id", id)
                );
                //删除内容安全记录
                dynamicContentSecurityService.remove(new UpdateWrapper<DynamicContentSecurity>()
                        .eq("dynamic_id", id)
                );
                hotSearchService.refreshTopTen(id);
                attentionInfoService.updateToUserNewDynamicNum(dynamicInfo.getUserId(), -1, dynamicInfo.getCreateDate());
                userService.updateFamilyUserNewDynamicNum(dynamicInfo.getUserId(), -1, dynamicInfo.getCreateDate());
            }
        }
        return flag;
    }

    @Override
    @Transactional
    public boolean addSave(AddDynamicReq req) {
        List<MediaInfo> dyMediaList = req.getDynamicMediaList();
        DynamicInfoBackstage dynamicInfo = new DynamicInfoBackstage();
        BeanUtil.copyProperties(req, dynamicInfo);
        //List<String> dynamicMediaList = req.getDynamicMediaList();
        //动态媒体数
        log.info("====dyMediaList:{}======", JSON.toJSONString(dyMediaList));
        if (dyMediaList != null && dyMediaList.size() > 0) {
            dynamicInfo.setMediaCount(dyMediaList.size());
            //动态类型（1：文字、2：图文、3：视频）
            if ("2".equals(req.getType()) || "4".equals(req.getType())) {
                //下载压缩图片获取长宽高赋值
                String coverImage = dyMediaList.get(0).getSmall();
                dynamicInfo.setCoverImage(coverImage);
                dynamicInfo.setHeight(dyMediaList.get(0).getWidth());
                dynamicInfo.setWidth(dyMediaList.get(0).getHeight());
                //插入动态信息
                this.save(dynamicInfo);
                for (int i = 0; i < dyMediaList.size(); i++) {
                    DynamicMedia dynamicMedia = new DynamicMedia();
                    dynamicMedia.setMediaUrl(dyMediaList.get(i).getUrl());
                    dynamicMedia.setSmallUrl(dyMediaList.get(i).getSmall());
                    dynamicMedia.setPetId(req.getPetId());
                    dynamicMedia.setDynamicId(dynamicInfo.getDynamicId());
                    dynamicMedia.setWidth(dyMediaList.get(i).getWidth());
                    dynamicMedia.setHeight(dyMediaList.get(i).getHeight());
                    dynamicMedia.setSortNo(i + 1);
                    dynamicMedia.setType("1");
                    dynamicMediaService.save(dynamicMedia);
                }
            } else if ("3".equals(req.getType())) {
                //下载视频取封面图设置
                String coverImage = dyMediaList.get(0).getSmall();
                dynamicInfo.setCoverImage(coverImage);
                dynamicInfo.setHeight(0);
                dynamicInfo.setWidth(0);
                //插入动态信息
                this.save(dynamicInfo);
                DynamicMedia dynamicMedia = new DynamicMedia();
                dynamicMedia.setMediaUrl(dyMediaList.get(0).getUrl());
                dynamicMedia.setSmallUrl(dyMediaList.get(0).getSmall());
                dynamicMedia.setPetId(req.getPetId());
                dynamicMedia.setDynamicId(dynamicInfo.getDynamicId());
                dynamicMedia.setWidth(dyMediaList.get(0).getWidth());
                dynamicMedia.setHeight(dyMediaList.get(0).getHeight());
                dynamicMedia.setSortNo(1);
                dynamicMedia.setType("2");
                dynamicMediaService.save(dynamicMedia);
            }
        } else {
            this.save(dynamicInfo);
        }
        //修改动态数
        userInfoService.update(new UpdateWrapper<UserInfo>()
                .eq("user_id", dynamicInfo.getUserId())
                .setSql("dynamic_count=dynamic_count+1")
        );
        attentionInfoService.updateToUserNewDynamicNum(dynamicInfo.getUserId(), 1, null);
        userService.updateFamilyUserNewDynamicNum(dynamicInfo.getUserId(), 1, null);
        //增加钩子处理随机文章调整
        String result = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT);
        if (StringUtils.isNotEmpty(result)) {
            List<DynamicInfoRespVo> dynamicInfoRespVos = JSON.parseArray(result, DynamicInfoRespVo.class);
            DynamicInfoRespVo item = new DynamicInfoRespVo();
            item.setContent(req.getContent());
            item.setTitle(req.getTitle());
            item.setArticleType(req.getArticleType());
            item.setCoverImage(req.getCoverImage());
            item.setSource(req.getSource());
            dynamicInfoRespVos.add(item);
            //重新放置redis中
            redisUtil.del(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT);
            redisUtil.set(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT, JSON.toJSONString(dynamicInfoRespVos));
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean newAddSave(NewAddOrEditDynamicReq req) {
        //先校验 标题和内容的 内容安全

        List<String> addDynamicMediaId = Lists.newArrayList();

        SysUser currUser = ShiroUtils.getSysUser();
        Boolean noNeedSecurity = false;
        if (req.getIsCheck().equals(YesNoEnum.YES.getCode())) {
            //不是草稿才需要校验
            for (SysRole role : currUser.getRoles()) {
                noNeedSecurity = sysDictDataService.dictValueInDictType("cat_dynamic_security_no_need", role.getRoleKey());
                if (noNeedSecurity) {
                    break;
                }
            }
        } else {
            noNeedSecurity = true;
        }

        List<DynamicMedia> dyMediaList = req.getDynamicMediaList();
        DynamicInfoBackstage dynamicInfo = new DynamicInfoBackstage();
        BeanUtil.copyProperties(req, dynamicInfo);
        dynamicInfo.setSysUserId(currUser.getUserId());
        dynamicInfo.setNeedSecurity(noNeedSecurity?YesNoEnum.NO.getCode():YesNoEnum.YES.getCode());
        if (req.getReleaseType().equals(YesNoEnum.YES.getCode())) {
            dynamicInfo.setReleaseTime(new Date());
        }
        //List<String> dynamicMediaList = req.getDynamicMediaList();
        //动态媒体数
        log.info("====dyMediaList:{}======", JSON.toJSONString(dyMediaList));
        if (dyMediaList != null && dyMediaList.size() > 0) {
            dynamicInfo.setMediaCount(dyMediaList.size());
            //动态类型（1：文字、2：图文、3：视频）
            if ("2".equals(req.getType())) {
                //下载压缩图片获取长宽高赋值
                String coverImage = dyMediaList.get(0).getSmallUrl();
                dynamicInfo.setCoverImage(coverImage);
                dynamicInfo.setHeight(dyMediaList.get(0).getWidth());
                dynamicInfo.setWidth(dyMediaList.get(0).getHeight());
                //插入动态信息
                this.save(dynamicInfo);
                for (int i = 0; i < dyMediaList.size(); i++) {
                    DynamicMedia dynamicMedia = new DynamicMedia();
                    dynamicMedia.setMediaUrl(dyMediaList.get(i).getMediaUrl());
                    dynamicMedia.setSmallUrl(dyMediaList.get(i).getSmallUrl());
                    dynamicMedia.setPetId(req.getPetId());
                    dynamicMedia.setDynamicId(dynamicInfo.getDynamicId());
                    dynamicMedia.setWidth(dyMediaList.get(i).getWidth());
                    dynamicMedia.setHeight(dyMediaList.get(i).getHeight());
                    dynamicMedia.setSortNo(i + 1);
                    dynamicMedia.setType("1");
                    dynamicMedia.setExt(dyMediaList.get(i).getExt());
                    dynamicMediaService.save(dynamicMedia);
                    if (!noNeedSecurity) {
                        addDynamicMediaId.add(dynamicMedia.getDynamicMediaId());
                    }
                }
            } else if ("3".equals(req.getType())) {
                //下载视频取封面图设置
                if (StringUtils.isBlank(dynamicInfo.getCoverImage())){
                    String coverImage = dyMediaList.get(0).getSmallUrl();
                    dynamicInfo.setCoverImage(coverImage);
                }
                dynamicInfo.setHeight(0);
                dynamicInfo.setWidth(0);
                //插入动态信息
                this.save(dynamicInfo);
                DynamicMedia dynamicMedia = new DynamicMedia();
                dynamicMedia.setMediaUrl(dyMediaList.get(0).getMediaUrl());
                dynamicMedia.setFileId(dynamicMedia.getMediaUrl().split("fileId=")[1]);
                dynamicMedia.setSmallUrl(dyMediaList.get(0).getSmallUrl());
                dynamicMedia.setPetId(req.getPetId());
                dynamicMedia.setDynamicId(dynamicInfo.getDynamicId());
                dynamicMedia.setWidth(dyMediaList.get(0).getWidth());
                dynamicMedia.setHeight(dyMediaList.get(0).getHeight());
                dynamicMedia.setSortNo(1);
                dynamicMedia.setType("2");
                dynamicMedia.setExt(dyMediaList.get(0).getExt());
                dynamicMediaService.save(dynamicMedia);

                if (!noNeedSecurity) {
                    addDynamicMediaId.add(dynamicMedia.getDynamicMediaId());
                }
            }
        } else {
            this.save(dynamicInfo);
        }
        updateById(dynamicInfo);
        dynamicContentSecurityAsyncService.syncDynamicContentSecurityCheck(dynamicInfo,null,null,
                addDynamicMediaId,null);
        //话题引用数累加and记录动态关联话题
        if (BlankUtil.isNotEmpty(req.getTopic())) {
            List<String> topics = req.getTopic();
            topicInfoService.addTopicRel(dynamicInfo.getDynamicId(), "1", topics);
        }
        //修改动态数
        userInfoService.update(new UpdateWrapper<UserInfo>()
                .eq("user_id", dynamicInfo.getUserId())
                .setSql("dynamic_count=dynamic_count+1")
        );
        attentionInfoService.updateToUserNewDynamicNum(dynamicInfo.getUserId(), 1, null);
        userService.updateFamilyUserNewDynamicNum(dynamicInfo.getUserId(), 1, null);
        //增加钩子处理随机文章调整
        String result = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT);
        if (StringUtils.isNotEmpty(result)) {
            List<DynamicInfoRespVo> dynamicInfoRespVos = JSON.parseArray(result, DynamicInfoRespVo.class);
            DynamicInfoRespVo item = new DynamicInfoRespVo();
            item.setContent(req.getContent());
            item.setTitle(req.getTitle());
            item.setArticleType(req.getArticleType());
            item.setCoverImage(req.getCoverImage());
            item.setSource(req.getSource());
            dynamicInfoRespVos.add(item);
            //重新放置redis中
            redisUtil.del(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT);
            redisUtil.set(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT, JSON.toJSONString(dynamicInfoRespVos));
        }
        return true;
    }

    @Override
    public boolean reasonsSub(String dynamicId, String reasons) {
        DynamicInfoBackstage dynamicInfo = this.getById(dynamicId);
        boolean flag = this.update(new UpdateWrapper<DynamicInfoBackstage>()
                .set("is_check", "0")
                .set("audit_status", AuditEnum.REJECT.getCode())
                .set("reasons", reasons)
                .eq("dynamic_id", dynamicId)
        );
        if (flag) {
            //发送驳回通知
            sysMessageInfoService.endMessage("000000000", "驳回：" + reasons, dynamicId, "1", "1");
            UserPushNotice notice = new UserPushNotice()
                    .setId(IdUtil.fastSimpleUUID())
                    .setUserId("000000000")
                    .setToUserId(dynamicInfo.getUserId())
                    .setType(NoticeTypeEnum.DYNAMIC_FAIL.getCode())
                    .setObjectId(dynamicInfo.getDynamicId())
                    .setPushTitle(dynamicInfo.getTitle())
                    .setPushDesc(reasons)
                    .setExtraStr(null)
                    .setIsSend("0")
                    .setIsRead("0")
                    .setIsDelete("0")
                    .setSendCount(0);
            userPushNoticeService.save(notice);
        }
        return flag;
    }

    @Override
    public boolean passDynamicId(String dynamicId, String reasons) {
//        DynamicInfoBackstage dynamicInfo = this.getById(dynamicId);
        boolean flag = this.update(new UpdateWrapper<DynamicInfoBackstage>()
                .set("audit_status", AuditEnum.PASS.getCode())
                .set("reasons", reasons)
                .eq("dynamic_id", dynamicId)
        );
        return flag;
    }


    @Override
    public boolean pinned(String dynamicId) {
        return update(new LambdaUpdateWrapper<DynamicInfoBackstage>()
                .set(DynamicInfoBackstage::getIsTop, YesNoEnum.YES.getCode())
                .in(DynamicInfoBackstage::getDynamicId, dynamicId.split(",")));
    }

    @Override
    public boolean unPinned(String dynamicId) {
        return update(new LambdaUpdateWrapper<DynamicInfoBackstage>()
                .set(DynamicInfoBackstage::getIsTop, YesNoEnum.NO.getCode())
                .in(DynamicInfoBackstage::getDynamicId, dynamicId.split(",")));
    }

    @Override
    @Transactional
    public boolean updateInfo(EditDynamicReq req) {
        DynamicInfoBackstage dbDynamic = getById(req.getDynamicId());
        DynamicInfoBackstage dynamicInfoBackstage = new DynamicInfoBackstage();
        BeanUtil.copyProperties(req, dynamicInfoBackstage);
        if (CollectionUtil.isNotEmpty(req.getDynamicMediaList())) {
            dynamicInfoBackstage.setCoverImage(req.getDynamicMediaList().get(0));
        }
        dynamicInfoMapper.updateById(dynamicInfoBackstage);
        //查询现有的
        List<DynamicMedia> dynamicMediaTempList = dynamicMediaMapper.selectList(new LambdaQueryWrapper<DynamicMedia>()
                .eq(DynamicMedia::getDynamicId, dynamicInfoBackstage.getDynamicId()));
        //删除不要的
        List<String> mediaUrlList = dynamicMediaTempList.stream().map(DynamicMedia::getMediaUrl).collect(Collectors.toList());
        Set<String> setA = new HashSet<>(mediaUrlList);
        Set<String> setB = new HashSet<>(req.getDynamicMediaList());
        Set<String> tempList = new HashSet<>(setA);
        // Calculate A - B
        tempList.removeAll(setB);
        Set<String> tempSet = new HashSet<>(setB);
        tempSet.removeAll(setA);
        tempList.addAll(tempSet);
        List<MediaInfo> mediaInfos = JSONObject.parseArray(req.getMediaInfoList(), MediaInfo.class);
        if (CollectionUtil.isNotEmpty(tempList)) {
            int delete = dynamicMediaMapper.delete(new LambdaUpdateWrapper<DynamicMedia>()
                    .eq(DynamicMedia::getDynamicId, dynamicInfoBackstage.getDynamicId())
                    .in(DynamicMedia::getMediaUrl, tempList));
            log.info("删除数量：{}", delete);
        }
        if (CollectionUtil.isNotEmpty(mediaInfos)) {
            try {
                //保存最新的
                if ("2".equals(req.getType()) || "4".equals(req.getType())) {
                    for (int i = 0; i < mediaInfos.size(); i++) {
                        DynamicMedia dynamicMedia = new DynamicMedia();
                        dynamicMedia.setMediaUrl(mediaInfos.get(i).getUrl());
                        dynamicMedia.setSmallUrl(mediaInfos.get(i).getSmall());
                        dynamicMedia.setPetId(req.getPetId());
                        dynamicMedia.setDynamicId(dynamicInfoBackstage.getDynamicId());
                        dynamicMedia.setWidth(mediaInfos.get(i).getWidth());
                        dynamicMedia.setHeight(mediaInfos.get(i).getHeight());
                        dynamicMedia.setSortNo(CollectionUtil.isNotEmpty(dynamicMediaTempList) ? dynamicMediaTempList.stream().findFirst().get().getSortNo() + 1 : 0);
                        dynamicMedia.setType("1");
                        dynamicMediaService.save(dynamicMedia);
                    }
                }
            } catch (Exception e) {
                log.info(e.getMessage());
                return false;
            }
        }
        //设置排序
        List<DynamicMedia> dynamicMediaList = dynamicMediaMapper.selectList(new LambdaQueryWrapper<DynamicMedia>()
                .eq(DynamicMedia::getDynamicId, dynamicInfoBackstage.getDynamicId()));
        if (CollectionUtil.isNotEmpty(dynamicMediaList) && CollectionUtil.isNotEmpty(req.getDynamicMediaList())) {
            for (int i = 0; i < req.getDynamicMediaList().size(); i++) {
                for (DynamicMedia dynamicMedia : dynamicMediaList) {
                    if (dynamicMedia.getMediaUrl().equals(req.getDynamicMediaList().get(i))) {
                        DynamicMedia updateSort = new DynamicMedia();
                        updateSort.setSortNo(i);
                        dynamicMediaMapper.update(updateSort, new LambdaUpdateWrapper<DynamicMedia>()
                                .eq(DynamicMedia::getDynamicMediaId, dynamicMedia.getDynamicMediaId()));
                    }
                }
            }
        }
        if (!dbDynamic.getUserId().equals(dynamicInfoBackstage.getUserId())) {
            //修改动态数
            userInfoService.update(new UpdateWrapper<UserInfo>()
                    .eq("user_id", dbDynamic.getUserId())
                    .setSql("dynamic_count=dynamic_count-1")
            );
            userInfoService.update(new UpdateWrapper<UserInfo>()
                    .eq("user_id", dynamicInfoBackstage.getUserId())
                    .setSql("dynamic_count=dynamic_count+1")
            );
            attentionInfoService.updateToUserNewDynamicNum(dbDynamic.getUserId(), -1, dbDynamic.getCreateDate());
            attentionInfoService.updateToUserNewDynamicNum(dynamicInfoBackstage.getUserId(), 1, null);
            userService.updateFamilyUserNewDynamicNum(dbDynamic.getUserId(), -1, dbDynamic.getCreateDate());
            userService.updateFamilyUserNewDynamicNum(dynamicInfoBackstage.getUserId(), 1, null);
        }
        //增加钩子处理随机文章修改
        String result = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT);
        if (StringUtils.isNotEmpty(result)) {
            List<DynamicInfoRespVo> dynamicInfoRespVos = JSON.parseArray(result, DynamicInfoRespVo.class);
            dynamicInfoRespVos.forEach(item -> {
                if (item.getDynamicId().equals(req.getDynamicId())) {
                    item.setContent(req.getContent());
                    item.setTitle(req.getTitle());
                    item.setArticleType(req.getArticleType());
                    item.setCoverImage(req.getCoverImage());
                    item.setSource(req.getSource());
                }
            });
            //重新放置redis中
            redisUtil.del(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT);
            redisUtil.set(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT, JSON.toJSONString(dynamicInfoRespVos));
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean newUpdateInfo(NewAddOrEditDynamicReq req) {

        // 2. 添加参数校验
        if (req == null || req.getDynamicId() == null) {
            throw new BusinessException("参数不能为空");
        }
        DynamicInfoBackstage dbDynamic = getById(req.getDynamicId());
        SysUser currUser = ShiroUtils.getSysUser();
        Boolean noNeedSecurity = false;
        if (req.getIsCheck().equals(YesNoEnum.YES.getCode())) {
            //不是草稿才需要校验
            for (SysRole role : currUser.getRoles()) {
                noNeedSecurity = sysDictDataService.dictValueInDictType("cat_dynamic_security_no_need", role.getRoleKey());
                if (noNeedSecurity) {
                    break;
                }
            }
        } else {
            noNeedSecurity = true;
        }
        DynamicInfoBackstage dbDynamicInfoBackstage = getById(req.getDynamicId());
        List<String> addDynamicMediaId = Lists.newArrayList();
        List<String> delDynamicMediaId = Lists.newArrayList();

        DynamicInfoBackstage dynamicInfoBackstage = new DynamicInfoBackstage();
        BeanUtil.copyProperties(req, dynamicInfoBackstage);
        dynamicInfoBackstage.setSysUserId(currUser.getUserId());
        dynamicInfoBackstage.setNeedSecurity(noNeedSecurity?YesNoEnum.NO.getCode():YesNoEnum.YES.getCode());
        if (req.getReleaseType().equals(YesNoEnum.YES.getCode()) && ObjectUtil.isNull(dynamicInfoBackstage.getReleaseTime())) {
            dynamicInfoBackstage.setReleaseTime(new Date());
        }
        //查询现有的
        List<DynamicMedia> dbDynamicMediaList = dynamicMediaMapper.selectList(new LambdaQueryWrapper<DynamicMedia>()
                .eq(DynamicMedia::getDynamicId, dynamicInfoBackstage.getDynamicId()));
        // dynamicMediaTempList 获取 mediaUrl 和 dynamicMediaId  组成 map
        Map<String, String> dbMediaUrlMap = dbDynamicMediaList.stream().collect(Collectors.toMap(DynamicMedia::getMediaUrl, DynamicMedia::getDynamicMediaId));

        if (CollectionUtil.isNotEmpty(req.getDynamicMediaList())) {
            //动态类型（1：文字、2：图文、3：视频）
            if ("2".equals(req.getType())) {
                //下载压缩图片获取长宽高赋值
                String coverImage = req.getDynamicMediaList().get(0).getSmallUrl();
                dynamicInfoBackstage.setCoverImage(coverImage);
                dynamicInfoBackstage.setHeight(req.getDynamicMediaList().get(0).getWidth());
                dynamicInfoBackstage.setWidth(req.getDynamicMediaList().get(0).getHeight());
                for (int i = 0; i < req.getDynamicMediaList().size(); i++) {
                    if (dbMediaUrlMap.containsKey(req.getDynamicMediaList().get(i).getMediaUrl())) {
                        dynamicMediaService.update(new LambdaUpdateWrapper<DynamicMedia>()
                                        .set(DynamicMedia::getSortNo, i + 1)
                                        .eq(DynamicMedia::getDynamicMediaId, dbMediaUrlMap.get(req.getDynamicMediaList().get(i).getMediaUrl())));

                        //原本存在只修改下排序号
                        continue;
                    }
                    //不存在的 新增
                    DynamicMedia dynamicMedia = new DynamicMedia();
                    dynamicMedia.setMediaUrl(req.getDynamicMediaList().get(i).getMediaUrl());
                    dynamicMedia.setSmallUrl(req.getDynamicMediaList().get(i).getSmallUrl());
                    dynamicMedia.setPetId(req.getPetId());
                    dynamicMedia.setDynamicId(dynamicInfoBackstage.getDynamicId());
                    dynamicMedia.setWidth(req.getDynamicMediaList().get(i).getWidth());
                    dynamicMedia.setHeight(req.getDynamicMediaList().get(i).getHeight());
                    dynamicMedia.setSortNo(i + 1);
                    dynamicMedia.setType("1");
                    dynamicMedia.setExt(req.getDynamicMediaList().get(i).getExt());
                    dynamicMediaService.save(dynamicMedia);
                    if (!noNeedSecurity) {
                        addDynamicMediaId.add(dynamicMedia.getDynamicMediaId());
                    }
                }
                //还要删除原本dbDynamicMediaList中有 但是现在req.getDynamicMediaList()中没有的,同时删除对应安全审核记录
                List<String> reqMediaUrlList = req.getDynamicMediaList().stream()
                        .map(DynamicMedia::getMediaUrl).collect(Collectors.toList());
                List<DynamicMedia> toDelId = dbDynamicMediaList.stream().filter(e -> !reqMediaUrlList.contains(e.getMediaUrl())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(toDelId)) {
                    dynamicMediaService.removeByIds(toDelId.stream().map(DynamicMedia::getDynamicMediaId).collect(Collectors.toList()));
                }
            } else if ("3".equals(req.getType())) {
                if (!dbMediaUrlMap.containsKey(req.getDynamicMediaList().get(0).getMediaUrl())) {
                    //和原本的不一样才需要处理，一样不需要处理，视频只有一个
                    //先删除，再添加
                    dynamicMediaService.remove(new LambdaQueryWrapper<DynamicMedia>()
                            .eq(DynamicMedia::getDynamicId, dynamicInfoBackstage.getDynamicId())
                            .eq(DynamicMedia::getType, "2"));
                    delDynamicMediaId.add(dbMediaUrlMap.get(req.getDynamicMediaList().get(0).getDynamicId()));
                    if (StringUtils.isBlank(dynamicInfoBackstage.getCoverImage())){
                        String coverImage = req.getDynamicMediaList().get(0).getSmallUrl();
                        dynamicInfoBackstage.setCoverImage(coverImage);
                    }
                    dynamicInfoBackstage.setHeight(0);
                    dynamicInfoBackstage.setWidth(0);
                    DynamicMedia dynamicMedia = new DynamicMedia();
                    dynamicMedia.setMediaUrl(req.getDynamicMediaList().get(0).getMediaUrl());
                    dynamicMedia.setFileId(dynamicMedia.getMediaUrl().split("fileId=")[1]);
                    dynamicMedia.setSmallUrl(req.getDynamicMediaList().get(0).getSmallUrl());
                    dynamicMedia.setPetId(req.getPetId());
                    dynamicMedia.setDynamicId(dynamicInfoBackstage.getDynamicId());
                    dynamicMedia.setWidth(req.getDynamicMediaList().get(0).getWidth());
                    dynamicMedia.setHeight(req.getDynamicMediaList().get(0).getHeight());
                    dynamicMedia.setSortNo(1);
                    dynamicMedia.setType("2");
                    dynamicMedia.setExt(req.getDynamicMediaList().get(0).getExt());
                    dynamicMediaService.save(dynamicMedia);

                    if (!noNeedSecurity) {
                        addDynamicMediaId.add(dynamicMedia.getDynamicMediaId());
                    }
                }

            }
        }
        updateById(dynamicInfoBackstage);
        if (!noNeedSecurity && dbDynamicInfoBackstage.getIsCheck().equals(YesNoEnum.NO.getCode()) &&dynamicInfoBackstage.getIsCheck().equals(YesNoEnum.YES.getCode())){
            //需要审核  从草稿变成不是草稿
            List<DynamicMedia> currDynamicMediaList = dynamicMediaMapper.selectList(new LambdaQueryWrapper<DynamicMedia>()
                    .eq(DynamicMedia::getDynamicId, dynamicInfoBackstage.getDynamicId()));
            dynamicContentSecurityAsyncService.syncDynamicContentSecurityCheck(dynamicInfoBackstage,null,null,
                    currDynamicMediaList.stream().map(DynamicMedia::getDynamicMediaId).collect(Collectors.toList()), delDynamicMediaId);
        }else {
            dynamicContentSecurityAsyncService.syncDynamicContentSecurityCheck(dynamicInfoBackstage,dbDynamicInfoBackstage.getTitle(),dbDynamicInfoBackstage.getContent(),
                    addDynamicMediaId,delDynamicMediaId);
        }
        //话题引用数累加and记录动态关联话题
        if (BlankUtil.isNotEmpty(req.getTopic())) {
            List<String> topics = req.getTopic();
            //先删除文章绑定话题数据
            topicInfoService.delContentTopic(dynamicInfoBackstage.getDynamicId(), "1");
            topicInfoService.addTopicRel(dynamicInfoBackstage.getDynamicId(), "1", topics);
        }
        if (!dbDynamic.getUserId().equals(dynamicInfoBackstage.getUserId())) {
            //修改动态数
            userInfoService.update(new UpdateWrapper<UserInfo>()
                    .eq("user_id", dbDynamic.getUserId())
                    .setSql("dynamic_count=dynamic_count-1")
            );
            userInfoService.update(new UpdateWrapper<UserInfo>()
                    .eq("user_id", dynamicInfoBackstage.getUserId())
                    .setSql("dynamic_count=dynamic_count+1")
            );
            attentionInfoService.updateToUserNewDynamicNum(dbDynamic.getUserId(), -1, dbDynamic.getCreateDate());
            attentionInfoService.updateToUserNewDynamicNum(dynamicInfoBackstage.getUserId(), 1, null);
            userService.updateFamilyUserNewDynamicNum(dbDynamic.getUserId(), -1, dbDynamic.getCreateDate());
            userService.updateFamilyUserNewDynamicNum(dynamicInfoBackstage.getUserId(), 1, null);
        }

        //增加钩子处理随机文章修改
        String result = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT);
        if (StringUtils.isNotEmpty(result)) {
            List<DynamicInfoRespVo> dynamicInfoRespVos = JSON.parseArray(result, DynamicInfoRespVo.class);
            dynamicInfoRespVos.forEach(item -> {
                if (req.getDynamicId().equals(item.getDynamicId())) {
                    item.setContent(req.getContent());
                    item.setTitle(req.getTitle());
                    item.setArticleType(req.getArticleType());
                    item.setCoverImage(req.getCoverImage());
                    item.setSource(req.getSource());
                }
            });
            //重新放置redis中
            redisUtil.del(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT);
            redisUtil.set(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT, JSON.toJSONString(dynamicInfoRespVos));
        }
        return true;
    }

    /**
     * 动态增加虚拟浏览量
     *
     * @param regionStart 随机数起始值
     * @param regionEnd   随机数最大值
     */
    @Override
    public void autoAddVisualView(Integer regionStart, Integer regionEnd, Integer recentDays) {
        Random random = new Random();
        LocalDate endDay = LocalDate.now();
        LocalDate startDay = endDay.plusDays(-recentDays);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String startTime = startDay.format(formatter) + " 00:00:00";
        String endTime = endDay.format(formatter) + " 23:59:59";
        List<DynamicInfoBackstage> dataList = dynamicInfoMapper.selectList(new LambdaQueryWrapper<DynamicInfoBackstage>()
                .between(DynamicInfoBackstage::getCreateDate, startTime, endTime)
                .eq(DynamicInfoBackstage::getIsDelete, 0)
                .eq(DynamicInfoBackstage::getIsCheck, 1));
        dataList.forEach(itm -> {
            int randomNumber = random.nextInt((regionEnd - regionStart) + 1) + regionStart;
            DynamicInfoBackstage updateDO = new DynamicInfoBackstage();
            updateDO.setDynamicId(itm.getDynamicId());
            updateDO.setVisualViewCount(randomNumber + itm.getVisualViewCount());
            dynamicInfoMapper.updateById(updateDO);
        });
    }


}
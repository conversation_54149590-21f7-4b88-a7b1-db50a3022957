package com.jeefast.cat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.ExchangeGoodsBackstageMapper;
import com.jeefast.cat.domain.ExchangeGoodsBackstage;
import com.jeefast.cat.service.IExchangeGoodsBackstageService;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * 可兑换商品 服务层实现
 *
 * <AUTHOR>
 * @date 2020-08-09
 */
@Service
//@DS("slave")去掉多数据源
public class ExchangeGoodsBackstageServiceImpl extends ServiceImpl<ExchangeGoodsBackstageMapper, ExchangeGoodsBackstage> implements IExchangeGoodsBackstageService {

}
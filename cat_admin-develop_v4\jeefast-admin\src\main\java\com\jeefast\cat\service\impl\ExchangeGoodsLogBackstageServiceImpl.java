package com.jeefast.cat.service.impl;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.ExchangeGoodsLogBackstageMapper;
import com.jeefast.cat.domain.ExchangeGoodsLogBackstage;
import com.jeefast.cat.service.IExchangeGoodsLogBackstageService;
import com.baomidou.dynamic.datasource.annotation.DS;

import java.util.List;

/**
 * 兑换商品日志 服务层实现
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
@Service
//@DS("slave")去掉多数据源
public class ExchangeGoodsLogBackstageServiceImpl extends ServiceImpl<ExchangeGoodsLogBackstageMapper, ExchangeGoodsLogBackstage> implements IExchangeGoodsLogBackstageService {

    @Autowired
    private ExchangeGoodsLogBackstageMapper dao;

    @Override
    public List<CamelCaseMap<String, Object>> logList(QueryWrapper<ExchangeGoodsLogBackstage> queryWrapper) {
        return dao.logList(queryWrapper);
    }
}
package com.jeefast.cat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeefast.cat.domain.GiftInfo;
import com.jeefast.cat.mapper.GiftInfoMapper;
import com.jeefast.cat.service.IGiftInfoService;
import org.springframework.stereotype.Service;

/**
 * 礼物信息 服务层实现
 *
 * <AUTHOR>
 * @date 2020-10-18
 */
@Service
//@DS("slave")去掉多数据源
public class GiftInfoServiceImpl extends ServiceImpl<GiftInfoMapper, GiftInfo> implements IGiftInfoService {

}
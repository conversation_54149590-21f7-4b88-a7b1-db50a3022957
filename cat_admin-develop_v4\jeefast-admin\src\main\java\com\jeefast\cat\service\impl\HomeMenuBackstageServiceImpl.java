package com.jeefast.cat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.HomeMenuBackstageMapper;
import com.jeefast.cat.domain.HomeMenuBackstage;
import com.jeefast.cat.service.IHomeMenuBackstageService;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * 首页菜单 服务层实现
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
@Service
//@DS("slave")去掉多数据源
public class HomeMenuBackstageServiceImpl extends ServiceImpl<HomeMenuBackstageMapper, HomeMenuBackstage> implements IHomeMenuBackstageService {

}
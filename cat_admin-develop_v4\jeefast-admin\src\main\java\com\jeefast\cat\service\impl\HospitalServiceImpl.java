package com.jeefast.cat.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.sys.entity.RuleInfo;
import com.cat.modules.sys.service.IRuleInfoService;
import com.jeefast.cat.domain.Channel;
import com.jeefast.cat.resp.DictResp;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.system.domain.SysDictData;
import com.jeefast.system.service.ISysDictDataService;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.HospitalMapper;
import com.jeefast.cat.domain.Hospital;
import com.jeefast.cat.service.IHospitalService;
import com.baomidou.dynamic.datasource.annotation.DS;
import oshi.util.StringUtil;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 医院 服务层实现
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Service
//@DS("slave")去掉多数据源
public class HospitalServiceImpl extends ServiceImpl<HospitalMapper, Hospital> implements IHospitalService {

    @Resource
    private HospitalMapper hospitalMapper;

    @Resource
    private ISysDictDataService dictDataService;
    @Resource
    private IRuleInfoService ruleInfoService;

    @Override
    public int changeStatus(Hospital hospital) {
        return hospitalMapper.updateById(hospital);
    }

    @Override
    public boolean softDelete(String ids) {
        List<String> idList = Arrays.asList(Convert.toStrArray(ids));
        List<Hospital> hospitalList = hospitalMapper.selectBatchIds(idList);
        if (hospitalList != null && hospitalList.size() > 0) {
            hospitalList.forEach(itm -> {
                Hospital updateHospital = new Hospital();
                updateHospital.setId(itm.getId());
                updateHospital.setIsDelete(1);
                hospitalMapper.updateById(updateHospital);
            });
        }
        return true;
    }

    /**
     * 格式化数据列表
     *
     * @param hospitalList
     * @return
     */
    @Override
    public List<Hospital> processList(List<Hospital> hospitalList) {
        List<SysDictData> dataList = dictDataService.selectDictDataByType("hospital_tag");
        if (dataList != null && dataList.size() > 0) {
            if (hospitalList != null && hospitalList.size() > 0) {
                for (Hospital hospital : hospitalList) {
                    List<String> tagLabelList = new ArrayList<>();
                    if (StringUtils.isNotEmpty(hospital.getHospitalTags())) {
                        List<String> tagValueList = Arrays.asList(StringUtils.split(hospital.getHospitalTags(), ",").clone());
                        tagValueList.forEach(itm -> {
                            for (SysDictData dictData : dataList) {
                                if (itm.equals(dictData.getDictValue())) {
                                    tagLabelList.add(dictData.getDictLabel());
                                }
                            }
                        });
                    }
                    if (tagLabelList.size() > 0) {
                        hospital.setHospitalTagNames(StringUtils.join(tagLabelList));
                    }
                }
            }
        }
        return hospitalList;
    }

    /**
     * 获取医院科室信息
     *
     * @param id
     * @return
     */
    @Override
    public List<DictResp> getHospitalDeptInfo(Long id) {
        List<DictResp> dictRespList = new ArrayList<>();
        Hospital hospital = hospitalMapper.selectById(id);
        if (StringUtils.isNotEmpty(hospital.getDeptRelateCode())) {
            RuleInfo ruleInfo = ruleInfoService.getOne(new QueryWrapper<RuleInfo>().eq("rule_id", hospital.getDeptRelateCode()));
            JSONObject jsonObject = JSON.parseObject(ruleInfo.getContent());
            for (String key : jsonObject.keySet()) {
                DictResp dictResp = new DictResp();
                dictResp.setLabel(key);
                dictResp.setValue(jsonObject.getString(key));
                dictRespList.add(dictResp);
            }
        }
        return dictRespList;
    }
}
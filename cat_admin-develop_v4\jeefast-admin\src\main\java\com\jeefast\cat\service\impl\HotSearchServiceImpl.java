package com.jeefast.cat.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.dynamic.entity.DynamicInfo;
import com.cat.modules.dynamic.resp.DynamicViewRespVo;
import com.cat.modules.dynamic.service.IDynamicInfoService;
import com.cat.modules.stats.service.IStatsAppVisitService;
import com.cat.util.DataRow;
import com.google.common.collect.Lists;
import com.jeefast.cat.domain.DynamicInfoBackstage;
import com.jeefast.cat.service.IDynamicInfoBackstageService;
import com.jeefast.common.enums.HotTypeEnum;
import com.jeefast.common.enums.YesNoEnum;
import com.jeefast.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.HotSearchMapper;
import com.jeefast.cat.domain.HotSearch;
import com.jeefast.cat.service.IHotSearchService;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 热搜词条 服务层实现
 *
 * <AUTHOR>
 * @date 2025-03-11
 */
@Service
//@DS("slave")去掉多数据源
public class HotSearchServiceImpl extends ServiceImpl<HotSearchMapper, HotSearch> implements IHotSearchService {

    @Autowired
    private IDynamicInfoService dynamicInfoService;
    @Autowired
    private IStatsAppVisitService statsAppVisitService;
    @Value("${dynamic.finderUserName}")
    private String finderUserName;

    /**
     * 每小时刷新一次热版
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void refreshTopTen(String delDynamicId) {
        if (!StringUtils.isEmpty(delDynamicId)) {
            //被删除的动态 在当前的热版中泽需要重新执行
            int dbDynamicHot = count(new LambdaQueryWrapper<HotSearch>()
                    .eq(HotSearch::getDynamicId, delDynamicId)
                    .eq(HotSearch::getIsDelete, YesNoEnum.NO.getCode())
                    .eq(HotSearch::getBeAdvertising, YesNoEnum.NO.getCode())
            );
            if (dbDynamicHot <= 0) {
                return;
            }
        }
        List<DynamicInfo> topTenList = dynamicInfoService.getTopTen();
        //先把旧的非广告，未删除的设置为已删除
        update(new LambdaUpdateWrapper<HotSearch>()
                .set(HotSearch::getIsDelete, YesNoEnum.YES.getCode())
                .eq(HotSearch::getIsDelete, YesNoEnum.NO.getCode())
                .eq(HotSearch::getBeAdvertising, YesNoEnum.NO.getCode()));
        List<HotSearch> oldHotList = list(new LambdaQueryWrapper<HotSearch>()
                .select(HotSearch::getDynamicId)
                .in(HotSearch::getDynamicId, topTenList.stream().map(DynamicInfo::getDynamicId).collect(Collectors.toList())));
        Map<String, List<HotSearch>> oldHotDynamicIdMap = oldHotList.stream().collect(Collectors.groupingBy(HotSearch::getDynamicId));
        List<HotSearch> toAddNewTopTen = getHotSearches(topTenList, oldHotDynamicIdMap);
        saveBatch(toAddNewTopTen);

    }

    public List<HotSearch> getHotSearches(List<DynamicInfo> topTenList, Map<String, List<HotSearch>> oldHotDynamicIdMap) {

        List<HotSearch> toAddNewTopTen = Lists.newArrayList();
        //判断 hottype 数据准备
        Date now = new Date();
        DateTime beginOfDay = DateUtil.beginOfDay(now);
        DateTime beforeOneHour = DateUtil.offsetHour(now, -1);
        DateTime beforeTwoHour = DateUtil.offsetHour(now, -2);
        // 获取对应 yyyy-mm-dd HH:mm:ss 字符串
        String nowStr = DateUtil.format(now, "yyyy-MM-dd HH:mm:ss");
        String beginOfDayStr = DateUtil.format(beginOfDay, "yyyy-MM-dd HH:mm:ss");
        String beforeOneHourStr = DateUtil.format(beforeOneHour, "yyyy-MM-dd HH:mm:ss");
        String beforeTwoHourStr = DateUtil.format(beforeTwoHour, "yyyy-MM-dd HH:mm:ss");
        //爆（Burst）
        //• 定义：平台内短期内引发用户高度关注的内容。
        //• 触发条件（需同时满足）：
        //• 阅读量：总的阅读量≥平台日均活跃设备的50%（取过去30天的平均日活跃设备）（如平均活跃设备1000，则阅读量≥500）；
        //• 相对增速：每小时内阅读量增速≥50%（如从10阅读量快速涨到60+）；【增速 = （当前时段阅读量 - 上一时段阅读量）/ 上一时段阅读量 ×100%】（一个人点了10次算10次）
        //热（Hot）
        //• 定义：平台内稳定吸引用户的内容，反映长期价值。
        //• 触发条件（满足其一）：
        //• 持续阅读量：单日阅读量≥日均活跃设备的50%（取过去30天的平均日活跃设备）；
        //新（New）
        //• 定义：新上榜
        List<String> dynamicIds = topTenList.stream().map(DynamicInfo::getDynamicId).collect(Collectors.toList());
        List<DynamicViewRespVo> totalViewCount = dynamicInfoService.viewCount(dynamicIds, null, null);
        List<DynamicViewRespVo> todayViewCount = dynamicInfoService.viewCount(dynamicIds, beginOfDayStr, nowStr);
        List<DynamicViewRespVo> oneHourViewCount = dynamicInfoService.viewCount(dynamicIds, beforeOneHourStr, nowStr);
        List<DynamicViewRespVo> beforeTwoHourViewCount = dynamicInfoService.viewCount(dynamicIds, beforeTwoHourStr, beforeOneHourStr);
        // 根据 dynamicId分组 取 countNum  组成map
        Map<String, Long> totalViewCountMap = totalViewCount.stream().collect(Collectors.toMap(DynamicViewRespVo::getDynamicId, DynamicViewRespVo::getCountNum));
        Map<String, Long> todayViewCountMap = todayViewCount.stream().collect(Collectors.toMap(DynamicViewRespVo::getDynamicId, DynamicViewRespVo::getCountNum));
        Map<String, Long> oneHourViewCountMap = oneHourViewCount.stream().collect(Collectors.toMap(DynamicViewRespVo::getDynamicId, DynamicViewRespVo::getCountNum));
        Map<String, Long> beforeTwoHourViewCountMap = beforeTwoHourViewCount.stream().collect(Collectors.toMap(DynamicViewRespVo::getDynamicId, DynamicViewRespVo::getCountNum));
        //过去30天的平均日活跃设备数
        Date nowEndDate = DateUtil.endOfDay(now);
        //近30天
        Date nowStartDate = DateUtil.offsetDay(nowEndDate, -30);
        List<DataRow> deviceData = statsAppVisitService.userTrendChart(DateUtil.beginOfDay(nowStartDate), DateUtil.endOfDay(nowEndDate));
        // 获取 deviceData  中 getInt("count") 之和
        Integer averageActiveDevice = 0;
        for (DataRow dataRow : deviceData) {
            averageActiveDevice = averageActiveDevice + dataRow.getInt("count");
        }
        averageActiveDevice = averageActiveDevice / 30;
        for (int i = 1; i <= topTenList.size(); i++) {
            DynamicInfo dynamicInfo = topTenList.get(i - 1);
            HotSearch hotSearch = new HotSearch();
            hotSearch.setDynamicId(dynamicInfo.getDynamicId());
            hotSearch.setTitle(dynamicInfo.getTitle());
            hotSearch.setBeAdvertising(YesNoEnum.NO.getCode());
            hotSearch.setRealReadNo(dynamicInfo.getViewCount());
            hotSearch.setInventedReadNo(dynamicInfo.getVisualViewCount());
            hotSearch.setTotalReadNo(dynamicInfo.getViewCount() + dynamicInfo.getVisualViewCount());
            hotSearch.setTotalReadNoUpdateDate(now);
            if (dynamicInfo.getType().equals("3")) {
                hotSearch.setRouteUrl("/find/pages/swiperVideo/swiperVideo?id=" + dynamicInfo.getDynamicId());
            } else if (dynamicInfo.getType().equals("4")) {
                hotSearch.setRouteUrl("shop/pages/openChannelIsActivity?finderUserName=" + finderUserName);
            } else {
                hotSearch.setRouteUrl("/pageSub/note/note?id=" + dynamicInfo.getDynamicId());
            }
            HotTypeEnum hotTypeEnum = getHotTypeEnum(oldHotDynamicIdMap, totalViewCountMap, dynamicInfo, beforeTwoHourViewCountMap, oneHourViewCountMap, todayViewCountMap, averageActiveDevice);
            hotSearch.setHotType(hotTypeEnum == null ? null : hotTypeEnum.getCode());
            hotSearch.setSortNo(i);
            toAddNewTopTen.add(hotSearch);
        }
        return toAddNewTopTen;
    }

    /**
     * //爆（Burst）
     * //• 定义：平台内短期内引发用户高度关注的内容。
     * //• 触发条件（需同时满足）：
     * //• 阅读量：总的阅读量≥平台日均活跃设备的50%（取过去30天的平均日活跃设备）（如平均活跃设备1000，则阅读量≥500）；
     * //• 相对增速：每小时内阅读量增速≥50%（如从10阅读量快速涨到60+）；【增速 = （当前时段阅读量 - 上一时段阅读量）/ 上一时段阅读量 ×100%】（一个人点了10次算10次）
     * //热（Hot）
     * //• 定义：平台内稳定吸引用户的内容，反映长期价值。
     * //• 触发条件（满足其一）：
     * //• 持续阅读量：单日阅读量≥日均活跃设备的50%（取过去30天的平均日活跃设备）；
     * //新（New）
     * //• 定义：新上榜
     *
     * @param oldHotDynamicIdMap
     * @param totalViewCountMap
     * @param dynamicInfo
     * @param beforeTwoHourViewCountMap
     * @param oneHourViewCountMap
     * @param todayViewCountMap
     * @param averageActiveDevice
     * @return
     */
    private static HotTypeEnum getHotTypeEnum(Map<String, List<HotSearch>> oldHotDynamicIdMap,
                                              Map<String, Long> totalViewCountMap, DynamicInfo dynamicInfo,
                                              Map<String, Long> beforeTwoHourViewCountMap, Map<String, Long> oneHourViewCountMap,
                                              Map<String, Long> todayViewCountMap, Integer averageActiveDevice) {
        //阅读量 不存在则当做0
        Long totalReadNo = totalViewCountMap.containsKey(dynamicInfo.getDynamicId()) ? totalViewCountMap.get(dynamicInfo.getDynamicId()) : 0L;
        //相对增速
        BigDecimal relativeGrowthRate = BigDecimal.ZERO;
        if (beforeTwoHourViewCountMap.containsKey(dynamicInfo.getDynamicId())) {
            long oneHourCount = oneHourViewCountMap.containsKey(dynamicInfo.getDynamicId()) ? oneHourViewCountMap.get(dynamicInfo.getDynamicId()) : 0L;
            BigDecimal beforeTwoCount = new BigDecimal(beforeTwoHourViewCountMap.get(dynamicInfo.getDynamicId()));
            relativeGrowthRate = new BigDecimal(oneHourCount).subtract(beforeTwoCount)
                    .divide(beforeTwoCount, 2, RoundingMode.HALF_UP);
        }
        //持续阅读量
        Long todayReadNo = todayViewCountMap.containsKey(dynamicInfo.getDynamicId()) ? todayViewCountMap.get(dynamicInfo.getDynamicId()) : 0L;
        HotTypeEnum hotTypeEnum = null;
        if (totalReadNo >= averageActiveDevice * 0.5 && relativeGrowthRate.intValue() >= 0.5) {
            hotTypeEnum = HotTypeEnum.BOT;
        } else if (todayReadNo >= averageActiveDevice * 0.5) {
            hotTypeEnum = HotTypeEnum.HOT;
        } else if (!oldHotDynamicIdMap.containsKey(dynamicInfo.getDynamicId())) {
            hotTypeEnum = HotTypeEnum.NEW;
        }
        return hotTypeEnum;
    }
}
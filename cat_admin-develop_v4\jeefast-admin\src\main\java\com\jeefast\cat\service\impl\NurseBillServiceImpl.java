package com.jeefast.cat.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.CamelCaseMap;
import cn.hutool.core.util.StrUtil;
import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeefast.cat.common.EleAliasInfo;
import com.jeefast.cat.domain.NurseBillModel;
import com.jeefast.cat.domain.SystemOfSystemsNurseBill;
import com.jeefast.cat.mapper.NurseBillMapper;
import com.jeefast.cat.mapper.SystemOfSystemsNurseBillMapper;
import com.jeefast.cat.req.addEleAliasReq;
import com.jeefast.cat.service.NurseBillService;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.json.JSON;
import com.jeefast.common.utils.DateUtils;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.framework.web.service.DictService;
import com.jeefast.system.domain.SysDictData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.sql.SQLIntegrityConstraintViolationException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NurseBillServiceImpl extends ServiceImpl<NurseBillMapper, NurseBillModel> implements NurseBillService {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private NurseBillMapper nurseBillMapper;

    @Autowired
    private SystemOfSystemsNurseBillMapper systemOfSystemsNurseBillMapper;


    @Autowired
    DictService dictService;

    @Override
    public List<CamelCaseMap<String, Object>> NurseBillList(QueryWrapper<NurseBillModel> queryWrapper) {
        return nurseBillMapper.nurseBillList(queryWrapper);
    }

    @Override
    @Transactional
    public boolean delete(List<String> asList) {
        boolean flag = false;
        for (String id : asList) {
            flag = this.removeById(id);
        }
        deleteAliaCache();
        return flag;
    }

    @Transactional
    public boolean Move(NurseBillModel req, QueryWrapper<NurseBillModel> queryWrapper) {
        boolean flag = false;
        String ele_id_list = req.getIdList();
        String[] eleIdList = Convert.toStrArray(ele_id_list);
        BigInteger eleId = req.getEleId();
        NurseBillModel eleIns = this.getById(eleId);
        nurseBillMapper.moveEleAliasToNewEle(eleId, queryWrapper);
        nurseBillMapper.moveEleDetail(eleId, eleIns.getEleName(), queryWrapper);
        List<CamelCaseMap<String, Object>> nurseBillList = this.NurseBillList(queryWrapper);
        for (CamelCaseMap<String, Object> item : nurseBillList) {
            String eleRemarks = item.get("eleRemarks").toString();
            String projectLabel = eleIns.getProjectLabel();
            nurseBillMapper.insertEleAlias(eleId, eleRemarks, projectLabel);
        }
        for (String id : eleIdList) {
//            this.getById(id);
            flag = this.removeById(id);

        }
//        this.removeByIds(eleIdList);
        deleteAliaCache();
        return flag;
    }

    @Transactional
    public boolean addSave(addEleAliasReq req) {
        if (StringUtils.isEmpty(req.getEleMaster()) || StringUtils.isEmpty(req.getEleRemarks())) {
            throw new RuntimeException("指标显示名称 或者 指标备注，不能为空");
        }
        List<NurseBillModel> nurseBillList = nurseBillMapper.selectList(new LambdaUpdateWrapper<NurseBillModel>()
                .eq(NurseBillModel::getEleMaster, req.getEleMaster())
                .or().eq(NurseBillModel::getEleRemarks, req.getEleRemarks()));
        if (CollectionUtil.isNotEmpty(nurseBillList)) {
            throw new RuntimeException("指标显示名称 或者 指标备注 重复，请勿重复添加");
        }
        List<EleAliasInfo> eleAliasList = req.getEleAliasInfoList();
        NurseBillModel eleIns = new NurseBillModel();
        BeanUtil.copyProperties(req, eleIns);
        eleIns.setSystemNames(JSONObject.toJSONString(req.getSystemNameList()));
        boolean flag = this.save(eleIns);
        if (eleAliasList != null) {
            for (EleAliasInfo item : eleAliasList) {
                nurseBillMapper.insertEleAlias(eleIns.getEleId(), item.getAliasName(), item.getProjectLabel());
            }
        }
        if (CollectionUtil.isNotEmpty(req.getSystemNameList())) {
            List<SysDictData> dictDatas = dictService.getType("system_of_systems_name");
            List<SysDictData> sysDictData = dictDatas.stream()
                    .filter(i -> req.getSystemNameList().contains(i.getDictValue())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(sysDictData)) {
                //遍历插入
                for (SysDictData sysDictDatum : sysDictData) {
                    SystemOfSystemsNurseBill systemOfSystemsNurseBill = new SystemOfSystemsNurseBill();
                    SysDictData sysDictData1 = sysDictData.stream()
                            .filter(i -> i.getDictValue().equals(sysDictDatum.getDictValue())).findFirst().orElse(null);
                    systemOfSystemsNurseBill.setSystemName(sysDictData1.getDictLabel());
                    systemOfSystemsNurseBill.setEleName(eleIns.getEleName());
                    systemOfSystemsNurseBill.setCreateUid("ai");
                    systemOfSystemsNurseBill.setIsDelete("0");
                    systemOfSystemsNurseBill.setWriteDate(LocalDateTime.now());
                    systemOfSystemsNurseBill.setCreateDate(new Date());
                    try {
                        systemOfSystemsNurseBillMapper.insert(systemOfSystemsNurseBill);
                    } catch (Exception e) {
                        throw new RuntimeException("系统名称数据可能存在重复，请勿重复添加");
                    }
                }
            }
        }
        deleteAliaCache();
        return flag;
    }

    public EleAliasInfo selectAliasByEleId(BigInteger eleId) {
        return nurseBillMapper.selectAliasByEleId(eleId);
    }

    @Override
    public boolean updateNurseBill(NurseBillModel nurseBill) {
        if (StringUtils.isEmpty(nurseBill.getEleMaster()) || StringUtils.isEmpty(nurseBill.getEleRemarks())) {
            throw new RuntimeException("指标显示名称 或者 指标备注，不能为空");
        }
        nurseBillMapper.deleteAliasByEleId(nurseBill.getEleId());
        if (StrUtil.isNotEmpty(nurseBill.getProjectAlias()) && StrUtil.isNotEmpty(nurseBill.getAliasNameList())) {
            for (String projectName : nurseBill.getProjectAlias().split(";")) {
                for (String alias : nurseBill.getAliasNameList().split(";")) {
                    nurseBillMapper.insertEleAlias(nurseBill.getEleId(), alias, projectName);
                }
            }
        }
        if (StringUtils.isNotEmpty(nurseBill.getEleName()) && CollectionUtil.isNotEmpty(nurseBill.getSystemNameList())) {
            systemOfSystemsNurseBillMapper.delete(new LambdaUpdateWrapper<SystemOfSystemsNurseBill>()
                    .eq(SystemOfSystemsNurseBill::getEleName,nurseBill.getEleName()));
            List<SysDictData> dictDatas = dictService.getType("system_of_systems_name");

            List<SysDictData> sysDictData = dictDatas.stream()
                    .filter(i -> nurseBill.getSystemNameList().contains(i.getDictValue())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(sysDictData)) {
                //遍历插入
                for (SysDictData sysDictDatum : sysDictData) {
                    SystemOfSystemsNurseBill systemOfSystemsNurseBill = new SystemOfSystemsNurseBill();
                    SysDictData sysDictData1 = sysDictData.stream()
                            .filter(i -> i.getDictValue().equals(sysDictDatum.getDictValue())).findFirst().orElse(null);
                    systemOfSystemsNurseBill.setSystemName(sysDictData1.getDictLabel());
                    systemOfSystemsNurseBill.setEleName(nurseBill.getEleName());
                    systemOfSystemsNurseBill.setCreateUid("ai");
                    systemOfSystemsNurseBill.setIsDelete("0");
                    systemOfSystemsNurseBill.setWriteDate(LocalDateTime.now());
                    systemOfSystemsNurseBill.setCreateDate(new Date());
                    try {
                        systemOfSystemsNurseBillMapper.insert(systemOfSystemsNurseBill);
                    } catch (Exception e) {
                        throw new RuntimeException("系统名称数据可能存在重复，请勿重复添加");
                    }
                }
                List<String> dictDataValueList = sysDictData.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
                nurseBill.setSystemNames(JSONObject.toJSONString(dictDataValueList));
            }

        }
        deleteAliaCache();
        return nurseBillMapper.updateById(nurseBill) > 0;
    }

    private void deleteAliaCache() {
        redisUtil.del("sys:ele_name_key");
        redisUtil.del("sys:ele_alias_dict");
        redisUtil.del("sys:ele_name_id");
    }
}

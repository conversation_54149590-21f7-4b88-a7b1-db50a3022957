package com.jeefast.cat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jeefast.cat.domain.PayInfoBackstage;
import com.jeefast.cat.mapper.PayInfoBackstageMapper;
import com.jeefast.cat.service.IPayInfoBackstageService;
import org.springframework.stereotype.Service;

/**
 * 支付信息 服务层实现
 *
 * <AUTHOR>
 * @date 2020-09-28
 */
@Service
//@DS("slave")去掉多数据源
public class PayInfoBackstageServiceImpl extends ServiceImpl<PayInfoBackstageMapper, PayInfoBackstage> implements IPayInfoBackstageService {

}
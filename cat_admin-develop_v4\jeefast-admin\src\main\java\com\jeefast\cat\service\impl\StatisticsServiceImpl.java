package com.jeefast.cat.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jeefast.cat.domain.BToB;
import com.jeefast.cat.domain.Channel;
import com.jeefast.cat.domain.Hospital;
import com.jeefast.cat.req.AICountReq;
import com.jeefast.cat.req.AIDeptCountReq;
import com.jeefast.cat.resp.AIDeptCountResp;
import com.jeefast.cat.service.*;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.framework.util.ShiroUtils;
import com.jeefast.system.domain.SysUser;
import com.jeefast.system.service.ISysUserService;
import io.swagger.models.HttpMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.bcel.Const;
import org.springframework.stereotype.Service;
import org.springframework.ui.ModelMap;

import javax.annotation.Resource;
import javax.xml.ws.WebServiceException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StatisticsServiceImpl implements IStatisticsService {

    @Resource
    private IThirdService thirdService;
    @Resource
    private ISysUserService userService;
    @Resource
    IChannelService channelService;
    @Resource
    IHospitalService hospitalService;
    @Resource
    IBToBService bService;
    String baseUrl="proxy/base/statistics/";

    @Override
    public JSONArray count(AICountReq req) throws JsonProcessingException {
        SysUser user = userService.selectUserById(ShiroUtils.getUserId());
        String channelId = StringUtils.isNotEmpty(user.getChannelIds()) ? user.getChannelIds() : "";
        String hospitalId = StringUtils.isNotEmpty(user.getHospitalIds()) ? user.getHospitalIds() : "";
        if (StringUtils.isNotEmpty(req.getChannelId())) {
            channelId = req.getChannelId();
        }
        if (StringUtils.isNotEmpty(req.getHospitalId())) {
            hospitalId = req.getHospitalId();
        }
        Map<String, String> pushMap = new HashMap<>();
        pushMap.put("start_time", req.getStartTime());
        pushMap.put("end_time", req.getEndTime());
        pushMap.put("user_id", "");
        pushMap.put("channel_id", channelId);
        pushMap.put("hospital_id", hospitalId);
        String params = getParams(pushMap);
        String url = baseUrl+"count?" + params;
        log.info("AI分诊统计返回值:");
        return callList(pushMap, url);
    }

    @Override
    public AIDeptCountResp deptCount(AIDeptCountReq req) throws JsonProcessingException {
        SysUser user = userService.selectUserById(ShiroUtils.getUserId());
        String channelId = StringUtils.isNotEmpty(user.getChannelIds()) ? user.getChannelIds() : "";
        String hospitalId = StringUtils.isNotEmpty(user.getHospitalIds()) ? user.getHospitalIds() : "";
        if (StringUtils.isNotEmpty(req.getChannelId())) {
            channelId = req.getChannelId();
        }
        if (StringUtils.isNotEmpty(req.getHospitalId())) {
            hospitalId = req.getHospitalId();
        }
        Map<String, String> pushMap = new HashMap<>();
        pushMap.put("start_time", req.getStartTime());
        pushMap.put("end_time", req.getEndTime());
        pushMap.put("user_id", "");
        pushMap.put("channel_id", channelId);
        pushMap.put("hospital_id", hospitalId);
        pushMap.put("dept_code", req.getDeptCode());
        pushMap.put("page", req.getPageNum());
        pushMap.put("limit", req.getPageSize());
        String params = getParams(pushMap);
        String url = baseUrl+"dept_count?" + params;
        log.info("AI分诊科室统计返回值:");
        return callPage(pushMap, url);
    }

    @Override
    public JSONArray deptTotal(AIDeptCountReq req) throws JsonProcessingException {
        SysUser user = userService.selectUserById(ShiroUtils.getUserId());
        String channelId = StringUtils.isNotEmpty(user.getChannelIds()) ? user.getChannelIds() : "";
        String hospitalId = StringUtils.isNotEmpty(user.getHospitalIds()) ? user.getHospitalIds() : "";
        if (StringUtils.isNotEmpty(req.getChannelId())) {
            channelId = req.getChannelId();
        }
        if (StringUtils.isNotEmpty(req.getHospitalId())) {
            hospitalId = req.getHospitalId();
        }
        Map<String, String> pushMap = new HashMap<>();
        pushMap.put("start_time", req.getStartTime());
        pushMap.put("end_time", req.getEndTime());
        pushMap.put("user_id", "");
        pushMap.put("channel_id", channelId);
        pushMap.put("hospital_id", hospitalId);
        pushMap.put("dept_code", req.getDeptCode());
        String params = getParams(pushMap);
        String url = baseUrl+"dept_total?" + params;
        log.info("AI分诊科室统计返回值:");
        return callList(pushMap, url);
    }

    @Override
    public ModelMap getModelMap(ModelMap mmap) {
        SysUser user = userService.selectUserById(ShiroUtils.getUserId());
        //渠道非空
        List<Channel> channelList = new ArrayList<>();
        List<Hospital> hospitalList = new ArrayList<>();
        if (StringUtils.isNotEmpty(user.getChannelIds()) || StringUtils.isNotEmpty(user.getHospitalIds())) {
            //渠道非空
            if (StringUtils.isNotEmpty(user.getChannelIds())) {
                QueryWrapper<Channel> channelQuery = new QueryWrapper<>();
                channelQuery.eq("is_delete", 0);
                List<String> channelIdList = Arrays.asList(user.getChannelIds().split(","));
                channelQuery.in("id", channelIdList);
                channelList = channelService.list(channelQuery);
                List<Long> idList = channelList.stream().map(Channel::getId).collect(Collectors.toList());
                List<BToB> bList = bService.list(new QueryWrapper<BToB>().in("channel_id", idList).eq("'is_delete'", 0));
                if (bList != null && bList.size() > 0) {
                    List<Integer> hospitalIdList = bList.stream().map(BToB::getHospitalId).collect(Collectors.toList());
                    hospitalList = hospitalService.list(new QueryWrapper<Hospital>().eq("is_delete", 0).in("id", hospitalIdList));
                }
            }
            //医院非空
            if (StringUtils.isNotEmpty(user.getHospitalIds())) {
                QueryWrapper<Hospital> hospitalQuery = new QueryWrapper<>();
                List<String> hospitalIdList = Arrays.asList(user.getHospitalIds().split(","));
                hospitalQuery.eq("is_delete", 0);
                hospitalQuery.in("id", hospitalIdList);
                hospitalList = hospitalService.list(hospitalQuery);
                List<Long> idList = hospitalList.stream().map(Hospital::getId).collect(Collectors.toList());
                List<BToB> bList = bService.list(new QueryWrapper<BToB>().in("hospital_id", idList).eq("'is_delete'", 0));
                if (bList != null && bList.size() > 0) {
                    List<Integer> channelIdList = bList.stream().map(BToB::getChannelId).collect(Collectors.toList());
                    channelList = channelService.list(new QueryWrapper<Channel>().eq("is_delete", 0).in("id", channelIdList));
                }
            }
        } else {
            QueryWrapper<Channel> channelQuery = new QueryWrapper<>();
            channelQuery.eq("is_delete", 0);
            channelList = channelService.list(channelQuery);
            QueryWrapper<Hospital> hospitalQuery = new QueryWrapper<>();
            hospitalQuery.eq("is_delete", 0);
            hospitalList = hospitalService.list(hospitalQuery);
        }
        mmap.put("channel_list", channelList);
        mmap.put("hospital_list", hospitalList);
        return mmap;
    }

    private String getParams(Map<String, String> pushMap) {
        List<String> strList = new ArrayList<>();
        for (String key : pushMap.keySet()) {
            strList.add(key + "=" + pushMap.get(key));
        }
        return StringUtils.join(strList, "&");
    }

    private JSONArray callList(Map<String, String> pushMap, String url) throws JsonProcessingException {
        String result = thirdService.serviceCall(pushMap, url, HttpMethod.POST.name());
        log.info(result);
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(result);
        String code = jsonObject.get("code").toString();
        JSONArray jsonArray = new JSONArray();
        if ("200".equals(code)) {
            jsonArray = jsonObject.getJSONArray("data");
        } else {
            throw new WebServiceException(jsonObject.get("msg").toString());
        }
        return jsonArray;
    }

    private AIDeptCountResp callPage(Map<String, String> pushMap, String url) throws JsonProcessingException {
        String result = thirdService.serviceCall(pushMap, url, HttpMethod.POST.name());
        log.info(result);
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(result);
        String code = jsonObject.get("code").toString();
        AIDeptCountResp resp = new AIDeptCountResp();
        JSONArray jsonArray = new JSONArray();
        if ("200".equals(code)) {
            com.alibaba.fastjson.JSONObject dataObject = jsonObject.getJSONObject("data");
            jsonArray = dataObject.getJSONArray("data");
            resp.setJsonArray(jsonArray);
            resp.setTotal(dataObject.getLong("total"));
        } else {
            throw new WebServiceException(jsonObject.get("msg").toString());
        }
        return resp;
    }
}

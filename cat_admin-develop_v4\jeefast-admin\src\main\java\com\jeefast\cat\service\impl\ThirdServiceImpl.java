package com.jeefast.cat.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jeefast.cat.service.IThirdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Service
@Slf4j
public class ThirdServiceImpl implements IThirdService {
    @Value("${yueyue.url}")
    private String BASE_URL;

    @Value("${yueyue.user_id}")
    private String USER_ID;

    @Value("${yueyue.secret}")
    private String SECRET;


    @Override
    public String serviceCall(Map<String, String> pushMap, String methodUrl, String methodType) throws JsonProcessingException {
        pushMap.put("t1", String.valueOf(System.currentTimeMillis()));
//        addCommonParams(pushMap);
        Map<String, String> sortMap = new TreeMap<>(pushMap);
        String paramStr = getParamsStr(sortMap);
        String sign = toSign(paramStr);
        ObjectMapper objectMapper = new ObjectMapper();
        String sendParams = objectMapper.writeValueAsString(sortMap);
        log.info("sendInfo：" + sendParams);
        log.info("sign：" + sign);
        String requestUrl = BASE_URL + methodUrl;
        String result = "";
        if ("POST".equals(methodType)) {
            result = httpPost(sendParams, requestUrl, sign);
        } else if ("PUT".equals(methodType)) {
            result = httpPut(sendParams, requestUrl, sign);
        }
        return result;
    }

    private String toSign(String signStr) {
        signStr += "&" + SECRET;
        log.info("signStr:" + signStr);
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            byte[] hash = digest.digest(signStr.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private String getParamsStr(Map<String, String> sortMap) {
        List<String> paramsList = new ArrayList<>();
        for (Map.Entry<String, String> entry : sortMap.entrySet()) {
            if (StringUtils.isNotEmpty(entry.getValue())) {
                paramsList.add(entry.getKey() + "=" + entry.getValue());
            }
        }
        String paramStr = String.join("&", paramsList);
        log.info("yueyue");
        log.info(paramStr);
        return paramStr;
    }

    private void addCommonParams(Map<String, String> map) {
        map.put("user_id", USER_ID);
    }

    public String httpPost(String jsonData, String url, String sign) {
        log.info("current-method:{}", "post");
        log.info("user-id:{}", USER_ID);
        log.info("url:{}", url);
        HttpClient client = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("user-id", USER_ID);
        httpPost.setHeader("sign", sign);
        StringEntity entity = new StringEntity(jsonData, "UTF-8");
        httpPost.setEntity(entity);
        try {
            log.info("headers:{}", org.json.JSONObject.valueToString(httpPost.getAllHeaders()));
            HttpResponse response = client.execute(httpPost);
            return EntityUtils.toString(response.getEntity());
        } catch (Exception e) {

        }
        return "";
    }

    private String httpPut(String jsonData, String url, String sign) {
        log.info("current-method:{}", "put");
        log.info("user-id:{}", USER_ID);
        HttpClient client = HttpClients.createDefault();
        HttpPut httpPut = new HttpPut(url);
        httpPut.setHeader("Content-Type", "application/json");
        httpPut.setHeader("user-id", USER_ID);
        httpPut.setHeader("sign", sign);
        StringEntity entity = new StringEntity(jsonData, "UTF-8");
        httpPut.setEntity(entity);
        try {
            log.info("headers:{}", org.json.JSONObject.valueToString(httpPut.getAllHeaders()));
            HttpResponse response = client.execute(httpPut);
            return EntityUtils.toString(response.getEntity());
        } catch (Exception e) {

        }
        return "";
    }
}

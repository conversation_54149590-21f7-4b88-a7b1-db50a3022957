package com.jeefast.cat.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.TopicInfoBackstageMapper;
import com.jeefast.cat.domain.TopicInfoBackstage;
import com.jeefast.cat.service.ITopicInfoBackstageService;

/**
 * 话题 服务层实现
 *
 * <AUTHOR>
 * @date 2020-08-08
 */
@Service
//@DS("slave")去掉多数据源
public class TopicInfoBackstageServiceImpl extends ServiceImpl<TopicInfoBackstageMapper, TopicInfoBackstage> implements ITopicInfoBackstageService {

}
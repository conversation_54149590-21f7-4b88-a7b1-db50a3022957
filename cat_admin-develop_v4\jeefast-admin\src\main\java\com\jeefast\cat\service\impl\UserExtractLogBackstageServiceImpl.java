package com.jeefast.cat.service.impl;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.user.entity.UserIncomeLog;
import com.cat.modules.user.service.IUserIncomeLogService;
import com.jeefast.cat.domain.UserInfo;
import com.jeefast.cat.service.IUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.UserExtractLogBackstageMapper;
import com.jeefast.cat.domain.UserExtractLogBackstage;
import com.jeefast.cat.service.IUserExtractLogBackstageService;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户提现记录 服务层实现
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
@Service
//@DS("slave")去掉多数据源
public class UserExtractLogBackstageServiceImpl extends ServiceImpl<UserExtractLogBackstageMapper, UserExtractLogBackstage> implements IUserExtractLogBackstageService {
    @Autowired
    private UserExtractLogBackstageMapper dao;
    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private IUserIncomeLogService userIncomeLogService;


    @Override
    public List<CamelCaseMap<String, Object>> infoList(QueryWrapper<UserExtractLogBackstage> queryWrapper) {
        return dao.infoList(queryWrapper);
    }

    @Override
    @Transactional
    public boolean authSave(String id, String status,String reason,String remark) {
        UserExtractLogBackstage userExtractLog = this.getById(id);
        String userId = userExtractLog.getUserId();
        boolean flag = this.update(new UpdateWrapper<UserExtractLogBackstage>()
                .set("status",status)
                .set("remark",remark)
                .set("reason",reason)
                .eq("id",id)
        );
        if(flag){
            if("3".equals(status)){

            }else if("5".equals(status)){
                //驳回还原用户余额
                Double money = userExtractLog.getMoney();
                flag = userInfoService.update(new UpdateWrapper<UserInfo>()
                        .setSql("cat_money=cat_money+"+money)
                        .eq("user_id",userId)
                );
                UserIncomeLog userIncomeLog = new UserIncomeLog();
                userIncomeLog.setUserId(userId);
                userIncomeLog.setTitle("提现驳回退回钱包");
                userIncomeLog.setMoney(money);
                userIncomeLog.setSort(0);
                userIncomeLog.setStatus("3");
                userIncomeLog.setType("1");
                userIncomeLog.setRemark("提现驳回退回钱包");
                userIncomeLogService.save(userIncomeLog);
            }
        }
        return flag;
    }
}
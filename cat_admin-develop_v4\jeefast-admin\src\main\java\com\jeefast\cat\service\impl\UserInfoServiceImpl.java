package com.jeefast.cat.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.can.service.ICanUpdateLogService;
import com.cat.modules.user.service.IUserIncomeLogService;
import com.jeefast.cat.req.UserRechargeReq;
import com.jeefast.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.UserInfoMapper;
import com.jeefast.cat.domain.UserInfo;
import com.jeefast.cat.service.IUserInfoService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户信息 服务层实现
 *
 * <AUTHOR>
 * @date 2019-12-19
 */
@Service
//@DS("slave")去掉多数据源
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements IUserInfoService {

    @Autowired
    private UserInfoMapper userInfoMapper;
    @Autowired
    private ICanUpdateLogService canUpdateLogService;
    @Autowired
    private IUserIncomeLogService userIncomeLogService;

    @Override
    @Transactional
    public AjaxResult recharge(UserRechargeReq req) {
        boolean flag = false;
        String userId = req.getUserId();
        if(req.getCanNumber()!=null && (req.getCanNumber()>0 || req.getCanNumber()<0)){
            String upSql = "can_number = can_number ";
            //判断是加还是减少
            if(req.getCanNumber()>0){
                upSql+="+"+ req.getCanNumber();
            }else if(req.getCanNumber()<0){
                upSql+="-"+ req.getCanNumber();
            }
            flag = this.update(new UpdateWrapper<UserInfo>()
                    .setSql(upSql)
                    .eq("user_id",userId)
            );
            if(flag){
                flag = canUpdateLogService.addLog(userId,req.getCanNumber().intValue(),"0",req.getRemark());
            }
        }
        //钱包收入记录
        if(req.getCatMoney()!=null && (req.getCatMoney()>0 || req.getCatMoney()<0)){
            String upSql = "cat_money = cat_money ";
            //判断是加还是减少
            if(req.getCatMoney()>0){
                upSql+="+"+ req.getCatMoney();
            }else if(req.getCatMoney()<0){
                upSql+="-"+ req.getCatMoney();
            }
            flag = this.update(new UpdateWrapper<UserInfo>()
                    .setSql(upSql)
                    .eq("user_id",userId)
            );
            if(flag){
                flag = userIncomeLogService.addLog(userId,req.getCatMoney().intValue(),"1",req.getRemark());
            }
        }
        if(!flag){
            AjaxResult.error("未更新数据");
        }
        return AjaxResult.success();
    }








}
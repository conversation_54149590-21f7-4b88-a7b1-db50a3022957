package com.jeefast.cat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.UserTagConfigBackstageMapper;
import com.jeefast.cat.domain.UserTagConfigBackstage;
import com.jeefast.cat.service.IUserTagConfigBackstageService;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * 用户标签配置 服务层实现
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
@Service
//@DS("slave")去掉多数据源
public class UserTagConfigBackstageServiceImpl extends ServiceImpl<UserTagConfigBackstageMapper, UserTagConfigBackstage> implements IUserTagConfigBackstageService {

}
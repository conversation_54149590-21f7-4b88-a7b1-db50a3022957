package com.jeefast.cat.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.VipOpenConfigBackstageMapper;
import com.jeefast.cat.domain.VipOpenConfigBackstage;
import com.jeefast.cat.service.IVipOpenConfigBackstageService;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * vip开通配置 服务层实现
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
@Service
//@DS("slave")去掉多数据源
public class VipOpenConfigBackstageServiceImpl extends ServiceImpl<VipOpenConfigBackstageMapper, VipOpenConfigBackstage> implements IVipOpenConfigBackstageService {

}
package com.jeefast.cat.service.impl;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeefast.cat.mapper.VipOpenLogBackstageMapper;
import com.jeefast.cat.domain.VipOpenLogBackstage;
import com.jeefast.cat.service.IVipOpenLogBackstageService;
import com.baomidou.dynamic.datasource.annotation.DS;

import java.util.List;

/**
 * vip开通记录 服务层实现
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
@Service
//@DS("slave")去掉多数据源
public class VipOpenLogBackstageServiceImpl extends ServiceImpl<VipOpenLogBackstageMapper, VipOpenLogBackstage> implements IVipOpenLogBackstageService {

    @Autowired
    private VipOpenLogBackstageMapper vipOpenLogBackstageMapper;

    @Override
    public List<CamelCaseMap<String, Object>> userList(QueryWrapper<VipOpenLogBackstage> queryWrapper) {
        return vipOpenLogBackstageMapper.userList(queryWrapper);
    }




}
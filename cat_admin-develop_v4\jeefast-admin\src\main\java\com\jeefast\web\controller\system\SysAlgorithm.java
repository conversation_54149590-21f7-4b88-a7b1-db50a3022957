package com.jeefast.web.controller.system;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.page.TableDataInfo;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/system/algorithm")
public class SysAlgorithm extends BaseController {
    private String prefix = "system/algorithm";

    private List<Map<String, String>> cache = new ArrayList<>();

    @GetMapping()
    public String algorithm() {
        return prefix + "/algorithm";
    }

    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo getList(Integer pageSize, Integer pageNum) throws Exception {
        if (cache.isEmpty()) {
            BufferedReader reader = new BufferedReader(new FileReader("C:\\Users\\<USER>\\Desktop\\output.json"));
            String text = reader.lines().collect(Collectors.joining());
            JSONArray array = JSON.parseArray(text);
            List<Map<String, String>> list = new ArrayList<>();
            for (int i = 0; i < array.size(); i++) {
                JSONObject object = array.getJSONObject(i);
                String id = object.getJSONObject("_id").getString("$oid");
                String content = object.getString("content");
                Map<String, String> map = new HashMap<>();
                map.put("id", id);
                map.put("content", content);
                map.put("status", StrUtil.isEmpty(object.getString("status")) ? "已通过" : "已拒绝");
                list.add(map);
            }
            cache = list;
        }
        int start = (pageNum - 1) * pageSize;
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setRows(cache.subList(start, start + pageSize));
        tableDataInfo.setTotal(cache.size());
        tableDataInfo.setCode(0);
        return tableDataInfo;
    }

}

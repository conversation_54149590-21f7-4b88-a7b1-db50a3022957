package com.jeefast.web.controller.system;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import com.jeefast.common.config.Global;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.framework.util.ShiroUtils;
import com.jeefast.system.domain.SysMenu;
import com.jeefast.system.domain.SysUser;
import com.jeefast.system.service.ISysMenuService;

/**
 * 首页 业务处理
 *
 * <AUTHOR>
 */
@Controller
public class SysIndexController extends BaseController {
    @Autowired
    private ISysMenuService menuService;
    @Value("${jeefast.pageTitle}")
    private String pageTitle;

    // 系统首页
    @GetMapping("/index")
    public String index(ModelMap mmap) {
        // 取身份信息
        SysUser user = ShiroUtils.getSysUser();
        // 根据用户id取出菜单
        List<SysMenu> menus = menuService.selectMenusByUser(user);
        mmap.put("menus", menus);
        mmap.put("pageTitle", pageTitle);
        mmap.put("user", user);
        mmap.put("copyrightYear", Global.getCopyrightYear());
        mmap.put("demoEnabled", Global.isDemoEnabled());
        boolean hasHomePerm=false;
        String openIndex=menus.get(0).getChildren().get(0).getUrl();
        if (menus != null && menus.size() > 0) {
            for (SysMenu menu : menus) {
                if(menu.getChildren()!=null&&menu.getChildren().size()>0){
                    for(SysMenu sub:menu.getChildren()){
                        if(sub.getPerms().equals("home-index")){
                            hasHomePerm=true;
                            break;
                        }
                    }
                }
            }
        }
        if(hasHomePerm){
            openIndex="index";
        }
        mmap.put("homeEnabled", hasHomePerm);
        mmap.put("openIndex",openIndex);
        return "index";
    }

    // 切换主题
    @GetMapping("/system/switchSkin")
    public String switchSkin(ModelMap mmap) {
        return "skin";
    }

    // 系统介绍
    @GetMapping("/system/main")
    public String main(ModelMap mmap) {
        mmap.put("version", Global.getVersion());
        return "main";
    }
}

package com.jeefast.web.controller.tool;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.ueditor.ActionEnter;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cat.modules.dynamic.entity.DynamicMedia;
import com.cat.modules.dynamic.service.IDynamicMediaService;
import com.cat.modules.sys.service.ISysFileUploadService;
import com.jeefast.cat.domain.DynamicInfoBackstage;
import com.jeefast.cat.service.IDynamicInfoBackstageService;
import com.jeefast.common.utils.file.FileUploadUtils;
import com.jeefast.common.utils.file.UploadCloudFileUtil;
import com.jeefast.framework.util.ShiroUtils;
import com.jeefast.system.domain.SysFileUpload;
import com.jeefast.system.domain.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Controller
@RequestMapping("/ueditor")
public class UEditorController {

    @Autowired
    private UploadCloudFileUtil uploadCloudFileUtil;

    @Autowired
    private IDynamicInfoBackstageService dynamicInfoBackstageService;

    @Autowired
    private IDynamicMediaService dynamicMediaService;

    @Autowired
    private ISysFileUploadService sysFileUploadService;

    @RequestMapping(value = "/config")
    public void config(MultipartFile upfile, HttpServletRequest request, HttpServletResponse response) {
        response.setContentType("application/json");
        String value = request.getParameter("action");
        String rootPath = request.getSession().getServletContext().getRealPath("/");
        try {
            if ("config".equals(value)) {
                String exec = new ActionEnter(request, rootPath).exec();
                PrintWriter writer = response.getWriter();
                writer.write(exec);
                writer.flush();
                writer.close();
            } else if ("uploadimage".equals(value)) {
                response.setContentType("application/json;charset=utf-8");
                String originalFilename = upfile.getOriginalFilename();
                if (StrUtil.isEmpty(originalFilename)) {
                    log.error("错误的文件格式！");
                    return;
                }
                String type = originalFilename.substring(originalFilename.indexOf("."));
                long size = upfile.getSize();
                /*System.out.println(originalFilename);
                System.out.println(size);*/
                // 图片访问地址（tomcat服务器）


                //上传文件支持本地云端
                String cloudPath = FileUploadUtils.uploadExt(upfile);
                //云上传
                Map<String, Object> map = new HashMap<>();
                map.put("state", "SUCCESS");
                map.put("original", originalFilename);
                map.put("size", size);
                map.put("title", originalFilename);
                map.put("type", type);
                map.put("url", cloudPath);
                String result = JSON.toJSONString(map);
                PrintWriter writer = response.getWriter();
                writer.write(result);
                writer.flush();
                writer.close();

                int lastBackslashIndex = cloudPath.lastIndexOf("/");
                String fileNameWithExtension = cloudPath.substring(lastBackslashIndex + 1);
                String[] split = fileNameWithExtension.split("\\.");
                SysFileUpload sysFileUpload = new SysFileUpload();
                sysFileUpload.setUserId(String.valueOf(ShiroUtils.getUserId()));
                sysFileUpload.setTruename(ShiroUtils.getUserName());
                sysFileUpload.setId(IdUtil.fastSimpleUUID());
                sysFileUpload.setFileSource("public");
                sysFileUpload.setCode(split[0]);
                sysFileUpload.setFileName(originalFilename);
                sysFileUpload.setFileSize(new BigDecimal(size));
                sysFileUpload.setFileContentType("image/" + split[1]);
                sysFileUpload.setFileExtension(split[1]);
                sysFileUpload.setFilePath(cloudPath);
                sysFileUpload.setCreateDate(LocalDateTime.now());
                sysFileUploadService.insertData(sysFileUpload);

                /*String fileFullName = "/Users/<USER>/Documents/workspace/cat/cat_app_api/" + originalFilename;
                // 图片访问地址（tomcat服务器）
                String url = "http://baidu/" + originalFilename ;
                try {
                    response.setContentType("application/json;charset=utf-8");
                    upfile.transferTo(new File(fileFullName));

                    Map<String,Object> map = new HashMap<String,Object>() ;
                    map.put("state", "SUCCESS") ;
                    map.put("original", originalFilename) ;
                    map.put("size", size) ;
                    map.put("title", originalFilename) ;
                    map.put("type", type) ;
                    map.put("url", url) ;
                    String result = JSON.toJSONString(map);
                    System.out.println("result : " + result);
                    PrintWriter writer = response.getWriter();
                    writer.write(result);
                    writer.flush();
                    writer.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }*/
            } else if ("listimage".equals(value)) {
                String dynamicId = request.getParameter("dynamicId");
                if (StrUtil.isEmpty(dynamicId)) {
                    log.error("参数错误，找不到对应资源！");
                    return;
                }
                List<DynamicMedia> dynamicMediaList = dynamicMediaService.list(new QueryWrapper<DynamicMedia>().eq("dynamic_id", dynamicId).orderByAsc("sort_no"));

                Map<String, Object> obj = new HashMap<>();
                obj.put("state", "SUCCESS");
                List<Map<String, Object>> list = new ArrayList<>();
                for (DynamicMedia dynamicMedia : dynamicMediaList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("url", dynamicMedia.getMediaUrl());
                    list.add(map);
                }
                obj.put("list", list);
                String result = JSON.toJSONString(obj);
                PrintWriter writer = response.getWriter();
                writer.write(result);
                writer.flush();
                writer.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }



        /*String rootPath = request.getSession().getServletContext().getRealPath("/");
        try {
            String exec = new ActionEnter(request, rootPath).exec();
            PrintWriter writer = response.getWriter();
            writer.write(exec);
            writer.flush();
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }*/
    }


    public static void main(String[] args) {
        String cloudPath = "https://b.weviva.com.cn:24021/profile/382821e7a9094ae69fce24716b263a4c.png";
        int lastBackslashIndex = cloudPath.lastIndexOf("/");
        String fileNameWithExtension = cloudPath.substring(lastBackslashIndex + 1);
        String[] split = fileNameWithExtension.split("\\.");

        System.out.println("split:     " + split[0] + "              split1:     " + split[1]);
    }
}

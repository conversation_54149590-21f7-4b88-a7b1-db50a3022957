# 数据源配置
spring:
    autoconfigure:
        exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure #忽略druid原生的快速配置类
    datasource:
        dynamic: #动态数据源
            primary: master
            druid:
                # 初始连接数
                initialSize: 2
                # 最小连接池数量
                minIdle: 2
                # 最大连接池数量
                maxActive: 20
                # 配置获取连接等待超时的时间
                maxWait: 60000
                # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
                timeBetweenEvictionRunsMillis: 60000
                # 配置一个连接在池中最小生存的时间，单位是毫秒
                minEvictableIdleTimeMillis: 300000
                # 配置一个连接在池中最大生存的时间，单位是毫秒
                maxEvictableIdleTimeMillis: 900000
                # 配置检测连接是否有效
                validationQuery: SELECT 1 FROM DUAL
                testWhileIdle: true
                testOnBorrow: false
                testOnReturn: false
                webStatFilter:
                    enabled: true
                statViewServlet:
                    enabled: true
                    # 设置白名单，不填则允许所有访问
                    allow:
                    url-pattern: /druid/*
                    # 控制台管理用户名和密码
                    login-username:
                    login-password:
                filter:
                    stat:
                        enabled: true
                        # 慢SQL记录
                        log-slow-sql: true
                        slow-sql-millis: 2000
                        merge-sql: true
                    wall:
                        config:
                            multi-statement-allow: true
            datasource:
                master: #数据源一
                    url: *********************************************************************************************************************************************************************************************************************************** # MySQL Connector/J 5.X 连接的示例
                    username: root
                    password: nFjQJ8xhy70MIXYZ
                    driver-class-name: com.mysql.jdbc.Driver
            #                slave: #数据源二
            #                    url: ***********************************************************************
            #                    username: root
            #                    password: root
            #                    driver-class-name: com.mysql.jdbc.Driver
            mp-enabled: true #避免切换数据源时报NP异常


#友猫应用相关
catapp:
    #请求服务启动地址
    serverUrl: https://wx.youmao.pro
    #过滤无用话题
    filter:
        - 猫超话
        - 猫
        - 猫咪
        - 宠物
        - 动物
        - 狗
        - 热门


#腾讯云配置
qcloud:
    appid:
    secretId:
    secretKey:
    bucketName:
    region:

#DCloud阿里云服务空间
dcloud-aliyun:
    #空间名称非必须
    spaceName:
    spaceId:
    clientSecret:

#微信小程序配置(企业)(推送消息)
weixin:
    appid:
    secret:
    #评论消息模板id
    template:
        commentId: xXQvkooGsgV1CcrSEKv6EWjheUIrHzv_Gqf82fhtnvA
        praiseId: LMdrpQtijaj_joGMYnmkoB1lRqlJwwUZo6S-wfxjAis
        userAttentionId: WHgpDsHj_tzbJi1FZ8zZ6UaFm2mq4kRGQ1gv9UFdoyk

fileTempPath: /srv/www/app/cat_task/contraction/
ffmpegEXE: /opt/install/ffmpeg-git-20200803-amd64-static/ffmpeg

#个推消息推送配置
getui:
    appId:
    appKey:
    masterSecret:
    packageName:
logging:
    config: classpath:logback.xml
    level:
        com.jeefast.cat: debug

yueyue:
    url: https://t.weviva.com.cn:14021/
    user_id: J3YMmtZSbHD6fF0ie0hhMhhjT35pFShw
    secret: 8WTD9nDrFcrWFHACeRJBNZB5TDwftH1d
    live:
        finderUserName: sphVAD7Dg5oCNkg
        originMiniId: gh_6471feec127d

dynamic:
  finderUserName: sphVAD7Dg5oCNkg
  originMiniId: gh_6471feec127d
  attentionPubDate: '2025-03-31 12:00:00'
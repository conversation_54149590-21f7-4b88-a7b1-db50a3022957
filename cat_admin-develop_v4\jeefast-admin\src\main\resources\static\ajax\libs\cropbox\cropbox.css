@charset "utf-8";
.container {
	margin: 10px auto 0 auto;
	position: relative;
	font-family: 微软雅黑;
	font-size: 12px;
}
.container p {
	line-height: 12px;
	line-height: 0px;
	height: 0px;
	margin: 10px;
	color: #bbb
}
.action {
	width: 400px;
	height: 30px;
	margin: 10px 0;
}
.cropped {
	position: absolute;
	left: 500px;
	top: 0;
	width: 200px;
	border: 1px #ddd solid;
	height: 450px;
	padding: 4px;
	box-shadow: 0px 0px 12px #ddd;
	text-align: center;
}
.imageBox {
	position: relative;
	height: 400px;
	width: 400px;
	border: 1px solid #aaa;
	background: #fff;
	overflow: hidden;
	background-repeat: no-repeat;
	cursor: move;
	box-shadow: 4px 4px 12px #B0B0B0; 
}
.imageBox .thumbBox {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 200px;
	height: 200px;
	margin-top: -100px;
	margin-left: -100px;
	box-sizing: border-box;
	border: 1px solid rgb(102, 102, 102);
	box-shadow: 0 0 0 1000px rgba(0, 0, 0, 0.5);
	background: none repeat scroll 0% 0% transparent;
}
.imageBox .spinner {
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	text-align: center;
	line-height: 400px;
	background: rgba(0,0,0,0.7);
}
.Btnsty_peyton{ float: right;
  width: 46px;
  display: inline-block;
  margin-bottom: 10px;
  height: 37px;
  line-height: 37px;
  font-size: 14px;
  color: #FFFFFF;
  margin:0px 2px;
  background-color: #f38e81;
  border-radius: 3px;
  text-decoration: none;
  cursor: pointer;
  box-shadow: 0px 0px 5px #B0B0B0;
  border: 0px #fff solid;}
/*选择文件上传*/
.new-contentarea {
	width: 165px;
	overflow:hidden;
	margin: 0 auto;
	position:relative;float:left;
}
.new-contentarea label {
	width:100%;
	height:100%;
	display:block;
}
.new-contentarea input[type=file] {
	width:188px;
	height:60px;
	background:#333;
	margin: 0 auto;
	position:absolute;
	right:50%;
	margin-right:-94px;
	top:0;
	right/*\**/:0px\9;
	margin-right/*\**/:0px\9;
	width/*\**/:10px\9;
	opacity:0;
	filter:alpha(opacity=0);
	z-index:2;
}
a.upload-img{
	width:165px;
	display: inline-block;
	margin-bottom: 10px;
	height:37px;
	line-height: 37px;
	font-size: 14px;
	color: #FFFFFF;
	background-color: #f38e81;
	border-radius: 3px;
	text-decoration:none;
	cursor:pointer;
	border: 0px #fff solid;
	box-shadow: 0px 0px 5px #B0B0B0;
}
a.upload-img:hover{
	background-color: #ec7e70;
}

.tc{text-align:center;}

/*! Summernote v0.8.11 | (c) 2013- <PERSON> and other contributors | MIT license */

(function(b,a){typeof exports==="object"&&typeof module!=="undefined"?a(require("jquery")):typeof define==="function"&&define.amd?define(["jquery"],a):(b=b||self,a(b.jQuery))}(this,function(a3){a3=a3&&a3.hasOwnProperty("default")?a3["default"]:a3;var b6=(function(){function e(co,cq,cp,cr){this.markup=co;this.children=cq;this.options=cp;this.callback=cr}e.prototype.render=function(cq){var cp=a3(this.markup);if(this.options&&this.options.contents){cp.html(this.options.contents)}if(this.options&&this.options.className){cp.addClass(this.options.className)}if(this.options&&this.options.data){a3.each(this.options.data,function(cs,cr){cp.attr("data-"+cs,cr)})}if(this.options&&this.options.click){cp.on("click",this.options.click)}if(this.children){var co=cp.find(".note-children-container");this.children.forEach(function(cr){cr.render(co.length?co:cp)})}if(this.callback){this.callback(cp,this.options)}if(this.options&&this.options.callback){this.options.callback(cp)}if(cq){cq.append(cp)}return cp};return e}());var aN={create:function(e,co){return function(){var cp=typeof arguments[1]==="object"?arguments[1]:arguments[0];var cq=a3.isArray(arguments[0])?arguments[0]:[];if(cp&&cp.children){cq=cp.children}return new b6(e,cq,cp,co)}}};var M=aN.create('<div class="note-editor note-frame panel panel-default"/>');var k=aN.create('<div class="note-toolbar panel-heading" role="toolbar"></div></div>');var ag=aN.create('<div class="note-editing-area"/>');var X=aN.create('<textarea class="note-codable" role="textbox" aria-multiline="true"/>');var a1=aN.create('<div class="note-editable" contentEditable="true" role="textbox" aria-multiline="true"/>');var b0=aN.create(['<output class="note-status-output" aria-live="polite"/>','<div class="note-statusbar" role="status">','  <div class="note-resizebar" role="seperator" aria-orientation="horizontal" aria-label="Resize">','    <div class="note-icon-bar"/>','    <div class="note-icon-bar"/>','    <div class="note-icon-bar"/>',"  </div>","</div>",].join(""));var a7=aN.create('<div class="note-editor"/>');var cn=aN.create(['<div class="note-editable" contentEditable="true" role="textbox" aria-multiline="true"/>','<output class="note-status-output" aria-live="polite"/>',].join(""));var w=aN.create('<div class="note-btn-group btn-group">');var bL=aN.create('<ul class="dropdown-menu" role="list">',function(co,cp){var e=a3.isArray(cp.items)?cp.items.map(function(cs){var cu=(typeof cs==="string")?cs:(cs.value||"");var cr=cp.template?cp.template(cs):cs;var cq=(typeof cs==="object")?cs.option:undefined;var cv='data-value="'+cu+'"';var ct=(cq!==undefined)?' data-option="'+cq+'"':"";return'<li role="listitem" aria-label="'+cs+'"><a href="#" '+(cv+ct)+">"+cr+"</a></li>"}).join(""):cp.items;co.html(e).attr({"aria-label":cp.title})});var b2=function(co,e){return co+" "+s(e.icons.caret,"span")};var cb=aN.create('<ul class="dropdown-menu note-check" role="list">',function(co,cp){var e=a3.isArray(cp.items)?cp.items.map(function(cr){var cs=(typeof cr==="string")?cr:(cr.value||"");var cq=cp.template?cp.template(cr):cr;return'<li role="listitem" aria-label="'+cr+'"><a href="#" data-value="'+cs+'">'+s(cp.checkClassName)+" "+cq+"</a></li>"}).join(""):cp.items;co.html(e).attr({"aria-label":cp.title})});var J=aN.create('<div class="note-color-palette"/>',function(co,cz){var cq=[];for(var cy=0,cv=cz.colors.length;cy<cv;cy++){var cs=cz.eventName;var e=cz.colors[cy];var cw=cz.colorsName[cy];var ct=[];for(var cp=0,cu=e.length;cp<cu;cp++){var cr=e[cp];var cx=cw[cp];ct.push(['<button type="button" class="note-color-btn"','style="background-color:',cr,'" ','data-event="',cs,'" ','data-value="',cr,'" ','title="',cx,'" ','aria-label="',cx,'" ','data-toggle="button" tabindex="-1"></button>',].join(""))}cq.push('<div class="note-color-row">'+ct.join("")+"</div>")}co.html(cq.join(""));if(cz.tooltip){co.find(".note-color-btn").tooltip({container:cz.container,trigger:"hover",placement:"bottom"})}});var b4=aN.create('<div class="modal" aria-hidden="false" tabindex="-1" role="dialog"/>',function(e,co){if(co.fade){e.addClass("fade")}e.attr({"aria-label":co.title});e.html(['<div class="modal-dialog">','  <div class="modal-content">',(co.title?'    <div class="modal-header">'+'      <button type="button" class="close" data-dismiss="modal" aria-label="Close" aria-hidden="true">&times;</button>'+'      <h4 class="modal-title">'+co.title+"</h4>"+"    </div>":""),'    <div class="modal-body">'+co.body+"</div>",(co.footer?'    <div class="modal-footer">'+co.footer+"</div>":""),"  </div>","</div>",].join(""))});var f=aN.create(['<div class="note-popover popover in">','  <div class="arrow"/>','  <div class="popover-content note-children-container"/>',"</div>",].join(""),function(e,co){var cp=typeof co.direction!=="undefined"?co.direction:"bottom";e.addClass(cp);if(co.hideArrow){e.find(".arrow").hide()}});var aP=aN.create('<div class="checkbox"></div>',function(e,co){e.html(["<label"+(co.id?' for="'+co.id+'"':"")+">",' <input role="checkbox" type="checkbox"'+(co.id?' id="'+co.id+'"':""),(co.checked?" checked":""),' aria-checked="'+(co.checked?"true":"false")+'"/>',(co.text?co.text:""),"</label>",].join(""))});var s=function(co,e){e=e||"i";return"<"+e+' class="'+co+'"/>'};var S={editor:M,toolbar:k,editingArea:ag,codable:X,editable:a1,statusbar:b0,airEditor:a7,airEditable:cn,buttonGroup:w,dropdown:bL,dropdownButtonContents:b2,dropdownCheck:cb,palette:J,dialog:b4,popover:f,checkbox:aP,icon:s,options:{},button:function(e,co){return aN.create('<button type="button" class="note-btn btn btn-default btn-sm" role="button" tabindex="-1">',function(cp,cq){if(cq&&cq.tooltip){cp.attr({title:cq.tooltip,"aria-label":cq.tooltip}).tooltip({container:(cq.container!==undefined)?cq.container:"body",trigger:"hover",placement:"bottom"}).on("click",function(cr){a3(cr.currentTarget).tooltip("hide")})}})(e,co)},toggleBtn:function(co,e){co.toggleClass("disabled",!e);co.attr("disabled",!e)},toggleBtnActive:function(co,e){co.toggleClass("active",e)},onDialogShown:function(co,e){co.one("shown.bs.modal",e)},onDialogHidden:function(co,e){co.one("hidden.bs.modal",e)},showDialog:function(e){e.modal("show")},hideDialog:function(e){e.modal("hide")},createLayout:function(e,co){var cp=(co.airMode?S.airEditor([S.editingArea([S.airEditable(),]),]):S.editor([S.toolbar(),S.editingArea([S.codable(),S.editable(),]),S.statusbar(),])).render();cp.insertAfter(e);return{note:e,editor:cp,toolbar:cp.find(".note-toolbar"),editingArea:cp.find(".note-editing-area"),editable:cp.find(".note-editable"),codable:cp.find(".note-codable"),statusbar:cp.find(".note-statusbar")}},removeLayout:function(e,co){e.html(co.editable.html());co.editor.remove();e.show()}};a3.summernote=a3.summernote||{lang:{}};a3.extend(a3.summernote.lang,{"en-US":{font:{bold:"Bold",italic:"Italic",underline:"Underline",clear:"Remove Font Style",height:"Line Height",name:"Font Family",strikethrough:"Strikethrough",subscript:"Subscript",superscript:"Superscript",size:"Font Size"},image:{image:"Picture",insert:"Insert Image",resizeFull:"Resize full",resizeHalf:"Resize half",resizeQuarter:"Resize quarter",resizeNone:"Original size",floatLeft:"Float Left",floatRight:"Float Right",floatNone:"Remove float",shapeRounded:"Shape: Rounded",shapeCircle:"Shape: Circle",shapeThumbnail:"Shape: Thumbnail",shapeNone:"Shape: None",dragImageHere:"Drag image or text here",dropImage:"Drop image or Text",selectFromFiles:"Select from files",maximumFileSize:"Maximum file size",maximumFileSizeError:"Maximum file size exceeded.",url:"Image URL",remove:"Remove Image",original:"Original"},video:{video:"Video",videoLink:"Video Link",insert:"Insert Video",url:"Video URL",providers:"(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)"},link:{link:"Link",insert:"Insert Link",unlink:"Unlink",edit:"Edit",textToDisplay:"Text to display",url:"To what URL should this link go?",openInNewWindow:"Open in new window"},table:{table:"Table",addRowAbove:"Add row above",addRowBelow:"Add row below",addColLeft:"Add column left",addColRight:"Add column right",delRow:"Delete row",delCol:"Delete column",delTable:"Delete table"},hr:{insert:"Insert Horizontal Rule"},style:{style:"Style",p:"Normal",blockquote:"Quote",pre:"Code",h1:"Header 1",h2:"Header 2",h3:"Header 3",h4:"Header 4",h5:"Header 5",h6:"Header 6"},lists:{unordered:"Unordered list",ordered:"Ordered list"},options:{help:"Help",fullscreen:"Full Screen",codeview:"Code View"},paragraph:{paragraph:"Paragraph",outdent:"Outdent",indent:"Indent",left:"Align left",center:"Align center",right:"Align right",justify:"Justify full"},color:{recent:"Recent Color",more:"More Color",background:"Background Color",foreground:"Foreground Color",transparent:"Transparent",setTransparent:"Set transparent",reset:"Reset",resetToDefault:"Reset to default",cpSelect:"Select"},shortcut:{shortcuts:"Keyboard shortcuts",close:"Close",textFormatting:"Text formatting",action:"Action",paragraphFormatting:"Paragraph formatting",documentStyle:"Document Style",extraKeys:"Extra keys"},help:{"insertParagraph":"Insert Paragraph","undo":"Undoes the last command","redo":"Redoes the last command","tab":"Tab","untab":"Untab","bold":"Set a bold style","italic":"Set a italic style","underline":"Set a underline style","strikethrough":"Set a strikethrough style","removeFormat":"Clean a style","justifyLeft":"Set left align","justifyCenter":"Set center align","justifyRight":"Set right align","justifyFull":"Set full align","insertUnorderedList":"Toggle unordered list","insertOrderedList":"Toggle ordered list","outdent":"Outdent on current paragraph","indent":"Indent on current paragraph","formatPara":"Change current block's format as a paragraph(P tag)","formatH1":"Change current block's format as H1","formatH2":"Change current block's format as H2","formatH3":"Change current block's format as H3","formatH4":"Change current block's format as H4","formatH5":"Change current block's format as H5","formatH6":"Change current block's format as H6","insertHorizontalRule":"Insert horizontal rule","linkDialog.show":"Show Link Dialog"},history:{undo:"Undo",redo:"Redo"},specialChar:{specialChar:"SPECIAL CHARACTERS",select:"Select Special characters"}}});var aK=typeof define==="function"&&define.amd;function b1(cs){var cu=cs==="Comic Sans MS"?"Courier New":"Comic Sans MS";var cr="mmmmmmmmmmwwwww";var e="200px";var co=document.createElement("canvas");var cp=co.getContext("2d");cp.font=e+" '"+cu+"'";var ct=cp.measureText(cr).width;cp.font=e+" '"+cs+"', '"+cu+"'";var cq=cp.measureText(cr).width;return ct!==cq}var A=navigator.userAgent;var ci=/MSIE|Trident/i.test(A);var bT;if(ci){var bW=/MSIE (\d+[.]\d+)/.exec(A);if(bW){bT=parseFloat(bW[1])}bW=/Trident\/.*rv:([0-9]{1,}[.0-9]{0,})/.exec(A);if(bW){bT=parseFloat(bW[1])}}var p=/Edge\/\d+/.test(A);var aa=!!window.CodeMirror;if(!aa&&aK){if(typeof __webpack_require__==="function"){try{require.resolve("codemirror");aa=true}catch(bs){}}else{if(typeof require!=="undefined"){if(typeof require.resolve!=="undefined"){try{require.resolve("codemirror");aa=true}catch(bs){}}else{if(typeof require.specified!=="undefined"){aa=require.specified("codemirror")}}}}}var d=(("ontouchstart" in window)||(navigator.MaxTouchPoints>0)||(navigator.msMaxTouchPoints>0));var at=(ci||p)?"DOMCharacterDataModified DOMSubtreeModified DOMNodeInserted":"input";var aj={isMac:navigator.appVersion.indexOf("Mac")>-1,isMSIE:ci,isEdge:p,isFF:!p&&/firefox/i.test(A),isPhantom:/PhantomJS/i.test(A),isWebkit:!p&&/webkit/i.test(A),isChrome:!p&&/chrome/i.test(A),isSafari:!p&&/safari/i.test(A),browserVersion:bT,jqueryVersion:parseFloat(a3.fn.jquery),isSupportAmd:aK,isSupportTouch:d,hasCodeMirror:aa,isFontInstalled:b1,isW3CRangeSupport:!!document.createRange,inputEventName:at};function aL(e){return function(co){return e===co}}function ab(co,e){return co===e}function aQ(e){return function(cp,co){return cp[e]===co[e]}}function bt(){return true}function b7(){return false}function bp(e){return function(){return !e.apply(e,arguments)}}function ax(co,e){return function(cp){return co(cp)&&e(cp)}}function aE(e){return e}function C(e,co){return function(){return e[co].apply(e,arguments)}}var bb=0;function bn(e){var co=++bb+"";return e?e+co:co}function aY(e){var co=$(document);return{top:e.top+co.scrollTop(),left:e.left+co.scrollLeft(),width:e.right-e.left,height:e.bottom-e.top}}function ar(cp){var e={};for(var co in cp){if(cp.hasOwnProperty(co)){e[cp[co]]=co}}return e}function bG(e,co){co=co||"";return co+e.split(".").map(function(cp){return cp.substring(0,1).toUpperCase()+cp.substring(1)}).join("")}function bM(co,cq,e){var cp;return function(){var cu=this;var ct=arguments;var cs=function(){cp=null;if(!e){co.apply(cu,ct)}};var cr=e&&!cp;clearTimeout(cp);cp=setTimeout(cs,cq);if(cr){co.apply(cu,ct)}}}function bq(e){var co=/[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/gi;return co.test(e)}var am={eq:aL,eq2:ab,peq2:aQ,ok:bt,fail:b7,self:aE,not:bp,and:ax,invoke:C,uniqueId:bn,rect2bnd:aY,invertObject:ar,namespaceToCamel:bG,debounce:bM,isValidUrl:bq};function a(e){return e[0]}function au(e){return e[e.length-1]}function cf(e){return e.slice(0,e.length-1)}function cj(e){return e.slice(1)}function aB(cr,cp){for(var co=0,e=cr.length;co<e;co++){var cq=cr[co];if(cp(cq)){return cq}}}function aS(cq,cp){for(var co=0,e=cq.length;co<e;co++){if(!cp(cq[co])){return false}}return true}function cc(co,e){return a3.inArray(e,co)}function Y(co,e){return cc(co,e)!==-1}function bi(co,e){e=e||am.self;return co.reduce(function(cq,cp){return cq+e(cp)},0)}function bJ(cq){var co=[];var cp=cq.length;var e=-1;while(++e<cp){co[e]=cq[e]}return co}function bw(e){return !e||!e.length}function F(cp,e){if(!cp.length){return[]}var co=cj(cp);return co.reduce(function(cs,cr){var cq=au(cs);if(e(au(cq),cr)){cq[cq.length]=cr}else{cs[cs.length]=[cr]}return cs},[[a(cp)]])}function H(cq){var cp=[];for(var co=0,e=cq.length;co<e;co++){if(cq[co]){cp.push(cq[co])}}return cp}function a2(cq){var cp=[];for(var co=0,e=cq.length;co<e;co++){if(!Y(cp,cq[co])){cp.push(cq[co])}}return cp}function ap(cp,co){var e=cc(cp,co);if(e===-1){return null}return cp[e+1]}function by(cp,co){var e=cc(cp,co);if(e===-1){return null}return cp[e-1]}var n={head:a,last:au,initial:cf,tail:cj,prev:by,next:ap,find:aB,contains:Y,all:aS,sum:bi,from:bJ,isEmpty:bw,clusterBy:F,compact:H,unique:a2};var aU=String.fromCharCode(160);var Z="\ufeff";function bV(e){return e&&a3(e).hasClass("note-editable")}function b5(e){return e&&a3(e).hasClass("note-control-sizing")}function bC(e){e=e.toUpperCase();return function(co){return co&&co.nodeName.toUpperCase()===e}}function ao(e){return e&&e.nodeType===3}function a4(e){return e&&e.nodeType===1}function cm(e){return e&&/^BR|^IMG|^HR|^IFRAME|^BUTTON|^INPUT|^AUDIO|^VIDEO|^EMBED/.test(e.nodeName.toUpperCase())}function v(e){if(bV(e)){return false}return e&&/^DIV|^P|^LI|^H[1-7]/.test(e.nodeName.toUpperCase())}function bX(e){return e&&/^H[1-7]/.test(e.nodeName.toUpperCase())}var u=bC("PRE");var bI=bC("LI");function be(e){return v(e)&&!bI(e)}var bu=bC("TABLE");var af=bC("DATA");function aT(e){return !K(e)&&!h(e)&&!r(e)&&!v(e)&&!bu(e)&&!ak(e)&&!af(e)}function h(e){return e&&/^UL|^OL/.test(e.nodeName.toUpperCase())}var r=bC("HR");function bl(e){return e&&/^TD|^TH/.test(e.nodeName.toUpperCase())}var ak=bC("BLOCKQUOTE");function K(e){return bl(e)||ak(e)||bV(e)}var cl=bC("A");function bQ(e){return aT(e)&&!!ac(e,v)}function Q(e){return aT(e)&&!ac(e,v)}var b=bC("BODY");function ca(co,e){return co.nextSibling===e||co.previousSibling===e}function j(co,e){e=e||am.ok;var cp=[];if(co.previousSibling&&e(co.previousSibling)){cp.push(co.previousSibling)}cp.push(co);if(co.nextSibling&&e(co.nextSibling)){cp.push(co.nextSibling)}return cp}var bS=aj.isMSIE&&aj.browserVersion<11?"&nbsp;":"<br>";function br(e){if(ao(e)){return e.nodeValue.length}if(e){return e.childNodes.length}return 0}function W(co){var e=br(co);if(e===0){return true}else{if(!ao(co)&&e===1&&co.innerHTML===bS){return true}else{if(n.all(co.childNodes,ao)&&co.innerHTML===""){return true}}}return false}function bK(e){if(!cm(e)&&!br(e)){e.innerHTML=bS}}function ac(co,e){while(co){if(e(co)){return co}if(bV(co)){break}co=co.parentNode}return null}function b9(co,e){co=co.parentNode;while(co){if(br(co)!==1){break}if(e(co)){return co}if(bV(co)){break}co=co.parentNode}return null}function bU(cp,e){e=e||am.fail;var co=[];ac(cp,function(cq){if(!bV(cq)){co.push(cq)}return e(cq)});return co}function V(cp,e){var co=bU(cp);return n.last(co.filter(e))}function aX(cp,co){var e=bU(cp);for(var cq=co;cq;cq=cq.parentNode){if(a3.inArray(cq,e)>-1){return cq}}return null}function bZ(cp,co){co=co||am.fail;var e=[];while(cp){if(co(cp)){break}e.push(cp);cp=cp.previousSibling}return e}function aJ(cp,co){co=co||am.fail;var e=[];while(cp){if(co(cp)){break}e.push(cp);cp=cp.nextSibling}return e}function z(co,e){var cq=[];e=e||am.ok;(function cp(ct){if(co!==ct&&e(ct)){cq.push(ct)}for(var cs=0,cr=ct.childNodes.length;cs<cr;cs++){cp(ct.childNodes[cs])}})(co);return cq}function aV(cp,co){var e=cp.parentNode;var cq=a3("<"+co+">")[0];e.insertBefore(cq,cp);cq.appendChild(cp);return cq}function aC(cq,cp){var co=cp.nextSibling;var e=cp.parentNode;if(co){e.insertBefore(cq,co)}else{e.appendChild(cq)}return cq}function a6(e,co){a3.each(co,function(cp,cq){e.appendChild(cq)});return e}function aG(e){return e.offset===0}function an(e){return e.offset===br(e.node)}function a9(e){return aG(e)||an(e)}function cd(co,e){while(co&&co!==e){if(aW(co)!==0){return false}co=co.parentNode}return true}function ay(co,e){if(!e){return false}while(co&&co!==e){if(aW(co)!==br(co.parentNode)-1){return false}co=co.parentNode}return true}function R(e,co){return aG(e)&&cd(e.node,co)}function aR(e,co){return an(e)&&ay(e.node,co)}function aW(e){var co=0;while((e=e.previousSibling)){co+=1}return co}function bg(e){return !!(e&&e.childNodes&&e.childNodes.length)}function bA(e,co){var cp;var cq;if(e.offset===0){if(bV(e.node)){return null}cp=e.node.parentNode;cq=aW(e.node)}else{if(bg(e.node)){cp=e.node.childNodes[e.offset-1];cq=br(cp)}else{cp=e.node;cq=co?0:e.offset-1}}return{node:cp,offset:cq}}function P(e,co){var cp,cq;if(br(e.node)===e.offset){if(bV(e.node)){return null}cp=e.node.parentNode;cq=aW(e.node)+1}else{if(bg(e.node)){cp=e.node.childNodes[e.offset];cq=0}else{cp=e.node;cq=co?br(e.node):e.offset+1}}return{node:cp,offset:cq}}function E(e,co){return e.node===co.node&&e.offset===co.offset}function b8(e){if(ao(e.node)||!bg(e.node)||W(e.node)){return true}var cp=e.node.childNodes[e.offset-1];var co=e.node.childNodes[e.offset];if((!cp||cm(cp))&&(!co||cm(co))){return true}return false}function O(e,co){while(e){if(co(e)){return e}e=bA(e)}return null}function bN(e,co){while(e){if(co(e)){return e}e=P(e)}return null}function ba(e){if(!ao(e.node)){return false}var co=e.node.nodeValue.charAt(e.offset-1);return co&&(co!==" "&&co!==aU)}function b3(cs,cp,cq,co){var e=cs;while(e){cq(e);if(E(e,cp)){break}var cr=co&&cs.node!==e.node&&cp.node!==e.node;e=P(e,cr)}}function az(e,cp){var co=bU(cp,am.eq(e));return co.map(aW).reverse()}function bP(cp,cq){var cr=cp;for(var co=0,e=cq.length;co<e;co++){if(cr.childNodes.length<=cq[co]){cr=cr.childNodes[cr.childNodes.length-1]}else{cr=cr.childNodes[cq[co]]}}return cr}function q(e,cp){var cs=cp&&cp.isSkipPaddingBlankHTML;var co=cp&&cp.isNotSplitEdgePoint;var cr=cp&&cp.isDiscardEmptySplits;if(cr){cs=true}if(a9(e)&&(ao(e.node)||co)){if(aG(e)){return e.node}else{if(an(e)){return e.node.nextSibling}}}if(ao(e.node)){return e.node.splitText(e.offset)}else{var cq=e.node.childNodes[e.offset];var ct=aC(e.node.cloneNode(false),e.node);a6(ct,aJ(cq));if(!cs){bK(e.node);bK(ct)}if(cr){if(W(e.node)){G(e.node)}if(W(ct)){G(ct);return e.node.nextSibling}}return ct}}function bE(co,e,cp){var cq=bU(e.node,am.eq(co));if(!cq.length){return null}else{if(cq.length===1){return q(e,cp)}}return cq.reduce(function(cs,cr){if(cs===e.node){cs=q(e,cp)}return q({node:cr,offset:cs?aW(cs):br(cr)},cp)})}function bH(e,cs){var cq=cs?v:K;var cr=bU(e.node,cq);var cu=n.last(cr)||e.node;var ct,cp;if(cq(cu)){ct=cr[cr.length-2];cp=cu}else{ct=cu;cp=ct.parentNode}var co=ct&&bE(ct,e,{isSkipPaddingBlankHTML:cs,isNotSplitEdgePoint:cs});if(!co&&cp===e.node){co=e.node.childNodes[e.offset]}return{rightNode:co,container:cp}}function L(e){return document.createElement(e)}function ai(e){return document.createTextNode(e)}function G(cr,cs){if(!cr||!cr.parentNode){return}if(cr.removeNode){return cr.removeNode(cs)}var cq=cr.parentNode;if(!cs){var co=[];for(var cp=0,e=cr.childNodes.length;cp<e;cp++){co.push(cr.childNodes[cp])}for(var cp=0,e=co.length;cp<e;cp++){cq.insertBefore(co[cp],cr)}}cq.removeChild(cr)}function bf(cp,e){while(cp){if(bV(cp)||!e(cp)){break}var co=cp.parentNode;G(cp);cp=co}}function a0(co,cp){if(co.nodeName.toUpperCase()===cp.toUpperCase()){return co}var e=L(cp);if(co.style.cssText){e.style.cssText=co.style.cssText}a6(e,n.from(co.childNodes));aC(e,co);G(co);return e}var aZ=bC("TEXTAREA");function g(co,e){var cp=aZ(co[0])?co.val():co.html();if(e){return cp.replace(/[\n\r]/g,"")}return cp}function al(co,cp){var e=g(co);if(cp){var cq=/<(\/?)(\b(?!!)[^>\s]*)(.*?)(\s*\/?>)/g;e=e.replace(cq,function(cs,cv,cr){cr=cr.toUpperCase();var cu=/^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(cr)&&!!cv;var ct=/^BLOCKQUOTE|^TABLE|^TBODY|^TR|^HR|^UL|^OL/.test(cr);return cs+((cu||ct)?"\n":"")});e=a3.trim(e)}return e}function bB(cp){var co=a3(cp);var cq=co.offset();var e=co.outerHeight(true);return{left:cq.left,top:cq.top+e}}function bD(e,co){Object.keys(co).forEach(function(cp){e.on(cp,co[cp])})}function bF(e,co){Object.keys(co).forEach(function(cp){e.off(cp,co[cp])})}function U(e){return e&&!ao(e)&&n.contains(e.classList,"note-styletag")}var bO={NBSP_CHAR:aU,ZERO_WIDTH_NBSP_CHAR:Z,blank:bS,emptyPara:"<p>"+bS+"</p>",makePredByNodeName:bC,isEditable:bV,isControlSizing:b5,isText:ao,isElement:a4,isVoid:cm,isPara:v,isPurePara:be,isHeading:bX,isInline:aT,isBlock:am.not(aT),isBodyInline:Q,isBody:b,isParaInline:bQ,isPre:u,isList:h,isTable:bu,isData:af,isCell:bl,isBlockquote:ak,isBodyContainer:K,isAnchor:cl,isDiv:bC("DIV"),isLi:bI,isBR:bC("BR"),isSpan:bC("SPAN"),isB:bC("B"),isU:bC("U"),isS:bC("S"),isI:bC("I"),isImg:bC("IMG"),isTextarea:aZ,isEmpty:W,isEmptyAnchor:am.and(cl,W),isClosestSibling:ca,withClosestSiblings:j,nodeLength:br,isLeftEdgePoint:aG,isRightEdgePoint:an,isEdgePoint:a9,isLeftEdgeOf:cd,isRightEdgeOf:ay,isLeftEdgePointOf:R,isRightEdgePointOf:aR,prevPoint:bA,nextPoint:P,isSamePoint:E,isVisiblePoint:b8,prevPointUntil:O,nextPointUntil:bN,isCharPoint:ba,walkPoint:b3,ancestor:ac,singleChildAncestor:b9,listAncestor:bU,lastAncestor:V,listNext:aJ,listPrev:bZ,listDescendant:z,commonAncestor:aX,wrap:aV,insertAfter:aC,appendChildNodes:a6,position:aW,hasChildren:bg,makeOffsetPath:az,fromOffsetPath:bP,splitTree:bE,splitPoint:bH,create:L,createText:ai,remove:G,removeWhile:bf,replace:a0,html:al,value:g,posFromPlaceholder:bB,attachEvents:bD,detachEvents:bF,isCustomStyleTag:U};var bz=(function(){function e(co,cp){this.ui=a3.summernote.ui;this.$note=co;this.memos={};this.modules={};this.layoutInfo={};this.options=cp;this.initialize()}e.prototype.initialize=function(){this.layoutInfo=this.ui.createLayout(this.$note,this.options);this._initialize();this.$note.hide();return this};e.prototype.destroy=function(){this._destroy();this.$note.removeData("summernote");this.ui.removeLayout(this.$note,this.layoutInfo)};e.prototype.reset=function(){var co=this.isDisabled();this.code(bO.emptyPara);this._destroy();this._initialize();if(co){this.disable()}};e.prototype._initialize=function(){var cq=this;var cp=a3.extend({},this.options.buttons);Object.keys(cp).forEach(function(cr){cq.memo("button."+cr,cp[cr])});var co=a3.extend({},this.options.modules,a3.summernote.plugins||{});Object.keys(co).forEach(function(cr){cq.module(cr,co[cr],true)});Object.keys(this.modules).forEach(function(cr){cq.initializeModule(cr)})};e.prototype._destroy=function(){var co=this;Object.keys(this.modules).reverse().forEach(function(cp){co.removeModule(cp)});Object.keys(this.memos).forEach(function(cp){co.removeMemo(cp)});this.triggerEvent("destroy",this)};e.prototype.code=function(cp){var co=this.invoke("codeview.isActivated");if(cp===undefined){this.invoke("codeview.sync");return co?this.layoutInfo.codable.val():this.layoutInfo.editable.html()}else{if(co){this.layoutInfo.codable.val(cp)}else{this.layoutInfo.editable.html(cp)}this.$note.val(cp);this.triggerEvent("change",cp,this.layoutInfo.editable)}};e.prototype.isDisabled=function(){return this.layoutInfo.editable.attr("contenteditable")==="false"};e.prototype.enable=function(){this.layoutInfo.editable.attr("contenteditable",true);this.invoke("toolbar.activate",true);this.triggerEvent("disable",false)};e.prototype.disable=function(){if(this.invoke("codeview.isActivated")){this.invoke("codeview.deactivate")}this.layoutInfo.editable.attr("contenteditable",false);this.invoke("toolbar.deactivate",true);this.triggerEvent("disable",true)};e.prototype.triggerEvent=function(){var cp=n.head(arguments);var co=n.tail(n.from(arguments));var cq=this.options.callbacks[am.namespaceToCamel(cp,"on")];if(cq){cq.apply(this.$note[0],co)}this.$note.trigger("summernote."+cp,co)};e.prototype.initializeModule=function(cp){var co=this.modules[cp];co.shouldInitialize=co.shouldInitialize||am.ok;if(!co.shouldInitialize()){return}if(co.initialize){co.initialize()}if(co.events){bO.attachEvents(this.$note,co.events)}};e.prototype.module=function(cq,cp,co){if(arguments.length===1){return this.modules[cq]}this.modules[cq]=new cp(this);if(!co){this.initializeModule(cq)}};e.prototype.removeModule=function(cp){var co=this.modules[cp];if(co.shouldInitialize()){if(co.events){bO.detachEvents(this.$note,co.events)}if(co.destroy){co.destroy()}}delete this.modules[cp]};e.prototype.memo=function(co,cp){if(arguments.length===1){return this.memos[co]}this.memos[co]=cp};e.prototype.removeMemo=function(co){if(this.memos[co]&&this.memos[co].destroy){this.memos[co].destroy()}delete this.memos[co]};e.prototype.createInvokeHandlerAndUpdateState=function(co,cp){var cq=this;return function(cr){cq.createInvokeHandler(co,cp)(cr);cq.invoke("buttons.updateCurrentStyle")}};e.prototype.createInvokeHandler=function(co,cp){var cq=this;return function(cs){cs.preventDefault();var cr=a3(cs.target);cq.invoke(co,cp||cr.closest("[data-value]").data("value"),cr)}};e.prototype.invoke=function(){var cs=n.head(arguments);var cq=n.tail(n.from(arguments));var cu=cs.split(".");var ct=cu.length>1;var cp=ct&&n.head(cu);var co=ct?n.last(cu):n.head(cu);var cr=this.modules[cp||"editor"];if(!cp&&this[co]){return this[co].apply(this,cq)}else{if(cr&&cr[co]&&cr.shouldInitialize()){return cr[co].apply(cr,cq)}}};return e}());a3.fn.extend({summernote:function(){var cq=a3.type(n.head(arguments));var cs=cq==="string";var cr=cq==="object";var co=a3.extend({},a3.summernote.options,cr?n.head(arguments):{});co.langInfo=a3.extend(true,{},a3.summernote.lang["en-US"],a3.summernote.lang[co.lang]);co.icons=a3.extend(true,{},a3.summernote.options.icons,co.icons);co.tooltip=co.tooltip==="auto"?!aj.isSupportTouch:co.tooltip;this.each(function(ct,cw){var cu=a3(cw);if(!cu.data("summernote")){var cv=new bz(cu,co);cu.data("summernote",cv);cu.data("summernote").triggerEvent("init",cv.layoutInfo)}});var e=this.first();if(e.length){var cp=e.data("summernote");if(cs){return cp.invoke.apply(cp,n.from(arguments))}else{if(co.focus){cp.invoke("editor.focus")}}}return this}});function ce(cx,e){var cq=cx.parentElement();var cs;var cw=document.body.createTextRange();var cu;var cv=n.from(cq.childNodes);for(cs=0;cs<cv.length;cs++){if(bO.isText(cv[cs])){continue}cw.moveToElementText(cv[cs]);if(cw.compareEndPoints("StartToStart",cx)>=0){break}cu=cv[cs]}if(cs!==0&&bO.isText(cv[cs-1])){var cy=document.body.createTextRange();var ct=null;cy.moveToElementText(cu||cq);cy.collapse(!cu);ct=cu?cu.nextSibling:cq.firstChild;var cp=cx.duplicate();cp.setEndPoint("StartToStart",cy);var co=cp.text.replace(/[\r\n]/g,"").length;while(co>ct.nodeValue.length&&ct.nextSibling){co-=ct.nodeValue.length;ct=ct.nextSibling}var cr=ct.nodeValue;if(e&&ct.nextSibling&&bO.isText(ct.nextSibling)&&co===ct.nodeValue.length){co-=ct.nodeValue.length;ct=ct.nextSibling}cq=ct;cs=co}return{cont:cq,offset:cs}}function bd(e){var cq=function(ct,cw){var cv,cs;if(bO.isText(ct)){var cr=bO.listPrev(ct,am.not(bO.isText));var cu=n.last(cr).previousSibling;cv=cu||ct.parentNode;cw+=n.sum(n.tail(cr),bO.nodeLength);cs=!cu}else{cv=ct.childNodes[cw]||ct;if(bO.isText(cv)){return cq(cv,0)}cw=0;cs=false}return{node:cv,collapseToStart:cs,offset:cw}};var co=document.body.createTextRange();var cp=cq(e.node,e.offset);co.moveToElementText(cp.node);co.collapse(cp.collapseToStart);co.moveStart("character",cp.offset);return co}var ch=(function(){function e(cr,cq,cp,co){this.sc=cr;this.so=cq;this.ec=cp;this.eo=co;this.isOnEditable=this.makeIsOn(bO.isEditable);this.isOnList=this.makeIsOn(bO.isList);this.isOnAnchor=this.makeIsOn(bO.isAnchor);this.isOnCell=this.makeIsOn(bO.isCell);this.isOnData=this.makeIsOn(bO.isData)}e.prototype.nativeRange=function(){if(aj.isW3CRangeSupport){var co=document.createRange();co.setStart(this.sc,this.so);co.setEnd(this.ec,this.eo);return co}else{var cp=bd({node:this.sc,offset:this.so});cp.setEndPoint("EndToEnd",bd({node:this.ec,offset:this.eo}));return cp}};e.prototype.getPoints=function(){return{sc:this.sc,so:this.so,ec:this.ec,eo:this.eo}};e.prototype.getStartPoint=function(){return{node:this.sc,offset:this.so}};e.prototype.getEndPoint=function(){return{node:this.ec,offset:this.eo}};e.prototype.select=function(){var cp=this.nativeRange();if(aj.isW3CRangeSupport){var co=document.getSelection();if(co.rangeCount>0){co.removeAllRanges()}co.addRange(cp)}else{cp.select()}return this};e.prototype.scrollIntoView=function(cp){var co=a3(cp).height();if(cp.scrollTop+co<this.sc.offsetTop){cp.scrollTop+=Math.abs(cp.scrollTop+co-this.sc.offsetTop)}return this};e.prototype.normalize=function(){var cq=function(cr,cs){if((bO.isVisiblePoint(cr)&&!bO.isEdgePoint(cr))||(bO.isVisiblePoint(cr)&&bO.isRightEdgePoint(cr)&&!cs)||(bO.isVisiblePoint(cr)&&bO.isLeftEdgePoint(cr)&&cs)||(bO.isVisiblePoint(cr)&&bO.isBlock(cr.node)&&bO.isEmpty(cr.node))){return cr}var cu=bO.ancestor(cr.node,bO.isBlock);if(((bO.isLeftEdgePointOf(cr,cu)||bO.isVoid(bO.prevPoint(cr).node))&&!cs)||((bO.isRightEdgePointOf(cr,cu)||bO.isVoid(bO.nextPoint(cr).node))&&cs)){if(bO.isVisiblePoint(cr)){return cr}cs=!cs}var ct=cs?bO.nextPointUntil(bO.nextPoint(cr),bO.isVisiblePoint):bO.prevPointUntil(bO.prevPoint(cr),bO.isVisiblePoint);return ct||cr};var co=cq(this.getEndPoint(),false);var cp=this.isCollapsed()?co:cq(this.getStartPoint(),true);return new e(cp.node,cp.offset,co.node,co.offset)};e.prototype.nodes=function(cq,cr){cq=cq||am.ok;var co=cr&&cr.includeAncestor;var cv=cr&&cr.fullyContains;var ct=this.getStartPoint();var cs=this.getEndPoint();var cp=[];var cu=[];bO.walkPoint(ct,cs,function(cw){if(bO.isEditable(cw.node)){return}var cx;if(cv){if(bO.isLeftEdgePoint(cw)){cu.push(cw.node)}if(bO.isRightEdgePoint(cw)&&n.contains(cu,cw.node)){cx=cw.node}}else{if(co){cx=bO.ancestor(cw.node,cq)}else{cx=cw.node}}if(cx&&cq(cx)){cp.push(cx)}},true);return n.unique(cp)};e.prototype.commonAncestor=function(){return bO.commonAncestor(this.sc,this.ec)};e.prototype.expand=function(co){var cq=bO.ancestor(this.sc,co);var cr=bO.ancestor(this.ec,co);if(!cq&&!cr){return new e(this.sc,this.so,this.ec,this.eo)}var cp=this.getPoints();if(cq){cp.sc=cq;cp.so=0}if(cr){cp.ec=cr;cp.eo=bO.nodeLength(cr)}return new e(cp.sc,cp.so,cp.ec,cp.eo)};e.prototype.collapse=function(co){if(co){return new e(this.sc,this.so,this.sc,this.so)}else{return new e(this.ec,this.eo,this.ec,this.eo)}};e.prototype.splitText=function(){var co=this.sc===this.ec;var cp=this.getPoints();if(bO.isText(this.ec)&&!bO.isEdgePoint(this.getEndPoint())){this.ec.splitText(this.eo)}if(bO.isText(this.sc)&&!bO.isEdgePoint(this.getStartPoint())){cp.sc=this.sc.splitText(this.so);cp.so=0;if(co){cp.ec=cp.sc;cp.eo=this.eo-this.so}}return new e(cp.sc,cp.so,cp.ec,cp.eo)};e.prototype.deleteContents=function(){if(this.isCollapsed()){return this}var cp=this.splitText();var cq=cp.nodes(null,{fullyContains:true});var co=bO.prevPointUntil(cp.getStartPoint(),function(cs){return !n.contains(cq,cs.node)});var cr=[];a3.each(cq,function(cs,cu){var ct=cu.parentNode;if(co.node!==ct&&bO.nodeLength(ct)===1){cr.push(ct)}bO.remove(cu,false)});a3.each(cr,function(cs,ct){bO.remove(ct,false)});return new e(co.node,co.offset,co.node,co.offset).normalize()};e.prototype.makeIsOn=function(co){return function(){var cp=bO.ancestor(this.sc,co);return !!cp&&(cp===bO.ancestor(this.ec,co))}};e.prototype.isLeftEdgeOf=function(co){if(!bO.isLeftEdgePoint(this.getStartPoint())){return false}var cp=bO.ancestor(this.sc,co);return cp&&bO.isLeftEdgeOf(this.sc,cp)};e.prototype.isCollapsed=function(){return this.sc===this.ec&&this.so===this.eo};e.prototype.wrapBodyInlineWithPara=function(){if(bO.isBodyContainer(this.sc)&&bO.isEmpty(this.sc)){this.sc.innerHTML=bO.emptyPara;return new e(this.sc.firstChild,0,this.sc.firstChild,0)}var cp=this.normalize();if(bO.isParaInline(this.sc)||bO.isPara(this.sc)){return cp}var cs;if(bO.isInline(cp.sc)){var cq=bO.listAncestor(cp.sc,am.not(bO.isInline));cs=n.last(cq);if(!bO.isInline(cs)){cs=cq[cq.length-2]||cp.sc.childNodes[cp.so]}}else{cs=cp.sc.childNodes[cp.so>0?cp.so-1:0]}var cr=bO.listPrev(cs,bO.isParaInline).reverse();cr=cr.concat(bO.listNext(cs.nextSibling,bO.isParaInline));if(cr.length){var co=bO.wrap(n.head(cr),"p");bO.appendChildNodes(co,n.tail(cr))}return this.normalize()};e.prototype.insertNode=function(cp){var co=this.wrapBodyInlineWithPara().deleteContents();var cq=bO.splitPoint(co.getStartPoint(),bO.isInline(cp));if(cq.rightNode){cq.rightNode.parentNode.insertBefore(cp,cq.rightNode)}else{cq.container.appendChild(cp)}return cp};e.prototype.pasteHTML=function(cq){var cp=a3("<div></div>").html(cq)[0];var cr=n.from(cp.childNodes);var co=this.wrapBodyInlineWithPara().deleteContents();if(co.so>0){cr=cr.reverse()}cr=cr.map(function(cs){return co.insertNode(cs)});if(co.so>0){cr=cr.reverse()}return cr};e.prototype.toString=function(){var co=this.nativeRange();return aj.isW3CRangeSupport?co.toString():co.text};e.prototype.getWordRange=function(cq){var co=this.getEndPoint();if(!bO.isCharPoint(co)){return this}var cp=bO.prevPointUntil(co,function(cr){return !bO.isCharPoint(cr)});if(cq){co=bO.nextPointUntil(co,function(cr){return !bO.isCharPoint(cr)})}return new e(cp.node,cp.offset,co.node,co.offset)};e.prototype.bookmark=function(co){return{s:{path:bO.makeOffsetPath(co,this.sc),offset:this.so},e:{path:bO.makeOffsetPath(co,this.ec),offset:this.eo}}};e.prototype.paraBookmark=function(co){return{s:{path:n.tail(bO.makeOffsetPath(n.head(co),this.sc)),offset:this.so},e:{path:n.tail(bO.makeOffsetPath(n.last(co),this.ec)),offset:this.eo}}};e.prototype.getClientRects=function(){var co=this.nativeRange();return co.getClientRects()};return e}());var T={create:function(cr,cq,cp,co){if(arguments.length===4){return new ch(cr,cq,cp,co)}else{if(arguments.length===2){cp=cr;co=cq;return new ch(cr,cq,cp,co)}else{var e=this.createFromSelection();if(!e&&arguments.length===1){e=this.createFromNode(arguments[0]);return e.collapse(bO.emptyPara===arguments[0].innerHTML)}return e}}},createFromSelection:function(){var cs,cp,cr,e;if(aj.isW3CRangeSupport){var cv=document.getSelection();if(!cv||cv.rangeCount===0){return null}else{if(bO.isBody(cv.anchorNode)){return null}}var cu=cv.getRangeAt(0);cs=cu.startContainer;cp=cu.startOffset;cr=cu.endContainer;e=cu.endOffset}else{var cx=document.selection.createRange();var cq=cx.duplicate();cq.collapse(false);var cw=cx;cw.collapse(true);var co=ce(cw,true);var ct=ce(cq,false);if(bO.isText(co.node)&&bO.isLeftEdgePoint(co)&&bO.isTextNode(ct.node)&&bO.isRightEdgePoint(ct)&&ct.node.nextSibling===co.node){co=ct}cs=co.cont;cp=co.offset;cr=ct.cont;e=ct.offset}return new ch(cs,cp,cr,e)},createFromNode:function(cp){var cr=cp;var cq=0;var co=cp;var e=bO.nodeLength(co);if(bO.isVoid(cr)){cq=bO.listPrev(cr).length-1;cr=cr.parentNode}if(bO.isBR(co)){e=bO.listPrev(co).length-1;co=co.parentNode}else{if(bO.isVoid(co)){e=bO.listPrev(co).length;co=co.parentNode}}return this.create(cr,cq,co,e)},createFromNodeBefore:function(e){return this.createFromNode(e).collapse(true)},createFromNodeAfter:function(e){return this.createFromNode(e).collapse()},createFromBookmark:function(cp,cq){var cs=bO.fromOffsetPath(cp,cq.s.path);var cr=cq.s.offset;var co=bO.fromOffsetPath(cp,cq.e.path);var e=cq.e.offset;return new ch(cs,cr,co,e)},createFromParaBookmark:function(cq,cp){var cr=cq.s.offset;var e=cq.e.offset;var cs=bO.fromOffsetPath(n.head(cp),cq.s.path);var co=bO.fromOffsetPath(n.last(cp),cq.e.path);return new ch(cs,cr,co,e)}};var cg={"BACKSPACE":8,"TAB":9,"ENTER":13,"SPACE":32,"DELETE":46,"LEFT":37,"UP":38,"RIGHT":39,"DOWN":40,"NUM0":48,"NUM1":49,"NUM2":50,"NUM3":51,"NUM4":52,"NUM5":53,"NUM6":54,"NUM7":55,"NUM8":56,"B":66,"E":69,"I":73,"J":74,"K":75,"L":76,"R":82,"S":83,"U":85,"V":86,"Y":89,"Z":90,"SLASH":191,"LEFTBRACKET":219,"BACKSLASH":220,"RIGHTBRACKET":221};var i={isEdit:function(e){return n.contains([cg.BACKSPACE,cg.TAB,cg.ENTER,cg.SPACE,cg.DELETE,],e)},isMove:function(e){return n.contains([cg.LEFT,cg.UP,cg.RIGHT,cg.DOWN,],e)},nameFromCode:am.invertObject(cg),code:cg};function aA(e){return a3.Deferred(function(co){a3.extend(new FileReader(),{onload:function(cp){var cq=cp.target.result;co.resolve(cq)},onerror:function(cp){co.reject(cp)}}).readAsDataURL(e)}).promise()}function aM(e){return a3.Deferred(function(co){var cp=a3("<img>");cp.one("load",function(){cp.off("error abort");co.resolve(cp)}).one("error abort",function(){cp.off("load").detach();co.reject(cp)}).css({display:"none"}).appendTo(document.body).attr("src",e)}).promise()}var o=(function(){function e(co){this.stack=[];this.stackOffset=-1;this.$editable=co;this.editable=co[0]}e.prototype.makeSnapshot=function(){var co=T.create(this.editable);var cp={s:{path:[],offset:0},e:{path:[],offset:0}};return{contents:this.$editable.html(),bookmark:((co&&co.isOnEditable())?co.bookmark(this.editable):cp)}};e.prototype.applySnapshot=function(co){if(co.contents!==null){this.$editable.html(co.contents)}if(co.bookmark!==null){T.createFromBookmark(this.editable,co.bookmark).select()}};e.prototype.rewind=function(){if(this.$editable.html()!==this.stack[this.stackOffset].contents){this.recordUndo()}this.stackOffset=0;this.applySnapshot(this.stack[this.stackOffset])};e.prototype.commit=function(){this.stack=[];this.stackOffset=-1;this.recordUndo()};e.prototype.reset=function(){this.stack=[];this.stackOffset=-1;this.$editable.html("");this.recordUndo()};e.prototype.undo=function(){if(this.$editable.html()!==this.stack[this.stackOffset].contents){this.recordUndo()}if(this.stackOffset>0){this.stackOffset--;this.applySnapshot(this.stack[this.stackOffset])}};e.prototype.redo=function(){if(this.stack.length-1>this.stackOffset){this.stackOffset++;this.applySnapshot(this.stack[this.stackOffset])}};e.prototype.recordUndo=function(){this.stackOffset++;if(this.stack.length>this.stackOffset){this.stack=this.stack.slice(0,this.stackOffset)}this.stack.push(this.makeSnapshot())};return e}());var y=(function(){function e(){}e.prototype.jQueryCSS=function(cq,co){if(aj.jqueryVersion<1.9){var cp={};a3.each(co,function(cr,cs){cp[cs]=cq.css(cs)});return cp}return cq.css(co)};e.prototype.fromNode=function(co){var cp=["font-family","font-size","text-align","list-style-type","line-height"];var cq=this.jQueryCSS(co,cp)||{};cq["font-size"]=parseInt(cq["font-size"],10);return cq};e.prototype.stylePara=function(co,cp){a3.each(co.nodes(bO.isPara,{includeAncestor:true}),function(cr,cq){a3(cq).css(cp)})};e.prototype.styleNodes=function(co,cs){co=co.splitText();var cv=(cs&&cs.nodeName)||"SPAN";var ct=!!(cs&&cs.expandClosestSibling);var cr=!!(cs&&cs.onlyPartialContains);if(co.isCollapsed()){return[co.insertNode(bO.create(cv))]}var cq=bO.makePredByNodeName(cv);var cp=co.nodes(bO.isText,{fullyContains:true}).map(function(cw){return bO.singleChildAncestor(cw,cq)||bO.wrap(cw,cv)});if(ct){if(cr){var cu=co.nodes();cq=am.and(cq,function(cw){return n.contains(cu,cw)})}return cp.map(function(cx){var cz=bO.withClosestSiblings(cx,cq);var cw=n.head(cz);var cy=n.tail(cz);a3.each(cy,function(cA,cB){bO.appendChildNodes(cw,cB.childNodes);bO.remove(cB)});return n.head(cz)})}else{return cp}};e.prototype.current=function(cp){var ct=a3(!bO.isElement(cp.sc)?cp.sc.parentNode:cp.sc);var cv=this.fromNode(ct);try{cv=a3.extend(cv,{"font-bold":document.queryCommandState("bold")?"bold":"normal","font-italic":document.queryCommandState("italic")?"italic":"normal","font-underline":document.queryCommandState("underline")?"underline":"normal","font-subscript":document.queryCommandState("subscript")?"subscript":"normal","font-superscript":document.queryCommandState("superscript")?"superscript":"normal","font-strikethrough":document.queryCommandState("strikethrough")?"strikethrough":"normal","font-family":document.queryCommandValue("fontname")||cv["font-family"]})}catch(cu){}if(!cp.isOnList()){cv["list-style"]="none"}else{var cq=["circle","disc","disc-leading-zero","square"];var cr=a3.inArray(cv["list-style-type"],cq)>-1;cv["list-style"]=cr?"unordered":"ordered"}var co=bO.ancestor(cp.sc,bO.isPara);if(co&&co.style["line-height"]){cv["line-height"]=co.style.lineHeight}else{var cs=parseInt(cv["line-height"],10)/parseInt(cv["font-size"],10);cv["line-height"]=cs.toFixed(1)}cv.anchor=cp.isOnAnchor()&&bO.ancestor(cp.sc,bO.isAnchor);cv.ancestors=bO.listAncestor(cp.sc,bO.isEditable);cv.range=cp;return cv};return e}());var c=(function(){function e(){}e.prototype.insertOrderedList=function(co){this.toggleList("OL",co)};e.prototype.insertUnorderedList=function(co){this.toggleList("UL",co)};e.prototype.indent=function(cq){var cs=this;var co=T.create(cq).wrapBodyInlineWithPara();var cp=co.nodes(bO.isPara,{includeAncestor:true});var cr=n.clusterBy(cp,am.peq2("parentNode"));a3.each(cr,function(ct,cu){var cv=n.head(cu);if(bO.isLi(cv)){var cw=cs.findList(cv.previousSibling);if(cw){cu.map(function(cx){return cw.appendChild(cx)})}else{cs.wrapList(cu,cv.parentNode.nodeName);cu.map(function(cx){return cx.parentNode}).map(function(cx){return cs.appendToPrevious(cx)})}}else{a3.each(cu,function(cy,cx){a3(cx).css("marginLeft",function(cz,cA){return(parseInt(cA,10)||0)+25})})}});co.select()};e.prototype.outdent=function(cq){var cs=this;var co=T.create(cq).wrapBodyInlineWithPara();var cp=co.nodes(bO.isPara,{includeAncestor:true});var cr=n.clusterBy(cp,am.peq2("parentNode"));a3.each(cr,function(ct,cu){var cv=n.head(cu);if(bO.isLi(cv)){cs.releaseList([cu])}else{a3.each(cu,function(cx,cw){a3(cw).css("marginLeft",function(cy,cz){cz=(parseInt(cz,10)||0);return cz>25?cz-25:""})})}});co.select()};e.prototype.toggleList=function(cq,cr){var ct=this;var co=T.create(cr).wrapBodyInlineWithPara();var cw=co.nodes(bO.isPara,{includeAncestor:true});var cu=co.paraBookmark(cw);var cp=n.clusterBy(cw,am.peq2("parentNode"));if(n.find(cw,bO.isPurePara)){var cs=[];a3.each(cp,function(cx,cy){cs=cs.concat(ct.wrapList(cy,cq))});cw=cs}else{var cv=co.nodes(bO.isList,{includeAncestor:true}).filter(function(cx){return !a3.nodeName(cx,cq)});if(cv.length){a3.each(cv,function(cx,cy){bO.replace(cy,cq)})}else{cw=this.releaseList(cp,true)}}T.createFromParaBookmark(cu,cw).select()};e.prototype.wrapList=function(cr,cq){var cs=n.head(cr);var cu=n.last(cr);var cp=bO.isList(cs.previousSibling)&&cs.previousSibling;var co=bO.isList(cu.nextSibling)&&cu.nextSibling;var ct=cp||bO.insertAfter(bO.create(cq||"UL"),cu);cr=cr.map(function(cv){return bO.isPurePara(cv)?bO.replace(cv,"LI"):cv});bO.appendChildNodes(ct,cr);if(co){bO.appendChildNodes(ct,n.from(co.childNodes));bO.remove(co)}return cr};e.prototype.releaseList=function(cq,co){var cr=this;var cp=[];a3.each(cq,function(cy,cA){var cv=n.head(cA);var cz=n.last(cA);var cs=co?bO.lastAncestor(cv,bO.isList):cv.parentNode;var ct=cs.parentNode;if(cs.parentNode.nodeName==="LI"){cA.map(function(cB){var cC=cr.findNextSiblings(cB);if(ct.nextSibling){ct.parentNode.insertBefore(cB,ct.nextSibling)}else{ct.parentNode.appendChild(cB)}if(cC.length){cr.wrapList(cC,cs.nodeName);cB.appendChild(cC[0].parentNode)}});if(cs.children.length===0){ct.removeChild(cs)}if(ct.childNodes.length===0){ct.parentNode.removeChild(ct)}}else{var cu=cs.childNodes.length>1?bO.splitTree(cs,{node:cz.parentNode,offset:bO.position(cz)+1},{isSkipPaddingBlankHTML:true}):null;var cw=bO.splitTree(cs,{node:cv.parentNode,offset:bO.position(cv)},{isSkipPaddingBlankHTML:true});cA=co?bO.listDescendant(cw,bO.isLi):n.from(cw.childNodes).filter(bO.isLi);if(co||!bO.isList(cs.parentNode)){cA=cA.map(function(cB){return bO.replace(cB,"P")})}a3.each(n.from(cA).reverse(),function(cC,cB){bO.insertAfter(cB,cs)});var cx=n.compact([cs,cw,cu]);a3.each(cx,function(cB,cC){var cD=[cC].concat(bO.listDescendant(cC,bO.isList));a3.each(cD.reverse(),function(cE,cF){if(!bO.nodeLength(cF)){bO.remove(cF,true)}})})}cp=cp.concat(cA)});return cp};e.prototype.appendToPrevious=function(co){return co.previousSibling?bO.appendChildNodes(co.previousSibling,[co]):this.wrapList([co],"LI")};e.prototype.findList=function(co){return co?n.find(co.children,function(cp){return["OL","UL"].indexOf(cp.nodeName)>-1}):null};e.prototype.findNextSiblings=function(co){var cp=[];while(co.nextSibling){cp.push(co.nextSibling);co=co.nextSibling}return cp};return e}());var B=(function(){function e(co){this.bullet=new c();this.options=co.options}e.prototype.insertTab=function(cp,co){var cq=bO.createText(new Array(co+1).join(bO.NBSP_CHAR));cp=cp.deleteContents();cp.insertNode(cq,true);cp=T.create(cq,co);cp.select()};e.prototype.insertParagraph=function(cr,cp){cp=cp||T.create(cr);cp=cp.deleteContents();cp=cp.wrapBodyInlineWithPara();var cu=bO.ancestor(cp.sc,bO.isPara);var ct;if(cu){if(bO.isEmpty(cu)&&bO.isLi(cu)){this.bullet.toggleList(cu.parentNode.nodeName);return}else{var cv=null;if(this.options.blockquoteBreakingLevel===1){cv=bO.ancestor(cu,bO.isBlockquote)}else{if(this.options.blockquoteBreakingLevel===2){cv=bO.lastAncestor(cu,bO.isBlockquote)}}if(cv){ct=a3(bO.emptyPara)[0];if(bO.isRightEdgePoint(cp.getStartPoint())&&bO.isBR(cp.sc.nextSibling)){a3(cp.sc.nextSibling).remove()}var cq=bO.splitTree(cv,cp.getStartPoint(),{isDiscardEmptySplits:true});if(cq){cq.parentNode.insertBefore(ct,cq)}else{bO.insertAfter(ct,cv)}}else{ct=bO.splitTree(cu,cp.getStartPoint());var co=bO.listDescendant(cu,bO.isEmptyAnchor);co=co.concat(bO.listDescendant(ct,bO.isEmptyAnchor));a3.each(co,function(cw,cx){bO.remove(cx)});if((bO.isHeading(ct)||bO.isPre(ct)||bO.isCustomStyleTag(ct))&&bO.isEmpty(ct)){ct=bO.replace(ct,"p")}}}}else{var cs=cp.sc.childNodes[cp.so];ct=a3(bO.emptyPara)[0];if(cs){cp.sc.insertBefore(ct,cs)}else{cp.sc.appendChild(ct)}}T.create(ct,0).normalize().select().scrollIntoView(cr)};return e}());var a5=function(e,ct,cq,cs){var co={"colPos":0,"rowPos":0};var cu=[];var cw=[];function cz(){if(!e||!e.tagName||(e.tagName.toLowerCase()!=="td"&&e.tagName.toLowerCase()!=="th")){console.error("Impossible to identify start Cell point.",e);return}co.colPos=e.cellIndex;if(!e.parentElement||!e.parentElement.tagName||e.parentElement.tagName.toLowerCase()!=="tr"){console.error("Impossible to identify start Row point.",e);return}co.rowPos=e.parentElement.rowIndex}function cy(cL,cI,cG,cH,cK,cF,cE){var cJ={"baseRow":cG,"baseCell":cH,"isRowSpan":cK,"isColSpan":cF,"isVirtual":cE};if(!cu[cL]){cu[cL]=[]}cu[cL][cI]=cJ}function cC(cH,cE,cF,cG){return{"baseCell":cH.baseCell,"action":cE,"virtualTable":{"rowIndex":cF,"cellIndex":cG}}}function cp(cG,cE){if(!cu[cG]){return cE}if(!cu[cG][cE]){return cE}var cF=cE;while(cu[cG][cF]){cF++;if(!cu[cG][cF]){return cF}}}function cx(cP,cO){var cM=cp(cP.rowIndex,cO.cellIndex);var cG=(cO.colSpan>1);var cI=(cO.rowSpan>1);var cN=(cP.rowIndex===co.rowPos&&cO.cellIndex===co.colPos);cy(cP.rowIndex,cM,cP,cO,cI,cG,false);var cF=cO.attributes.rowSpan?parseInt(cO.attributes.rowSpan.value,10):0;if(cF>1){for(var cJ=1;cJ<cF;cJ++){var cE=cP.rowIndex+cJ;cD(cE,cM,cO,cN);cy(cE,cM,cP,cO,true,cG,true)}}var cH=cO.attributes.colSpan?parseInt(cO.attributes.colSpan.value,10):0;if(cH>1){for(var cL=1;cL<cH;cL++){var cK=cp(cP.rowIndex,(cM+cL));cD(cP.rowIndex,cK,cO,cN);cy(cP.rowIndex,cK,cP,cO,cI,true,true)}}}function cD(cH,cF,cE,cG){if(cH===co.rowPos&&co.colPos>=cE.cellIndex&&cE.cellIndex<=cF&&!cG){co.colPos++}}function cA(){var cG=cs.rows;for(var cH=0;cH<cG.length;cH++){var cF=cG[cH].cells;for(var cE=0;cE<cF.length;cE++){cx(cG[cH],cF[cE])}}}function cv(cE){switch(ct){case a5.where.Column:if(cE.isColSpan){return a5.resultAction.SubtractSpanCount}break;case a5.where.Row:if(!cE.isVirtual&&cE.isRowSpan){return a5.resultAction.AddCell}else{if(cE.isRowSpan){return a5.resultAction.SubtractSpanCount}}break}return a5.resultAction.RemoveCell}function cr(cE){switch(ct){case a5.where.Column:if(cE.isColSpan){return a5.resultAction.SumSpanCount}else{if(cE.isRowSpan&&cE.isVirtual){return a5.resultAction.Ignore}}break;case a5.where.Row:if(cE.isRowSpan){return a5.resultAction.SumSpanCount}else{if(cE.isColSpan&&cE.isVirtual){return a5.resultAction.Ignore}}break}return a5.resultAction.AddCell}function cB(){cz();cA()}this.getActionList=function(){var cG=(ct===a5.where.Row)?co.rowPos:-1;var cJ=(ct===a5.where.Column)?co.colPos:-1;var cI=0;var cK=true;while(cK){var cF=(cG>=0)?cG:cI;var cH=(cJ>=0)?cJ:cI;var cM=cu[cF];if(!cM){cK=false;return cw}var cL=cM[cH];if(!cL){cK=false;return cw}var cE=a5.resultAction.Ignore;switch(cq){case a5.requestAction.Add:cE=cr(cL);break;case a5.requestAction.Delete:cE=cv(cL);break}cw.push(cC(cL,cE,cF,cH));cI++}return cw};cB()};a5.where={"Row":0,"Column":1};a5.requestAction={"Add":0,"Delete":1};a5.resultAction={"Ignore":0,"SubtractSpanCount":1,"RemoveCell":2,"AddCell":3,"SumSpanCount":4};var aw=(function(){function e(){}e.prototype.tab=function(cp,cr){var co=bO.ancestor(cp.commonAncestor(),bO.isCell);var ct=bO.ancestor(co,bO.isTable);var cq=bO.listDescendant(ct,bO.isCell);var cs=n[cr?"prev":"next"](cq,co);if(cs){T.create(cs,0).select()}};e.prototype.addRow=function(co,cx){var cC=bO.ancestor(co.commonAncestor(),bO.isCell);var cr=a3(cC).closest("tr");var cE=this.recoverAttributes(cr);var cw=a3("<tr"+cE+"></tr>");var cD=new a5(cC,a5.where.Row,a5.requestAction.Add,a3(cr).closest("table")[0]);var cu=cD.getActionList();for(var cB=0;cB<cu.length;cB++){var cA=cu[cB];var cz=this.recoverAttributes(cA.baseCell);switch(cA.action){case a5.resultAction.AddCell:cw.append("<td"+cz+">"+bO.blank+"</td>");break;case a5.resultAction.SumSpanCount:if(cx==="top"){var cq=cA.baseCell.parent;var cy=(!cq?0:cA.baseCell.closest("tr").rowIndex)<=cr[0].rowIndex;if(cy){var ct=a3("<div></div>").append(a3("<td"+cz+">"+bO.blank+"</td>").removeAttr("rowspan")).html();cw.append(ct);break}}var cp=parseInt(cA.baseCell.rowSpan,10);cp++;cA.baseCell.setAttribute("rowSpan",cp);break}}if(cx==="top"){cr.before(cw)}else{var cs=(cC.rowSpan>1);if(cs){var cv=cr[0].rowIndex+(cC.rowSpan-2);a3(a3(cr).parent().find("tr")[cv]).after(a3(cw));return}cr.after(cw)}};e.prototype.addCol=function(co,cr){var cw=bO.ancestor(co.commonAncestor(),bO.isCell);var cy=a3(cw).closest("tr");var cu=a3(cy).siblings();cu.push(cy);var cx=new a5(cw,a5.where.Column,a5.requestAction.Add,a3(cy).closest("table")[0]);var cq=cx.getActionList();for(var cv=0;cv<cq.length;cv++){var ct=cq[cv];var cs=this.recoverAttributes(ct.baseCell);switch(ct.action){case a5.resultAction.AddCell:if(cr==="right"){a3(ct.baseCell).after("<td"+cs+">"+bO.blank+"</td>")}else{a3(ct.baseCell).before("<td"+cs+">"+bO.blank+"</td>")}break;case a5.resultAction.SumSpanCount:if(cr==="right"){var cp=parseInt(ct.baseCell.colSpan,10);cp++;ct.baseCell.setAttribute("colSpan",cp)}else{a3(ct.baseCell).before("<td"+cs+">"+bO.blank+"</td>")}break}}};e.prototype.recoverAttributes=function(cq){var cp="";if(!cq){return cp}var cr=cq.attributes||[];for(var co=0;co<cr.length;co++){if(cr[co].name.toLowerCase()==="id"){continue}if(cr[co].specified){cp+=" "+cr[co].name+"='"+cr[co].value+"'"}}return cp};e.prototype.deleteRow=function(cq){var cw=bO.ancestor(cq.commonAncestor(),bO.isCell);var cB=a3(cw).closest("tr");var cz=cB.children("td, th").index(a3(cw));var cp=cB[0].rowIndex;var cy=new a5(cw,a5.where.Row,a5.requestAction.Delete,a3(cB).closest("table")[0]);var ct=cy.getActionList();for(var cv=0;cv<ct.length;cv++){if(!ct[cv]){continue}var cu=ct[cv].baseCell;var cs=ct[cv].virtualTable;var co=(cu.rowSpan&&cu.rowSpan>1);var cr=(co)?parseInt(cu.rowSpan,10):0;switch(ct[cv].action){case a5.resultAction.Ignore:continue;case a5.resultAction.AddCell:var cx=cB.next("tr")[0];if(!cx){continue}var cA=cB[0].cells[cz];if(co){if(cr>2){cr--;cx.insertBefore(cA,cx.cells[cz]);cx.cells[cz].setAttribute("rowSpan",cr);cx.cells[cz].innerHTML=""}else{if(cr===2){cx.insertBefore(cA,cx.cells[cz]);cx.cells[cz].removeAttribute("rowSpan");cx.cells[cz].innerHTML=""}}}continue;case a5.resultAction.SubtractSpanCount:if(co){if(cr>2){cr--;cu.setAttribute("rowSpan",cr);if(cs.rowIndex!==cp&&cu.cellIndex===cz){cu.innerHTML=""}}else{if(cr===2){cu.removeAttribute("rowSpan");if(cs.rowIndex!==cp&&cu.cellIndex===cz){cu.innerHTML=""}}}}continue;case a5.resultAction.RemoveCell:continue}}cB.remove()};e.prototype.deleteCol=function(co){var ct=bO.ancestor(co.commonAncestor(),bO.isCell);var cx=a3(ct).closest("tr");var cw=cx.children("td, th").index(a3(ct));var cv=new a5(ct,a5.where.Column,a5.requestAction.Delete,a3(cx).closest("table")[0]);var cq=cv.getActionList();for(var cs=0;cs<cq.length;cs++){if(!cq[cs]){continue}switch(cq[cs].action){case a5.resultAction.Ignore:continue;case a5.resultAction.SubtractSpanCount:var cr=cq[cs].baseCell;var cu=(cr.colSpan&&cr.colSpan>1);if(cu){var cp=(cr.colSpan)?parseInt(cr.colSpan,10):0;if(cp>2){cp--;cr.setAttribute("colSpan",cp);if(cr.cellIndex===cw){cr.innerHTML=""}}else{if(cp===2){cr.removeAttribute("colSpan");if(cr.cellIndex===cw){cr.innerHTML=""}}}}continue;case a5.resultAction.RemoveCell:bO.remove(cq[cs].baseCell,true);continue}}};e.prototype.createTable=function(cv,cu,cx){var cr=[];var cq;for(var cs=0;cs<cv;cs++){cr.push("<td>"+bO.blank+"</td>")}cq=cr.join("");var ct=[];var co;for(var cp=0;cp<cu;cp++){ct.push("<tr>"+cq+"</tr>")}co=ct.join("");var cw=a3("<table>"+co+"</table>");if(cx&&cx.tableClassName){cw.addClass(cx.tableClassName)}return cw[0]};e.prototype.deleteTable=function(cp){var co=bO.ancestor(cp.commonAncestor(),bO.isCell);a3(co).closest("table").remove()};return e}());var ck="bogus";var l=(function(){function e(cr){var cs=this;this.context=cr;this.$note=cr.layoutInfo.note;this.$editor=cr.layoutInfo.editor;this.$editable=cr.layoutInfo.editable;this.options=cr.options;this.lang=this.options.langInfo;this.editable=this.$editable[0];this.lastRange=null;this.style=new y();this.table=new aw();this.typing=new B(cr);this.bullet=new c();this.history=new o(this.$editable);this.context.memo("help.undo",this.lang.help.undo);this.context.memo("help.redo",this.lang.help.redo);this.context.memo("help.tab",this.lang.help.tab);this.context.memo("help.untab",this.lang.help.untab);this.context.memo("help.insertParagraph",this.lang.help.insertParagraph);this.context.memo("help.insertOrderedList",this.lang.help.insertOrderedList);this.context.memo("help.insertUnorderedList",this.lang.help.insertUnorderedList);this.context.memo("help.indent",this.lang.help.indent);this.context.memo("help.outdent",this.lang.help.outdent);this.context.memo("help.formatPara",this.lang.help.formatPara);this.context.memo("help.insertHorizontalRule",this.lang.help.insertHorizontalRule);this.context.memo("help.fontName",this.lang.help.fontName);var cq=["bold","italic","underline","strikethrough","superscript","subscript","justifyLeft","justifyCenter","justifyRight","justifyFull","formatBlock","removeFormat","backColor",];for(var cp=0,co=cq.length;cp<co;cp++){this[cq[cp]]=(function(ct){return function(cu){cs.beforeCommand();document.execCommand(ct,false,cu);cs.afterCommand(true)}})(cq[cp]);this.context.memo("help."+cq[cp],this.lang.help[cq[cp]])}this.fontName=this.wrapCommand(function(ct){return cs.fontStyling("font-family","'"+ct+"'")});this.fontSize=this.wrapCommand(function(ct){return cs.fontStyling("font-size",ct+"px")});for(var cp=1;cp<=6;cp++){this["formatH"+cp]=(function(ct){return function(){cs.formatBlock("H"+ct)}})(cp);this.context.memo("help.formatH"+cp,this.lang.help["formatH"+cp])}this.insertParagraph=this.wrapCommand(function(){cs.typing.insertParagraph(cs.editable)});this.insertOrderedList=this.wrapCommand(function(){cs.bullet.insertOrderedList(cs.editable)});this.insertUnorderedList=this.wrapCommand(function(){cs.bullet.insertUnorderedList(cs.editable)});this.indent=this.wrapCommand(function(){cs.bullet.indent(cs.editable)});this.outdent=this.wrapCommand(function(){cs.bullet.outdent(cs.editable)});this.insertNode=this.wrapCommand(function(cu){if(cs.isLimited(a3(cu).text().length)){return}var ct=cs.getLastRange();ct.insertNode(cu);T.createFromNodeAfter(cu).select();cs.setLastRange()});this.insertText=this.wrapCommand(function(cv){if(cs.isLimited(cv.length)){return}var ct=cs.getLastRange();var cu=ct.insertNode(bO.createText(cv));T.create(cu,bO.nodeLength(cu)).select();cs.setLastRange()});this.pasteHTML=this.wrapCommand(function(ct){if(cs.isLimited(ct.length)){return}ct=cs.context.invoke("codeview.purify",ct);var cu=cs.getLastRange().pasteHTML(ct);T.createFromNodeAfter(n.last(cu)).select();cs.setLastRange()});this.formatBlock=this.wrapCommand(function(cv,ct){var cu=cs.options.callbacks.onApplyCustomStyle;if(cu){cu.call(cs,ct,cs.context,cs.onFormatBlock)}else{cs.onFormatBlock(cv,ct)}});this.insertHorizontalRule=this.wrapCommand(function(){var ct=cs.getLastRange().insertNode(bO.create("HR"));if(ct.nextSibling){T.create(ct.nextSibling,0).normalize().select();cs.setLastRange()}});this.lineHeight=this.wrapCommand(function(ct){cs.style.stylePara(cs.getLastRange(),{lineHeight:ct})});this.createLink=this.wrapCommand(function(cA){var cB=cA.url;var cx=cA.text;var cE=cA.isNewWindow;var ct=cA.range||cs.getLastRange();var cw=cx.length-ct.toString().length;if(cw>0&&cs.isLimited(cw)){return}var cF=ct.toString()!==cx;if(typeof cB==="string"){cB=cB.trim()}if(cs.options.onCreateLink){cB=cs.options.onCreateLink(cB)}else{cB=/^([A-Za-z][A-Za-z0-9+-.]*\:|#|\/)/.test(cB)?cB:"http://"+cB}var cu=[];if(cF){ct=ct.deleteContents();var cz=ct.insertNode(a3("<A>"+cx+"</A>")[0]);cu.push(cz)}else{cu=cs.style.styleNodes(ct,{nodeName:"A",expandClosestSibling:true,onlyPartialContains:true})}a3.each(cu,function(cG,cH){a3(cH).attr("href",cB);if(cE){a3(cH).attr("target","_blank")}else{a3(cH).removeAttr("target")}});var cD=T.createFromNodeBefore(n.head(cu));var cv=cD.getStartPoint();var cy=T.createFromNodeAfter(n.last(cu));var cC=cy.getEndPoint();T.create(cv.node,cv.offset,cC.node,cC.offset).select();cs.setLastRange()});this.color=this.wrapCommand(function(cu){var cv=cu.foreColor;var ct=cu.backColor;if(cv){document.execCommand("foreColor",false,cv)}if(ct){document.execCommand("backColor",false,ct)}});this.foreColor=this.wrapCommand(function(ct){document.execCommand("styleWithCSS",false,true);document.execCommand("foreColor",false,ct)});this.insertTable=this.wrapCommand(function(cv){var cu=cv.split("x");var ct=cs.getLastRange().deleteContents();ct.insertNode(cs.table.createTable(cu[0],cu[1],cs.options))});this.removeMedia=this.wrapCommand(function(){var ct=a3(cs.restoreTarget()).parent();if(ct.parent("figure").length){ct.parent("figure").remove()}else{ct=a3(cs.restoreTarget()).detach()}cs.context.triggerEvent("media.delete",ct,cs.$editable)});this.floatMe=this.wrapCommand(function(cu){var ct=a3(cs.restoreTarget());ct.toggleClass("note-float-left",cu==="left");ct.toggleClass("note-float-right",cu==="right");ct.css("float",(cu==="none"?"":cu))});this.resize=this.wrapCommand(function(cu){var ct=a3(cs.restoreTarget());cu=parseFloat(cu);if(cu===0){ct.css("width","")}else{ct.css({width:cu*100+"%",height:""})}})}e.prototype.initialize=function(){var co=this;this.$editable.on("keydown",function(cp){if(cp.keyCode===i.code.ENTER){co.context.triggerEvent("enter",cp)}co.context.triggerEvent("keydown",cp);if(!cp.isDefaultPrevented()){if(co.options.shortcuts){co.handleKeyMap(cp)}else{co.preventDefaultEditableShortCuts(cp)}}if(co.isLimited(1,cp)){return false}}).on("keyup",function(cp){co.setLastRange();co.context.triggerEvent("keyup",cp)}).on("focus",function(cp){co.setLastRange();co.context.triggerEvent("focus",cp)}).on("blur",function(cp){co.context.triggerEvent("blur",cp)}).on("mousedown",function(cp){co.context.triggerEvent("mousedown",cp)}).on("mouseup",function(cp){co.setLastRange();co.context.triggerEvent("mouseup",cp)}).on("scroll",function(cp){co.context.triggerEvent("scroll",cp)}).on("paste",function(cp){co.setLastRange();co.context.triggerEvent("paste",cp)});this.$editable.attr("spellcheck",this.options.spellCheck);this.$editable.html(bO.html(this.$note)||bO.emptyPara);this.$editable.on(aj.inputEventName,am.debounce(function(){co.context.triggerEvent("change",co.$editable.html(),co.$editable)},10));this.$editor.on("focusin",function(cp){co.context.triggerEvent("focusin",cp)}).on("focusout",function(cp){co.context.triggerEvent("focusout",cp)});if(!this.options.airMode){if(this.options.width){this.$editor.outerWidth(this.options.width)}if(this.options.height){this.$editable.outerHeight(this.options.height)}if(this.options.maxHeight){this.$editable.css("max-height",this.options.maxHeight)}if(this.options.minHeight){this.$editable.css("min-height",this.options.minHeight)}}this.history.recordUndo();this.setLastRange()};e.prototype.destroy=function(){this.$editable.off()};e.prototype.handleKeyMap=function(cr){var cs=this.options.keyMap[aj.isMac?"mac":"pc"];var cq=[];if(cr.metaKey){cq.push("CMD")}if(cr.ctrlKey&&!cr.altKey){cq.push("CTRL")}if(cr.shiftKey){cq.push("SHIFT")}var cp=i.nameFromCode[cr.keyCode];if(cp){cq.push(cp)}var co=cs[cq.join("+")];if(co){if(this.context.invoke(co)!==false){cr.preventDefault()}}else{if(i.isEdit(cr.keyCode)){this.afterCommand()}}};e.prototype.preventDefaultEditableShortCuts=function(co){if((co.ctrlKey||co.metaKey)&&n.contains([66,73,85],co.keyCode)){co.preventDefault()}};e.prototype.isLimited=function(cp,co){cp=cp||0;if(typeof co!=="undefined"){if(i.isMove(co.keyCode)||(co.ctrlKey||co.metaKey)||n.contains([i.code.BACKSPACE,i.code.DELETE],co.keyCode)){return false}}if(this.options.maxTextLength>0){if((this.$editable.text().length+cp)>=this.options.maxTextLength){return true}}return false};e.prototype.createRange=function(){this.focus();this.setLastRange();return this.getLastRange()};e.prototype.setLastRange=function(){this.lastRange=T.create(this.editable)};e.prototype.getLastRange=function(){if(!this.lastRange){this.setLastRange()}return this.lastRange};e.prototype.saveRange=function(co){if(co){this.getLastRange().collapse().select()}};e.prototype.restoreRange=function(){if(this.lastRange){this.lastRange.select();this.focus()}};e.prototype.saveTarget=function(co){this.$editable.data("target",co)};e.prototype.clearTarget=function(){this.$editable.removeData("target")};e.prototype.restoreTarget=function(){return this.$editable.data("target")};e.prototype.currentStyle=function(){var co=T.create();if(co){co=co.normalize()}return co?this.style.current(co):this.style.fromNode(this.$editable)};e.prototype.styleFromNode=function(co){return this.style.fromNode(co)};e.prototype.undo=function(){this.context.triggerEvent("before.command",this.$editable.html());this.history.undo();this.context.triggerEvent("change",this.$editable.html(),this.$editable)};e.prototype.commit=function(){this.context.triggerEvent("before.command",this.$editable.html());this.history.commit();this.context.triggerEvent("change",this.$editable.html(),this.$editable)};e.prototype.redo=function(){this.context.triggerEvent("before.command",this.$editable.html());this.history.redo();this.context.triggerEvent("change",this.$editable.html(),this.$editable)};e.prototype.beforeCommand=function(){this.context.triggerEvent("before.command",this.$editable.html());this.focus()};e.prototype.afterCommand=function(co){this.normalizeContent();this.history.recordUndo();if(!co){this.context.triggerEvent("change",this.$editable.html(),this.$editable)}};e.prototype.tab=function(){var co=this.getLastRange();if(co.isCollapsed()&&co.isOnCell()){this.table.tab(co)}else{if(this.options.tabSize===0){return false}if(!this.isLimited(this.options.tabSize)){this.beforeCommand();this.typing.insertTab(co,this.options.tabSize);this.afterCommand()}}};e.prototype.untab=function(){var co=this.getLastRange();if(co.isCollapsed()&&co.isOnCell()){this.table.tab(co,true)}else{if(this.options.tabSize===0){return false}}};e.prototype.wrapCommand=function(co){return function(){this.beforeCommand();co.apply(this,arguments);this.afterCommand()}};e.prototype.insertImage=function(cp,co){var cq=this;return aM(cp,co).then(function(cr){cq.beforeCommand();if(typeof co==="function"){co(cr)}else{if(typeof co==="string"){cr.attr("data-filename",co)}cr.css("width",Math.min(cq.$editable.width(),cr.width()))}cr.show();T.create(cq.editable).insertNode(cr[0]);T.createFromNodeAfter(cr[0]).select();cq.setLastRange();cq.afterCommand()}).fail(function(cr){cq.context.triggerEvent("image.upload.error",cr)})};e.prototype.insertImagesAsDataURL=function(co){var cp=this;a3.each(co,function(cq,cs){var cr=cs.name;if(cp.options.maximumImageFileSize&&cp.options.maximumImageFileSize<cs.size){cp.context.triggerEvent("image.upload.error",cp.lang.image.maximumFileSizeError)}else{aA(cs).then(function(ct){return cp.insertImage(ct,cr)}).fail(function(){cp.context.triggerEvent("image.upload.error")})}})};e.prototype.insertImagesOrCallback=function(cp){var co=this.options.callbacks;if(co.onImageUpload){this.context.triggerEvent("image.upload",cp)}else{this.insertImagesAsDataURL(cp)}};e.prototype.getSelectedText=function(){var co=this.getLastRange();if(co.isOnAnchor()){co=T.createFromNode(bO.ancestor(co.sc,bO.isAnchor))}return co.toString()};e.prototype.onFormatBlock=function(cq,cp){cq=aj.isMSIE?"<"+cq+">":cq;document.execCommand("FormatBlock",false,cq);if(cp&&cp.length){var cr=cp[0].className||"";if(cr){var co=this.createRange();var cs=a3([co.sc,co.ec]).closest(cq);cs.addClass(cr)}}};e.prototype.formatPara=function(){this.formatBlock("P")};e.prototype.fontStyling=function(cs,cr){var co=this.getLastRange();if(co){var cp=this.style.styleNodes(co);a3(cp).css(cs,cr);if(co.isCollapsed()){var cq=n.head(cp);if(cq&&!bO.nodeLength(cq)){cq.innerHTML=bO.ZERO_WIDTH_NBSP_CHAR;T.createFromNodeAfter(cq.firstChild).select();this.setLastRange();this.$editable.data(ck,cq)}}}};e.prototype.unlink=function(){var co=this.getLastRange();if(co.isOnAnchor()){var cp=bO.ancestor(co.sc,bO.isAnchor);co=T.createFromNode(cp);co.select();this.setLastRange();this.beforeCommand();document.execCommand("unlink");this.afterCommand()}};e.prototype.getLinkInfo=function(){var co=this.getLastRange().expand(bO.isAnchor);var cq=a3(n.head(co.nodes(bO.isAnchor)));var cp={range:co,text:co.toString(),url:cq.length?cq.attr("href"):""};if(cq.length){cp.isNewWindow=cq.attr("target")==="_blank"}return cp};e.prototype.addRow=function(co){var cp=this.getLastRange(this.$editable);if(cp.isCollapsed()&&cp.isOnCell()){this.beforeCommand();this.table.addRow(cp,co);this.afterCommand()}};e.prototype.addCol=function(co){var cp=this.getLastRange(this.$editable);if(cp.isCollapsed()&&cp.isOnCell()){this.beforeCommand();this.table.addCol(cp,co);this.afterCommand()}};e.prototype.deleteRow=function(){var co=this.getLastRange(this.$editable);if(co.isCollapsed()&&co.isOnCell()){this.beforeCommand();this.table.deleteRow(co);this.afterCommand()}};e.prototype.deleteCol=function(){var co=this.getLastRange(this.$editable);if(co.isCollapsed()&&co.isOnCell()){this.beforeCommand();this.table.deleteCol(co);this.afterCommand()}};e.prototype.deleteTable=function(){var co=this.getLastRange(this.$editable);if(co.isCollapsed()&&co.isOnCell()){this.beforeCommand();this.table.deleteTable(co);this.afterCommand()}};e.prototype.resizeTo=function(ct,co,cp){var cs;if(cp){var cr=ct.y/ct.x;var cq=co.data("ratio");cs={width:cq>cr?ct.x:ct.y/cq,height:cq>cr?ct.x*cq:ct.y}}else{cs={width:ct.x,height:ct.y}}co.css(cs)};e.prototype.hasFocus=function(){return this.$editable.is(":focus")};e.prototype.focus=function(){if(!this.hasFocus()){this.$editable.focus()}};e.prototype.isEmpty=function(){return bO.isEmpty(this.$editable[0])||bO.emptyPara===this.$editable.html()};e.prototype.empty=function(){this.context.invoke("code",bO.emptyPara)};e.prototype.normalizeContent=function(){this.$editable[0].normalize()};return e}());var ae=(function(){function e(co){this.context=co;this.$editable=co.layoutInfo.editable}e.prototype.initialize=function(){this.$editable.on("paste",this.pasteByEvent.bind(this))};e.prototype.pasteByEvent=function(cp){var cq=cp.originalEvent.clipboardData;if(cq&&cq.items&&cq.items.length){var co=cq.items.length>1?cq.items[1]:n.head(cq.items);if(co.kind==="file"&&co.type.indexOf("image/")!==-1){this.context.invoke("editor.insertImagesOrCallback",[co.getAsFile()])}this.context.invoke("editor.afterCommand")}};return e}());var bh=(function(){function e(co){this.context=co;this.$eventListener=a3(document);this.$editor=co.layoutInfo.editor;this.$editable=co.layoutInfo.editable;this.options=co.options;this.lang=this.options.langInfo;this.documentEventHandlers={};this.$dropzone=a3(['<div class="note-dropzone">','  <div class="note-dropzone-message"/>',"</div>",].join("")).prependTo(this.$editor)}e.prototype.initialize=function(){if(this.options.disableDragAndDrop){this.documentEventHandlers.onDrop=function(co){co.preventDefault()};this.$eventListener=this.$dropzone;this.$eventListener.on("drop",this.documentEventHandlers.onDrop)}else{this.attachDragAndDropEvent()}};e.prototype.attachDragAndDropEvent=function(){var cq=this;var cp=a3();var co=this.$dropzone.find(".note-dropzone-message");this.documentEventHandlers.onDragenter=function(ct){var cs=cq.context.invoke("codeview.isActivated");var cr=cq.$editor.width()>0&&cq.$editor.height()>0;if(!cs&&!cp.length&&cr){cq.$editor.addClass("dragover");cq.$dropzone.width(cq.$editor.width());cq.$dropzone.height(cq.$editor.height());co.text(cq.lang.image.dragImageHere)}cp=cp.add(ct.target)};this.documentEventHandlers.onDragleave=function(cr){cp=cp.not(cr.target);if(!cp.length){cq.$editor.removeClass("dragover")}};this.documentEventHandlers.onDrop=function(){cp=a3();cq.$editor.removeClass("dragover")};this.$eventListener.on("dragenter",this.documentEventHandlers.onDragenter).on("dragleave",this.documentEventHandlers.onDragleave).on("drop",this.documentEventHandlers.onDrop);this.$dropzone.on("dragenter",function(){cq.$dropzone.addClass("hover");co.text(cq.lang.image.dropImage)}).on("dragleave",function(){cq.$dropzone.removeClass("hover");co.text(cq.lang.image.dragImageHere)});this.$dropzone.on("drop",function(cr){var cs=cr.originalEvent.dataTransfer;cr.preventDefault();if(cs&&cs.files&&cs.files.length){cq.$editable.focus();cq.context.invoke("editor.insertImagesOrCallback",cs.files)}else{a3.each(cs.types,function(ct,cu){var cv=cs.getData(cu);if(cu.toLowerCase().indexOf("text")>-1){cq.context.invoke("editor.pasteHTML",cv)}else{a3(cv).each(function(cw,cx){cq.context.invoke("editor.insertNode",cx)})}})}}).on("dragover",false)};e.prototype.destroy=function(){var co=this;Object.keys(this.documentEventHandlers).forEach(function(cp){co.$eventListener.off(cp.substr(2).toLowerCase(),co.documentEventHandlers[cp])});this.documentEventHandlers={}};return e}());var N;if(aj.hasCodeMirror){if(aj.isSupportAmd){require(["codemirror"],function(e){N=e})}else{N=window.CodeMirror}}var a8=(function(){function e(co){this.context=co;this.$editor=co.layoutInfo.editor;this.$editable=co.layoutInfo.editable;this.$codable=co.layoutInfo.codable;this.options=co.options}e.prototype.sync=function(){var co=this.isActivated();if(co&&aj.hasCodeMirror){this.$codable.data("cmEditor").save()}};e.prototype.isActivated=function(){return this.$editor.hasClass("codeview")};e.prototype.toggle=function(){if(this.isActivated()){this.deactivate()}else{this.activate()}this.context.triggerEvent("codeview.toggled")};e.prototype.purify=function(co){if(this.options.codeviewFilter){co=co.replace(this.options.codeviewFilterRegex,"");if(this.options.codeviewIframeFilter){var cp=this.options.codeviewIframeWhitelistSrc.concat(this.options.codeviewIframeWhitelistSrcBase);co=co.replace(/(<iframe.*?>.*?(?:<\/iframe>)?)/gi,function(cq){if(/<.+src(?==?('|"|\s)?)[\s\S]+src(?=('|"|\s)?)[^>]*?>/i.test(cq)){return""}for(var cr=0,ct=cp;cr<ct.length;cr++){var cs=ct[cr];if((new RegExp('src="(https?:)?//'+cs+'/(.+)"')).test(cq)){return cq}}return""})}}return co};e.prototype.activate=function(){var cq=this;this.$codable.val(bO.html(this.$editable,this.options.prettifyHtml));this.$codable.height(this.$editable.height());this.context.invoke("toolbar.updateCodeview",true);this.$editor.addClass("codeview");this.$codable.focus();if(aj.hasCodeMirror){var co=N.fromTextArea(this.$codable[0],this.options.codemirror);if(this.options.codemirror.tern){var cp=new N.TernServer(this.options.codemirror.tern);co.ternServer=cp;co.on("cursorActivity",function(cr){cp.updateArgHints(cr)})}co.on("blur",function(cr){cq.context.triggerEvent("blur.codeview",co.getValue(),cr)});co.on("change",function(cr){cq.context.triggerEvent("change.codeview",co.getValue(),co)});co.setSize(null,this.$editable.outerHeight());this.$codable.data("cmEditor",co)}else{this.$codable.on("blur",function(cr){cq.context.triggerEvent("blur.codeview",cq.$codable.val(),cr)});this.$codable.on("input",function(cr){cq.context.triggerEvent("change.codeview",cq.$codable.val(),cq.$codable)})}};e.prototype.deactivate=function(){if(aj.hasCodeMirror){var cq=this.$codable.data("cmEditor");this.$codable.val(cq.getValue());cq.toTextArea()}var cp=this.purify(bO.value(this.$codable,this.options.prettifyHtml)||bO.emptyPara);var co=this.$editable.html()!==cp;this.$editable.html(cp);this.$editable.height(this.options.height?this.$codable.height():"auto");this.$editor.removeClass("codeview");if(co){this.context.triggerEvent("change",this.$editable.html(),this.$editable)}this.$editable.focus();this.context.invoke("toolbar.updateCodeview",false)};e.prototype.destroy=function(){if(this.isActivated()){this.deactivate()}};return e}());var bm=24;var aO=(function(){function e(co){this.$document=a3(document);this.$statusbar=co.layoutInfo.statusbar;this.$editable=co.layoutInfo.editable;this.options=co.options}e.prototype.initialize=function(){var co=this;if(this.options.airMode||this.options.disableResizeEditor){this.destroy();return}this.$statusbar.on("mousedown",function(cq){cq.preventDefault();cq.stopPropagation();var cp=co.$editable.offset().top-co.$document.scrollTop();var cr=function(ct){var cs=ct.clientY-(cp+bm);cs=(co.options.minheight>0)?Math.max(cs,co.options.minheight):cs;cs=(co.options.maxHeight>0)?Math.min(cs,co.options.maxHeight):cs;co.$editable.height(cs)};co.$document.on("mousemove",cr).one("mouseup",function(){co.$document.off("mousemove",cr)})})};e.prototype.destroy=function(){this.$statusbar.off();this.$statusbar.addClass("locked")};return e}());var bj=(function(){function e(co){var cp=this;this.context=co;this.$editor=co.layoutInfo.editor;this.$toolbar=co.layoutInfo.toolbar;this.$editable=co.layoutInfo.editable;this.$codable=co.layoutInfo.codable;this.$window=a3(window);this.$scrollbar=a3("html, body");this.onResize=function(){cp.resizeTo({h:cp.$window.height()-cp.$toolbar.outerHeight()})}}e.prototype.resizeTo=function(co){this.$editable.css("height",co.h);this.$codable.css("height",co.h);if(this.$codable.data("cmeditor")){this.$codable.data("cmeditor").setsize(null,co.h)}};e.prototype.toggle=function(){this.$editor.toggleClass("fullscreen");if(this.isFullscreen()){this.$editable.data("orgHeight",this.$editable.css("height"));this.$editable.data("orgMaxHeight",this.$editable.css("maxHeight"));this.$editable.css("maxHeight","");this.$window.on("resize",this.onResize).trigger("resize");this.$scrollbar.css("overflow","hidden")}else{this.$window.off("resize",this.onResize);this.resizeTo({h:this.$editable.data("orgHeight")});this.$editable.css("maxHeight",this.$editable.css("orgMaxHeight"));this.$scrollbar.css("overflow","visible")}this.context.invoke("toolbar.updateFullscreen",this.isFullscreen())};e.prototype.isFullscreen=function(){return this.$editor.hasClass("fullscreen")};return e}());var t=(function(){function e(co){var cp=this;this.context=co;this.$document=a3(document);this.$editingArea=co.layoutInfo.editingArea;this.options=co.options;this.lang=this.options.langInfo;this.events={"summernote.mousedown":function(cq,cr){if(cp.update(cr.target)){cr.preventDefault()}},"summernote.keyup summernote.scroll summernote.change summernote.dialog.shown":function(){cp.update()},"summernote.disable":function(){cp.hide()},"summernote.codeview.toggled":function(){cp.update()}}}e.prototype.initialize=function(){var co=this;this.$handle=a3(['<div class="note-handle">','<div class="note-control-selection">','<div class="note-control-selection-bg"></div>','<div class="note-control-holder note-control-nw"></div>','<div class="note-control-holder note-control-ne"></div>','<div class="note-control-holder note-control-sw"></div>','<div class="',(this.options.disableResizeImage?"note-control-holder":"note-control-sizing"),' note-control-se"></div>',(this.options.disableResizeImage?"":'<div class="note-control-selection-info"></div>'),"</div>","</div>",].join("")).prependTo(this.$editingArea);this.$handle.on("mousedown",function(cp){if(bO.isControlSizing(cp.target)){cp.preventDefault();cp.stopPropagation();var cq=co.$handle.find(".note-control-selection").data("target");var ct=cq.offset();var cs=co.$document.scrollTop();var cr=function(cu){co.context.invoke("editor.resizeTo",{x:cu.clientX-ct.left,y:cu.clientY-(ct.top-cs)},cq,!cu.shiftKey);co.update(cq[0])};co.$document.on("mousemove",cr).one("mouseup",function(cu){cu.preventDefault();co.$document.off("mousemove",cr);co.context.invoke("editor.afterCommand")});if(!cq.data("ratio")){cq.data("ratio",cq.height()/cq.width())}}});this.$handle.on("wheel",function(cp){cp.preventDefault();co.update()})};e.prototype.destroy=function(){this.$handle.remove()};e.prototype.update=function(ct){if(this.context.isDisabled()){return false}var cp=bO.isImg(ct);var cr=this.$handle.find(".note-control-selection");this.context.invoke("imagePopover.update",ct);if(cp){var cq=a3(ct);var cs=cq.position();var cv={left:cs.left+parseInt(cq.css("marginLeft"),10),top:cs.top+parseInt(cq.css("marginTop"),10)};var cu={w:cq.outerWidth(false),h:cq.outerHeight(false)};cr.css({display:"block",left:cv.left,top:cv.top,width:cu.w,height:cu.h}).data("target",cq);var co=new Image();co.src=cq.attr("src");var cw=cu.w+"x"+cu.h+" ("+this.lang.image.original+": "+co.width+"x"+co.height+")";cr.find(".note-control-selection-info").text(cw);this.context.invoke("editor.saveTarget",ct)}else{this.hide()}return cp};e.prototype.hide=function(){this.context.invoke("editor.clearTarget");this.$handle.children().hide()};return e}());var aI="http://";var aH=/^([A-Za-z][A-Za-z0-9+-.]*\:[\/]{2}|mailto:[A-Z0-9._%+-]+@)?(www\.)?(.+)$/i;var ah=(function(){function e(co){var cp=this;this.context=co;this.events={"summernote.keyup":function(cq,cr){if(!cr.isDefaultPrevented()){cp.handleKeyup(cr)}},"summernote.keydown":function(cq,cr){cp.handleKeydown(cr)}}}e.prototype.initialize=function(){this.lastWordRange=null};e.prototype.destroy=function(){this.lastWordRange=null};e.prototype.replace=function(){if(!this.lastWordRange){return}var co=this.lastWordRange.toString();var cp=co.match(aH);if(cp&&(cp[1]||cp[2])){var cr=cp[1]?co:aI+co;var cq=a3("<a />").html(co).attr("href",cr)[0];if(this.context.options.linkTargetBlank){a3(cq).attr("target","_blank")}this.lastWordRange.insertNode(cq);this.lastWordRange=null;this.context.invoke("editor.focus")}};e.prototype.handleKeydown=function(co){if(n.contains([i.code.ENTER,i.code.SPACE],co.keyCode)){var cp=this.context.invoke("editor.createRange").getWordRange();this.lastWordRange=cp}};e.prototype.handleKeyup=function(co){if(n.contains([i.code.ENTER,i.code.SPACE],co.keyCode)){this.replace()}};return e}());var bv=(function(){function e(co){var cp=this;this.$note=co.layoutInfo.note;this.events={"summernote.change":function(){cp.$note.val(co.invoke("code"))}}}e.prototype.shouldInitialize=function(){return bO.isTextarea(this.$note[0])};return e}());var D=(function(){function e(co){var cp=this;this.context=co;this.options=co.options.replace||{};this.keys=[i.code.ENTER,i.code.SPACE,i.code.PERIOD,i.code.COMMA,i.code.SEMICOLON,i.code.SLASH];this.previousKeydownCode=null;this.events={"summernote.keyup":function(cq,cr){if(!cr.isDefaultPrevented()){cp.handleKeyup(cr)}},"summernote.keydown":function(cq,cr){cp.handleKeydown(cr)}}}e.prototype.shouldInitialize=function(){return !!this.options.match};e.prototype.initialize=function(){this.lastWord=null};e.prototype.destroy=function(){this.lastWord=null};e.prototype.replace=function(){if(!this.lastWord){return}var cp=this;var co=this.lastWord.toString();this.options.match(co,function(cq){if(cq){var cr="";if(typeof cq==="string"){cr=bO.createText(cq)}else{if(cq instanceof jQuery){cr=cq[0]}else{if(cq instanceof Node){cr=cq}}}if(!cr){return}cp.lastWord.insertNode(cr);cp.lastWord=null;cp.context.invoke("editor.focus")}})};e.prototype.handleKeydown=function(co){if(this.previousKeydownCode&&n.contains(this.keys,this.previousKeydownCode)){this.previousKeydownCode=co.keyCode;return}if(n.contains(this.keys,co.keyCode)){var cp=this.context.invoke("editor.createRange").getWordRange();this.lastWord=cp}this.previousKeydownCode=co.keyCode};e.prototype.handleKeyup=function(co){if(n.contains(this.keys,co.keyCode)){this.replace()}};return e}());var aF=(function(){function e(co){var cp=this;this.context=co;this.$editingArea=co.layoutInfo.editingArea;this.options=co.options;this.events={"summernote.init summernote.change":function(){cp.update()},"summernote.codeview.toggled":function(){cp.update()}}}e.prototype.shouldInitialize=function(){return !!this.options.placeholder};e.prototype.initialize=function(){var co=this;this.$placeholder=a3('<div class="note-placeholder">');this.$placeholder.on("click",function(){co.context.invoke("focus")}).html(this.options.placeholder).prependTo(this.$editingArea);this.update()};e.prototype.destroy=function(){this.$placeholder.remove()};e.prototype.update=function(){var co=!this.context.invoke("codeview.isActivated")&&this.context.invoke("editor.isEmpty");this.$placeholder.toggle(co)};return e}());var aq=(function(){function e(co){this.ui=a3.summernote.ui;this.context=co;this.$toolbar=co.layoutInfo.toolbar;this.options=co.options;this.lang=this.options.langInfo;this.invertedKeyMap=am.invertObject(this.options.keyMap[aj.isMac?"mac":"pc"])}e.prototype.representShortcut=function(cp){var co=this.invertedKeyMap[cp];if(!this.options.shortcuts||!co){return""}if(aj.isMac){co=co.replace("CMD","⌘").replace("SHIFT","⇧")}co=co.replace("BACKSLASH","\\").replace("SLASH","/").replace("LEFTBRACKET","[").replace("RIGHTBRACKET","]");return" ("+co+")"};e.prototype.button=function(co){if(!this.options.tooltip&&co.tooltip){delete co.tooltip}co.container=this.options.container;return this.ui.button(co)};e.prototype.initialize=function(){this.addToolbarButtons();this.addImagePopoverButtons();this.addLinkPopoverButtons();this.addTablePopoverButtons();this.fontInstalledMap={}};e.prototype.destroy=function(){delete this.fontInstalledMap};e.prototype.isFontInstalled=function(co){if(!this.fontInstalledMap.hasOwnProperty(co)){this.fontInstalledMap[co]=aj.isFontInstalled(co)||n.contains(this.options.fontNamesIgnoreCheck,co)}return this.fontInstalledMap[co]};e.prototype.isFontDeservedToAdd=function(co){var cp=["sans-serif","serif","monospace","cursive","fantasy"];co=co.toLowerCase();return((co!=="")&&this.isFontInstalled(co)&&(a3.inArray(co,cp)===-1))};e.prototype.colorPalette=function(cp,cq,co,cr){var cs=this;return this.ui.buttonGroup({className:"note-color "+cp,children:[this.button({className:"note-current-color-button",contents:this.ui.icon(this.options.icons.font+" note-recent-color"),tooltip:cq,click:function(cu){var ct=a3(cu.currentTarget);if(co&&cr){cs.context.invoke("editor.color",{backColor:ct.attr("data-backColor"),foreColor:ct.attr("data-foreColor")})}else{if(co){cs.context.invoke("editor.color",{backColor:ct.attr("data-backColor")})}else{if(cr){cs.context.invoke("editor.color",{foreColor:ct.attr("data-foreColor")})}}}},callback:function(cu){var ct=cu.find(".note-recent-color");if(co){ct.css("background-color",cs.options.colorButton.backColor);cu.attr("data-backColor",cs.options.colorButton.backColor)}if(cr){ct.css("color",cs.options.colorButton.foreColor);cu.attr("data-foreColor",cs.options.colorButton.foreColor)}else{ct.css("color","transparent")}}}),this.button({className:"dropdown-toggle",contents:this.ui.dropdownButtonContents("",this.options),tooltip:this.lang.color.more,data:{toggle:"dropdown"}}),this.ui.dropdown({items:(co?['<div class="note-palette">','  <div class="note-palette-title">'+this.lang.color.background+"</div>","  <div>",'    <button type="button" class="note-color-reset btn btn-light" data-event="backColor" data-value="inherit">',this.lang.color.transparent,"    </button>","  </div>",'  <div class="note-holder" data-event="backColor"/>',"  <div>",'    <button type="button" class="note-color-select btn" data-event="openPalette" data-value="backColorPicker">',this.lang.color.cpSelect,"    </button>",'    <input type="color" id="backColorPicker" class="note-btn note-color-select-btn" value="'+this.options.colorButton.backColor+'" data-event="backColorPalette">',"  </div>",'  <div class="note-holder-custom" id="backColorPalette" data-event="backColor"/>',"</div>",].join(""):"")+(cr?['<div class="note-palette">','  <div class="note-palette-title">'+this.lang.color.foreground+"</div>","  <div>",'    <button type="button" class="note-color-reset btn btn-light" data-event="removeFormat" data-value="foreColor">',this.lang.color.resetToDefault,"    </button>","  </div>",'  <div class="note-holder" data-event="foreColor"/>',"  <div>",'    <button type="button" class="note-color-select btn" data-event="openPalette" data-value="foreColorPicker">',this.lang.color.cpSelect,"    </button>",'    <input type="color" id="foreColorPicker" class="note-btn note-color-select-btn" value="'+this.options.colorButton.foreColor+'" data-event="foreColorPalette">','  <div class="note-holder-custom" id="foreColorPalette" data-event="foreColor"/>',"</div>",].join(""):""),callback:function(ct){ct.find(".note-holder").each(function(cv,cw){var cx=a3(cw);cx.append(cs.ui.palette({colors:cs.options.colors,colorsName:cs.options.colorsName,eventName:cx.data("event"),container:cs.options.container,tooltip:cs.options.tooltip}).render())});var cu=[["#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF"],];ct.find(".note-holder-custom").each(function(cv,cw){var cx=a3(cw);cx.append(cs.ui.palette({colors:cu,colorsName:cu,eventName:cx.data("event"),container:cs.options.container,tooltip:cs.options.tooltip}).render())});ct.find("input[type=color]").each(function(cv,cw){a3(cw).change(function(){var cy=ct.find("#"+a3(this).data("event")).find(".note-color-btn").first();var cx=this.value.toUpperCase();cy.css("background-color",cx).attr("aria-label",cx).attr("data-value",cx).attr("data-original-title",cx);cy.click()})})},click:function(cu){cu.stopPropagation();var cz=a3("."+cp);var ct=a3(cu.target);var cA=ct.data("event");var cC=ct.attr("data-value");if(cA==="openPalette"){var cv=cz.find("#"+cC);var cB=a3(cz.find("#"+cv.data("event")).find(".note-color-row")[0]);var cw=cB.find(".note-color-btn").last().detach();var cy=cv.val();cw.css("background-color",cy).attr("aria-label",cy).attr("data-value",cy).attr("data-original-title",cy);cB.prepend(cw);cv.click()}else{if(n.contains(["backColor","foreColor"],cA)){var cE=cA==="backColor"?"background-color":"color";var cx=ct.closest(".note-color").find(".note-recent-color");var cD=ct.closest(".note-color").find(".note-current-color-button");cx.css(cE,cC);cD.attr("data-"+cA,cC);cs.context.invoke("editor."+cA,cC)}}}}),]}).render()};e.prototype.addToolbarButtons=function(){var cu=this;this.context.memo("button.style",function(){return cu.ui.buttonGroup([cu.button({className:"dropdown-toggle",contents:cu.ui.dropdownButtonContents(cu.ui.icon(cu.options.icons.magic),cu.options),tooltip:cu.lang.style.style,data:{toggle:"dropdown"}}),cu.ui.dropdown({className:"dropdown-style",items:cu.options.styleTags,title:cu.lang.style.style,template:function(cC){if(typeof cC==="string"){cC={tag:cC,title:(cu.lang.style.hasOwnProperty(cC)?cu.lang.style[cC]:cC)}}var cz=cC.tag;var cD=cC.title;var cB=cC.style?' style="'+cC.style+'" ':"";var cA=cC.className?' class="'+cC.className+'"':"";return"<"+cz+cB+cA+">"+cD+"</"+cz+">"},click:cu.context.createInvokeHandler("editor.formatBlock")}),]).render()});var cp=function(cA,cz){var cB=cx.options.styleTags[cA];cx.context.memo("button.style."+cB,function(){return cu.button({className:"note-btn-style-"+cB,contents:'<div data-value="'+cB+'">'+cB.toUpperCase()+"</div>",tooltip:cu.lang.style[cB],click:cu.context.createInvokeHandler("editor.formatBlock")}).render()})};var cx=this;for(var cs=0,cq=this.options.styleTags.length;cs<cq;cs++){cp(cs,cq)}this.context.memo("button.bold",function(){return cu.button({className:"note-btn-bold",contents:cu.ui.icon(cu.options.icons.bold),tooltip:cu.lang.font.bold+cu.representShortcut("bold"),click:cu.context.createInvokeHandlerAndUpdateState("editor.bold")}).render()});this.context.memo("button.italic",function(){return cu.button({className:"note-btn-italic",contents:cu.ui.icon(cu.options.icons.italic),tooltip:cu.lang.font.italic+cu.representShortcut("italic"),click:cu.context.createInvokeHandlerAndUpdateState("editor.italic")}).render()});this.context.memo("button.underline",function(){return cu.button({className:"note-btn-underline",contents:cu.ui.icon(cu.options.icons.underline),tooltip:cu.lang.font.underline+cu.representShortcut("underline"),click:cu.context.createInvokeHandlerAndUpdateState("editor.underline")}).render()});this.context.memo("button.clear",function(){return cu.button({contents:cu.ui.icon(cu.options.icons.eraser),tooltip:cu.lang.font.clear+cu.representShortcut("removeFormat"),click:cu.context.createInvokeHandler("editor.removeFormat")}).render()});this.context.memo("button.strikethrough",function(){return cu.button({className:"note-btn-strikethrough",contents:cu.ui.icon(cu.options.icons.strikethrough),tooltip:cu.lang.font.strikethrough+cu.representShortcut("strikethrough"),click:cu.context.createInvokeHandlerAndUpdateState("editor.strikethrough")}).render()});this.context.memo("button.superscript",function(){return cu.button({className:"note-btn-superscript",contents:cu.ui.icon(cu.options.icons.superscript),tooltip:cu.lang.font.superscript,click:cu.context.createInvokeHandlerAndUpdateState("editor.superscript")}).render()});this.context.memo("button.subscript",function(){return cu.button({className:"note-btn-subscript",contents:cu.ui.icon(cu.options.icons.subscript),tooltip:cu.lang.font.subscript,click:cu.context.createInvokeHandlerAndUpdateState("editor.subscript")}).render()});this.context.memo("button.fontname",function(){var cz=cu.context.invoke("editor.currentStyle");a3.each(cz["font-family"].split(","),function(cA,cB){cB=cB.trim().replace(/['"]+/g,"");if(cu.isFontDeservedToAdd(cB)){if(a3.inArray(cB,cu.options.fontNames)===-1){cu.options.fontNames.push(cB)}}});return cu.ui.buttonGroup([cu.button({className:"dropdown-toggle",contents:cu.ui.dropdownButtonContents('<span class="note-current-fontname"/>',cu.options),tooltip:cu.lang.font.name,data:{toggle:"dropdown"}}),cu.ui.dropdownCheck({className:"dropdown-fontname",checkClassName:cu.options.icons.menuCheck,items:cu.options.fontNames.filter(cu.isFontInstalled.bind(cu)),title:cu.lang.font.name,template:function(cA){return"<span style=\"font-family: '"+cA+"'\">"+cA+"</span>"},click:cu.context.createInvokeHandlerAndUpdateState("editor.fontName")}),]).render()});this.context.memo("button.fontsize",function(){return cu.ui.buttonGroup([cu.button({className:"dropdown-toggle",contents:cu.ui.dropdownButtonContents('<span class="note-current-fontsize"/>',cu.options),tooltip:cu.lang.font.size,data:{toggle:"dropdown"}}),cu.ui.dropdownCheck({className:"dropdown-fontsize",checkClassName:cu.options.icons.menuCheck,items:cu.options.fontSizes,title:cu.lang.font.size,click:cu.context.createInvokeHandlerAndUpdateState("editor.fontSize")}),]).render()});this.context.memo("button.color",function(){return cu.colorPalette("note-color-all",cu.lang.color.recent,true,true)});this.context.memo("button.forecolor",function(){return cu.colorPalette("note-color-fore",cu.lang.color.foreground,false,true)});this.context.memo("button.backcolor",function(){return cu.colorPalette("note-color-back",cu.lang.color.background,true,false)});this.context.memo("button.ul",function(){return cu.button({contents:cu.ui.icon(cu.options.icons.unorderedlist),tooltip:cu.lang.lists.unordered+cu.representShortcut("insertUnorderedList"),click:cu.context.createInvokeHandler("editor.insertUnorderedList")}).render()});this.context.memo("button.ol",function(){return cu.button({contents:cu.ui.icon(cu.options.icons.orderedlist),tooltip:cu.lang.lists.ordered+cu.representShortcut("insertOrderedList"),click:cu.context.createInvokeHandler("editor.insertOrderedList")}).render()});var ct=this.button({contents:this.ui.icon(this.options.icons.alignLeft),tooltip:this.lang.paragraph.left+this.representShortcut("justifyLeft"),click:this.context.createInvokeHandler("editor.justifyLeft")});var co=this.button({contents:this.ui.icon(this.options.icons.alignCenter),tooltip:this.lang.paragraph.center+this.representShortcut("justifyCenter"),click:this.context.createInvokeHandler("editor.justifyCenter")});var cy=this.button({contents:this.ui.icon(this.options.icons.alignRight),tooltip:this.lang.paragraph.right+this.representShortcut("justifyRight"),click:this.context.createInvokeHandler("editor.justifyRight")});var cw=this.button({contents:this.ui.icon(this.options.icons.alignJustify),tooltip:this.lang.paragraph.justify+this.representShortcut("justifyFull"),click:this.context.createInvokeHandler("editor.justifyFull")});var cv=this.button({contents:this.ui.icon(this.options.icons.outdent),tooltip:this.lang.paragraph.outdent+this.representShortcut("outdent"),click:this.context.createInvokeHandler("editor.outdent")});var cr=this.button({contents:this.ui.icon(this.options.icons.indent),tooltip:this.lang.paragraph.indent+this.representShortcut("indent"),click:this.context.createInvokeHandler("editor.indent")});this.context.memo("button.justifyLeft",am.invoke(ct,"render"));this.context.memo("button.justifyCenter",am.invoke(co,"render"));this.context.memo("button.justifyRight",am.invoke(cy,"render"));this.context.memo("button.justifyFull",am.invoke(cw,"render"));this.context.memo("button.outdent",am.invoke(cv,"render"));this.context.memo("button.indent",am.invoke(cr,"render"));this.context.memo("button.paragraph",function(){return cu.ui.buttonGroup([cu.button({className:"dropdown-toggle",contents:cu.ui.dropdownButtonContents(cu.ui.icon(cu.options.icons.alignLeft),cu.options),tooltip:cu.lang.paragraph.paragraph,data:{toggle:"dropdown"}}),cu.ui.dropdown([cu.ui.buttonGroup({className:"note-align",children:[ct,co,cy,cw]}),cu.ui.buttonGroup({className:"note-list",children:[cv,cr]}),]),]).render()});this.context.memo("button.height",function(){return cu.ui.buttonGroup([cu.button({className:"dropdown-toggle",contents:cu.ui.dropdownButtonContents(cu.ui.icon(cu.options.icons.textHeight),cu.options),tooltip:cu.lang.font.height,data:{toggle:"dropdown"}}),cu.ui.dropdownCheck({items:cu.options.lineHeights,checkClassName:cu.options.icons.menuCheck,className:"dropdown-line-height",title:cu.lang.font.height,click:cu.context.createInvokeHandler("editor.lineHeight")}),]).render()});this.context.memo("button.table",function(){return cu.ui.buttonGroup([cu.button({className:"dropdown-toggle",contents:cu.ui.dropdownButtonContents(cu.ui.icon(cu.options.icons.table),cu.options),tooltip:cu.lang.table.table,data:{toggle:"dropdown"}}),cu.ui.dropdown({title:cu.lang.table.table,className:"note-table",items:['<div class="note-dimension-picker">','  <div class="note-dimension-picker-mousecatcher" data-event="insertTable" data-value="1x1"/>','  <div class="note-dimension-picker-highlighted"/>','  <div class="note-dimension-picker-unhighlighted"/>',"</div>",'<div class="note-dimension-display">1 x 1</div>',].join("")}),],{callback:function(cz){var cA=cz.find(".note-dimension-picker-mousecatcher");cA.css({width:cu.options.insertTableMaxSize.col+"em",height:cu.options.insertTableMaxSize.row+"em"}).mousedown(cu.context.createInvokeHandler("editor.insertTable")).on("mousemove",cu.tableMoveHandler.bind(cu))}}).render()});this.context.memo("button.link",function(){return cu.button({contents:cu.ui.icon(cu.options.icons.link),tooltip:cu.lang.link.link+cu.representShortcut("linkDialog.show"),click:cu.context.createInvokeHandler("linkDialog.show")}).render()});this.context.memo("button.picture",function(){return cu.button({contents:cu.ui.icon(cu.options.icons.picture),tooltip:cu.lang.image.image,click:cu.context.createInvokeHandler("imageDialog.show")}).render()});this.context.memo("button.video",function(){return cu.button({contents:cu.ui.icon(cu.options.icons.video),tooltip:cu.lang.video.video,click:cu.context.createInvokeHandler("videoDialog.show")}).render()});this.context.memo("button.hr",function(){return cu.button({contents:cu.ui.icon(cu.options.icons.minus),tooltip:cu.lang.hr.insert+cu.representShortcut("insertHorizontalRule"),click:cu.context.createInvokeHandler("editor.insertHorizontalRule")}).render()});this.context.memo("button.fullscreen",function(){return cu.button({className:"btn-fullscreen",contents:cu.ui.icon(cu.options.icons.arrowsAlt),tooltip:cu.lang.options.fullscreen,click:cu.context.createInvokeHandler("fullscreen.toggle")}).render()});this.context.memo("button.codeview",function(){return cu.button({className:"btn-codeview",contents:cu.ui.icon(cu.options.icons.code),tooltip:cu.lang.options.codeview,click:cu.context.createInvokeHandler("codeview.toggle")}).render()});this.context.memo("button.redo",function(){return cu.button({contents:cu.ui.icon(cu.options.icons.redo),tooltip:cu.lang.history.redo+cu.representShortcut("redo"),click:cu.context.createInvokeHandler("editor.redo")}).render()});this.context.memo("button.undo",function(){return cu.button({contents:cu.ui.icon(cu.options.icons.undo),tooltip:cu.lang.history.undo+cu.representShortcut("undo"),click:cu.context.createInvokeHandler("editor.undo")}).render()});this.context.memo("button.help",function(){return cu.button({contents:cu.ui.icon(cu.options.icons.question),tooltip:cu.lang.options.help,click:cu.context.createInvokeHandler("helpDialog.show")}).render()})};e.prototype.addImagePopoverButtons=function(){var co=this;this.context.memo("button.resizeFull",function(){return co.button({contents:'<span class="note-fontsize-10">100%</span>',tooltip:co.lang.image.resizeFull,click:co.context.createInvokeHandler("editor.resize","1")}).render()});this.context.memo("button.resizeHalf",function(){return co.button({contents:'<span class="note-fontsize-10">50%</span>',tooltip:co.lang.image.resizeHalf,click:co.context.createInvokeHandler("editor.resize","0.5")}).render()});this.context.memo("button.resizeQuarter",function(){return co.button({contents:'<span class="note-fontsize-10">25%</span>',tooltip:co.lang.image.resizeQuarter,click:co.context.createInvokeHandler("editor.resize","0.25")}).render()});this.context.memo("button.resizeNone",function(){return co.button({contents:co.ui.icon(co.options.icons.rollback),tooltip:co.lang.image.resizeNone,click:co.context.createInvokeHandler("editor.resize","0")}).render()});this.context.memo("button.floatLeft",function(){return co.button({contents:co.ui.icon(co.options.icons.floatLeft),tooltip:co.lang.image.floatLeft,click:co.context.createInvokeHandler("editor.floatMe","left")}).render()});this.context.memo("button.floatRight",function(){return co.button({contents:co.ui.icon(co.options.icons.floatRight),tooltip:co.lang.image.floatRight,click:co.context.createInvokeHandler("editor.floatMe","right")}).render()});this.context.memo("button.floatNone",function(){return co.button({contents:co.ui.icon(co.options.icons.rollback),tooltip:co.lang.image.floatNone,click:co.context.createInvokeHandler("editor.floatMe","none")}).render()});this.context.memo("button.removeMedia",function(){return co.button({contents:co.ui.icon(co.options.icons.trash),tooltip:co.lang.image.remove,click:co.context.createInvokeHandler("editor.removeMedia")}).render()})};e.prototype.addLinkPopoverButtons=function(){var co=this;this.context.memo("button.linkDialogShow",function(){return co.button({contents:co.ui.icon(co.options.icons.link),tooltip:co.lang.link.edit,click:co.context.createInvokeHandler("linkDialog.show")}).render()});this.context.memo("button.unlink",function(){return co.button({contents:co.ui.icon(co.options.icons.unlink),tooltip:co.lang.link.unlink,click:co.context.createInvokeHandler("editor.unlink")}).render()})};e.prototype.addTablePopoverButtons=function(){var co=this;this.context.memo("button.addRowUp",function(){return co.button({className:"btn-md",contents:co.ui.icon(co.options.icons.rowAbove),tooltip:co.lang.table.addRowAbove,click:co.context.createInvokeHandler("editor.addRow","top")}).render()});this.context.memo("button.addRowDown",function(){return co.button({className:"btn-md",contents:co.ui.icon(co.options.icons.rowBelow),tooltip:co.lang.table.addRowBelow,click:co.context.createInvokeHandler("editor.addRow","bottom")}).render()});this.context.memo("button.addColLeft",function(){return co.button({className:"btn-md",contents:co.ui.icon(co.options.icons.colBefore),tooltip:co.lang.table.addColLeft,click:co.context.createInvokeHandler("editor.addCol","left")}).render()});this.context.memo("button.addColRight",function(){return co.button({className:"btn-md",contents:co.ui.icon(co.options.icons.colAfter),tooltip:co.lang.table.addColRight,click:co.context.createInvokeHandler("editor.addCol","right")}).render()});this.context.memo("button.deleteRow",function(){return co.button({className:"btn-md",contents:co.ui.icon(co.options.icons.rowRemove),tooltip:co.lang.table.delRow,click:co.context.createInvokeHandler("editor.deleteRow")}).render()});this.context.memo("button.deleteCol",function(){return co.button({className:"btn-md",contents:co.ui.icon(co.options.icons.colRemove),tooltip:co.lang.table.delCol,click:co.context.createInvokeHandler("editor.deleteCol")}).render()});this.context.memo("button.deleteTable",function(){return co.button({className:"btn-md",contents:co.ui.icon(co.options.icons.trash),tooltip:co.lang.table.delTable,click:co.context.createInvokeHandler("editor.deleteTable")}).render()})};e.prototype.build=function(cx,cp){for(var cy=0,cr=cp.length;cy<cr;cy++){var cv=cp[cy];var cw=a3.isArray(cv)?cv[0]:cv;var cs=a3.isArray(cv)?((cv.length===1)?[cv[0]]:cv[1]):[cv];var cu=this.ui.buttonGroup({className:"note-"+cw}).render();for(var ct=0,cq=cs.length;ct<cq;ct++){var co=this.context.memo("button."+cs[ct]);if(co){cu.append(typeof co==="function"?co():co)}}cu.appendTo(cx)}};e.prototype.updateCurrentStyle=function(ct){var cv=this;var cr=ct||this.$toolbar;var cs=this.context.invoke("editor.currentStyle");this.updateBtnStates(cr,{".note-btn-bold":function(){return cs["font-bold"]==="bold"},".note-btn-italic":function(){return cs["font-italic"]==="italic"},".note-btn-underline":function(){return cs["font-underline"]==="underline"},".note-btn-subscript":function(){return cs["font-subscript"]==="subscript"},".note-btn-superscript":function(){return cs["font-superscript"]==="superscript"},".note-btn-strikethrough":function(){return cs["font-strikethrough"]==="strikethrough"}});if(cs["font-family"]){var co=cs["font-family"].split(",").map(function(cw){return cw.replace(/[\'\"]/g,"").replace(/\s+$/,"").replace(/^\s+/,"")});var cu=n.find(co,this.isFontInstalled.bind(this));cr.find(".dropdown-fontname a").each(function(cw,cy){var cx=a3(cy);var cz=(cx.data("value")+"")===(cu+"");cx.toggleClass("checked",cz)});cr.find(".note-current-fontname").text(cu).css("font-family",cu)}if(cs["font-size"]){var cp=cs["font-size"];cr.find(".dropdown-fontsize a").each(function(cw,cy){var cx=a3(cy);var cz=(cx.data("value")+"")===(cp+"");cx.toggleClass("checked",cz)});cr.find(".note-current-fontsize").text(cp)}if(cs["line-height"]){var cq=cs["line-height"];cr.find(".dropdown-line-height li a").each(function(cw,cx){var cy=(a3(cx).data("value")+"")===(cq+"");cv.className=cy?"checked":""})}};e.prototype.updateBtnStates=function(cp,co){var cq=this;a3.each(co,function(cr,cs){cq.ui.toggleBtnActive(cp.find(cr),cs())})};e.prototype.tableMoveHandler=function(cp){var cx=18;var cr=a3(cp.target.parentNode);var cq=cr.next();var cw=cr.find(".note-dimension-picker-mousecatcher");var cs=cr.find(".note-dimension-picker-highlighted");var ct=cr.find(".note-dimension-picker-unhighlighted");var cv;if(cp.offsetX===undefined){var co=a3(cp.target).offset();cv={x:cp.pageX-co.left,y:cp.pageY-co.top}}else{cv={x:cp.offsetX,y:cp.offsetY}}var cu={c:Math.ceil(cv.x/cx)||1,r:Math.ceil(cv.y/cx)||1};cs.css({width:cu.c+"em",height:cu.r+"em"});cw.data("value",cu.c+"x"+cu.r);if(cu.c>3&&cu.c<this.options.insertTableMaxSize.col){ct.css({width:cu.c+1+"em"})}if(cu.r>3&&cu.r<this.options.insertTableMaxSize.row){ct.css({height:cu.r+1+"em"})}cq.html(cu.c+" x "+cu.r)};return e}());var bx=(function(){function e(co){this.context=co;this.$window=a3(window);this.$document=a3(document);this.ui=a3.summernote.ui;this.$note=co.layoutInfo.note;this.$editor=co.layoutInfo.editor;this.$toolbar=co.layoutInfo.toolbar;this.$editable=co.layoutInfo.editable;this.$statusbar=co.layoutInfo.statusbar;this.options=co.options;this.isFollowing=false;this.followScroll=this.followScroll.bind(this)}e.prototype.shouldInitialize=function(){return !this.options.airMode};e.prototype.initialize=function(){var co=this;this.options.toolbar=this.options.toolbar||[];if(!this.options.toolbar.length){this.$toolbar.hide()}else{this.context.invoke("buttons.build",this.$toolbar,this.options.toolbar)}if(this.options.toolbarContainer){this.$toolbar.appendTo(this.options.toolbarContainer)}this.changeContainer(false);this.$note.on("summernote.keyup summernote.mouseup summernote.change",function(){co.context.invoke("buttons.updateCurrentStyle")});this.context.invoke("buttons.updateCurrentStyle");if(this.options.followingToolbar){this.$window.on("scroll resize",this.followScroll)}};e.prototype.destroy=function(){this.$toolbar.children().remove();if(this.options.followingToolbar){this.$window.off("scroll resize",this.followScroll)}};e.prototype.followScroll=function(){if(this.$editor.hasClass("fullscreen")){return false}var cr=this.$editor.outerHeight();var cp=this.$editor.width();var cu=this.$toolbar.height();var cw=this.$statusbar.height();var cv=0;if(this.options.otherStaticBar){cv=a3(this.options.otherStaticBar).outerHeight()}var ct=this.$document.scrollTop();var co=this.$editor.offset().top;var cq=co+cr;var cx=co-cv;var cs=cq-cv-cu-cw;if(!this.isFollowing&&(ct>cx)&&(ct<cs-cu)){this.isFollowing=true;this.$toolbar.css({position:"fixed",top:cv,width:cp});this.$editable.css({marginTop:this.$toolbar.height()+5})}else{if(this.isFollowing&&((ct<cx)||(ct>cs))){this.isFollowing=false;this.$toolbar.css({position:"relative",top:0,width:"100%"});this.$editable.css({marginTop:""})}}};e.prototype.changeContainer=function(co){if(co){this.$toolbar.prependTo(this.$editor)}else{if(this.options.toolbarContainer){this.$toolbar.appendTo(this.options.toolbarContainer)}}this.followScroll()};e.prototype.updateFullscreen=function(co){this.ui.toggleBtnActive(this.$toolbar.find(".btn-fullscreen"),co);this.changeContainer(co)};e.prototype.updateCodeview=function(co){this.ui.toggleBtnActive(this.$toolbar.find(".btn-codeview"),co);if(co){this.deactivate()}else{this.activate()}};e.prototype.activate=function(co){var cp=this.$toolbar.find("button");if(!co){cp=cp.not(".btn-codeview")}this.ui.toggleBtn(cp,true)};e.prototype.deactivate=function(co){var cp=this.$toolbar.find("button");if(!co){cp=cp.not(".btn-codeview")}this.ui.toggleBtn(cp,false)};return e}());var x=(function(){function e(co){this.context=co;this.ui=a3.summernote.ui;this.$body=a3(document.body);this.$editor=co.layoutInfo.editor;this.options=co.options;this.lang=this.options.langInfo;co.memo("help.linkDialog.show",this.options.langInfo.help["linkDialog.show"])}e.prototype.initialize=function(){var cq=this.options.dialogsInBody?this.$body:this.$editor;var co=['<div class="form-group note-form-group">','<label class="note-form-label">'+this.lang.link.textToDisplay+"</label>",'<input class="note-link-text form-control note-form-control note-input" type="text" />',"</div>",'<div class="form-group note-form-group">','<label class="note-form-label">'+this.lang.link.url+"</label>",'<input class="note-link-url form-control note-form-control note-input" type="text" value="http://" />',"</div>",!this.options.disableLinkTarget?a3("<div/>").append(this.ui.checkbox({className:"sn-checkbox-open-in-new-window",text:this.lang.link.openInNewWindow,checked:true}).render()).html():"",].join("");var cp="btn btn-primary note-btn note-btn-primary note-link-btn";var cr='<input type="button" href="#" class="'+cp+'" value="'+this.lang.link.insert+'" disabled>';this.$dialog=this.ui.dialog({className:"link-dialog",title:this.lang.link.insert,fade:this.options.dialogsFade,body:co,footer:cr}).render().appendTo(cq)};e.prototype.destroy=function(){this.ui.hideDialog(this.$dialog);this.$dialog.remove()};e.prototype.bindEnterKey=function(cp,co){cp.on("keypress",function(cq){if(cq.keyCode===i.code.ENTER){cq.preventDefault();co.trigger("click")}})};e.prototype.toggleLinkBtn=function(co,cp,cq){this.ui.toggleBtn(co,cp.val()&&cq.val())};e.prototype.showLinkDialog=function(co){var cp=this;return a3.Deferred(function(ct){var cs=cp.$dialog.find(".note-link-text");var cu=cp.$dialog.find(".note-link-url");var cr=cp.$dialog.find(".note-link-btn");var cq=cp.$dialog.find(".sn-checkbox-open-in-new-window input[type=checkbox]");cp.ui.onDialogShown(cp.$dialog,function(){cp.context.triggerEvent("dialog.shown");if(!co.url&&am.isValidUrl(co.text)){co.url=co.text}cs.val(co.text);var cx=function(){cp.toggleLinkBtn(cr,cs,cu);co.text=cs.val()};cs.on("input",cx).on("paste",function(){setTimeout(cx,0)});var cw=function(){cp.toggleLinkBtn(cr,cs,cu);if(!co.text){cs.val(cu.val())}};cu.on("input",cw).on("paste",function(){setTimeout(cw,0)}).val(co.url);if(!aj.isSupportTouch){cu.trigger("focus")}cp.toggleLinkBtn(cr,cs,cu);cp.bindEnterKey(cu,cr);cp.bindEnterKey(cs,cr);var cv=co.isNewWindow!==undefined?co.isNewWindow:cp.context.options.linkTargetBlank;cq.prop("checked",cv);cr.one("click",function(cy){cy.preventDefault();ct.resolve({range:co.range,url:cu.val(),text:cs.val(),isNewWindow:cq.is(":checked")});cp.ui.hideDialog(cp.$dialog)})});cp.ui.onDialogHidden(cp.$dialog,function(){cs.off("input paste keypress");cu.off("input paste keypress");cr.off("click");if(ct.state()==="pending"){ct.reject()}});cp.ui.showDialog(cp.$dialog)}).promise()};e.prototype.show=function(){var cp=this;var co=this.context.invoke("editor.getLinkInfo");this.context.invoke("editor.saveRange");this.showLinkDialog(co).then(function(cq){cp.context.invoke("editor.restoreRange");cp.context.invoke("editor.createLink",cq)}).fail(function(){cp.context.invoke("editor.restoreRange")})};return e}());var m=(function(){function e(co){var cp=this;this.context=co;this.ui=a3.summernote.ui;this.options=co.options;this.events={"summernote.keyup summernote.mouseup summernote.change summernote.scroll":function(){cp.update()},"summernote.disable summernote.dialog.shown":function(){cp.hide()}}}e.prototype.shouldInitialize=function(){return !n.isEmpty(this.options.popover.link)};e.prototype.initialize=function(){this.$popover=this.ui.popover({className:"note-link-popover",callback:function(cp){var cq=cp.find(".popover-content,.note-popover-content");cq.prepend('<span><a target="_blank"></a>&nbsp;</span>')}}).render().appendTo(this.options.container);var co=this.$popover.find(".popover-content,.note-popover-content");this.context.invoke("buttons.build",co,this.options.popover.link)};e.prototype.destroy=function(){this.$popover.remove()};e.prototype.update=function(){if(!this.context.invoke("editor.hasFocus")){this.hide();return}var co=this.context.invoke("editor.getLastRange");if(co.isCollapsed()&&co.isOnAnchor()){var cq=bO.ancestor(co.sc,bO.isAnchor);var cp=a3(cq).attr("href");this.$popover.find("a").attr("href",cp).html(cp);var cr=bO.posFromPlaceholder(cq);this.$popover.css({display:"block",left:cr.left,top:cr.top})}else{this.hide()}};e.prototype.hide=function(){this.$popover.hide()};return e}());var aD=(function(){function e(co){this.context=co;this.ui=a3.summernote.ui;this.$body=a3(document.body);this.$editor=co.layoutInfo.editor;this.options=co.options;this.lang=this.options.langInfo}e.prototype.initialize=function(){var ct=this.options.dialogsInBody?this.$body:this.$editor;var cr="";if(this.options.maximumImageFileSize){var cq=Math.floor(Math.log(this.options.maximumImageFileSize)/Math.log(1024));var cs=(this.options.maximumImageFileSize/Math.pow(1024,cq)).toFixed(2)*1+" "+" KMGTP"[cq]+"B";cr="<small>"+(this.lang.image.maximumFileSize+" : "+cs)+"</small>"}var co=['<div class="form-group note-form-group note-group-select-from-files">','<label class="note-form-label">'+this.lang.image.selectFromFiles+"</label>",'<input class="note-image-input form-control-file note-form-control note-input" ',' type="file" name="files" accept="image/*" multiple="multiple" />',cr,"</div>",'<div class="form-group note-group-image-url" style="overflow:auto;">','<label class="note-form-label">'+this.lang.image.url+"</label>",'<input class="note-image-url form-control note-form-control note-input ',' col-md-12" type="text" />',"</div>",].join("");var cp="btn btn-primary note-btn note-btn-primary note-image-btn";var cu='<input type="button" href="#" class="'+cp+'" value="'+this.lang.image.insert+'" disabled>';this.$dialog=this.ui.dialog({title:this.lang.image.insert,fade:this.options.dialogsFade,body:co,footer:cu}).render().appendTo(ct)};e.prototype.destroy=function(){this.ui.hideDialog(this.$dialog);this.$dialog.remove()};e.prototype.bindEnterKey=function(cp,co){cp.on("keypress",function(cq){if(cq.keyCode===i.code.ENTER){cq.preventDefault();co.trigger("click")}})};e.prototype.show=function(){var co=this;this.context.invoke("editor.saveRange");this.showImageDialog().then(function(cp){co.ui.hideDialog(co.$dialog);co.context.invoke("editor.restoreRange");if(typeof cp==="string"){if(co.options.callbacks.onImageLinkInsert){co.context.triggerEvent("image.link.insert",cp)}else{co.context.invoke("editor.insertImage",cp)}}else{co.context.invoke("editor.insertImagesOrCallback",cp)}}).fail(function(){co.context.invoke("editor.restoreRange")})};e.prototype.showImageDialog=function(){var co=this;return a3.Deferred(function(cr){var cs=co.$dialog.find(".note-image-input");var cp=co.$dialog.find(".note-image-url");var cq=co.$dialog.find(".note-image-btn");co.ui.onDialogShown(co.$dialog,function(){co.context.triggerEvent("dialog.shown");cs.replaceWith(cs.clone().on("change",function(ct){cr.resolve(ct.target.files||ct.target.value)}).val(""));cq.click(function(ct){ct.preventDefault();cr.resolve(cp.val())});cp.on("keyup paste",function(){var ct=cp.val();co.ui.toggleBtn(cq,ct)}).val("");if(!aj.isSupportTouch){cp.trigger("focus")}co.bindEnterKey(cp,cq)});co.ui.onDialogHidden(co.$dialog,function(){cs.off("change");cp.off("keyup paste keypress");cq.off("click");if(cr.state()==="pending"){cr.reject()}});co.ui.showDialog(co.$dialog)})};return e}());var bc=(function(){function e(co){var cp=this;this.context=co;this.ui=a3.summernote.ui;this.editable=co.layoutInfo.editable[0];this.options=co.options;this.events={"summernote.disable":function(){cp.hide()}}}e.prototype.shouldInitialize=function(){return !n.isEmpty(this.options.popover.image)};e.prototype.initialize=function(){this.$popover=this.ui.popover({className:"note-image-popover"}).render().appendTo(this.options.container);var co=this.$popover.find(".popover-content,.note-popover-content");this.context.invoke("buttons.build",co,this.options.popover.image)};e.prototype.destroy=function(){this.$popover.remove()};e.prototype.update=function(cp){if(bO.isImg(cp)){var cq=bO.posFromPlaceholder(cp);var co=bO.posFromPlaceholder(this.editable);this.$popover.css({display:"block",left:this.options.popatmouse?event.pageX-20:cq.left,top:this.options.popatmouse?event.pageY:Math.min(cq.top,co.top)})}else{this.hide()}};e.prototype.hide=function(){this.$popover.hide()};return e}());var bY=(function(){function e(co){var cp=this;this.context=co;this.ui=a3.summernote.ui;this.options=co.options;this.events={"summernote.mousedown":function(cq,cr){cp.update(cr.target)},"summernote.keyup summernote.scroll summernote.change":function(){cp.update()},"summernote.disable":function(){cp.hide()}}}e.prototype.shouldInitialize=function(){return !n.isEmpty(this.options.popover.table)};e.prototype.initialize=function(){this.$popover=this.ui.popover({className:"note-table-popover"}).render().appendTo(this.options.container);var co=this.$popover.find(".popover-content,.note-popover-content");this.context.invoke("buttons.build",co,this.options.popover.table);if(aj.isFF){document.execCommand("enableInlineTableEditing",false,false)}};e.prototype.destroy=function(){this.$popover.remove()};e.prototype.update=function(cp){if(this.context.isDisabled()){return false}var co=bO.isCell(cp);if(co){var cq=bO.posFromPlaceholder(cp);this.$popover.css({display:"block",left:cq.left,top:cq.top})}else{this.hide()}return co};e.prototype.hide=function(){this.$popover.hide()};return e}());var I=(function(){function e(co){this.context=co;this.ui=a3.summernote.ui;this.$body=a3(document.body);this.$editor=co.layoutInfo.editor;this.options=co.options;this.lang=this.options.langInfo}e.prototype.initialize=function(){var cq=this.options.dialogsInBody?this.$body:this.$editor;var co=['<div class="form-group note-form-group row-fluid">','<label class="note-form-label">'+this.lang.video.url+' <small class="text-muted">'+this.lang.video.providers+"</small></label>",'<input class="note-video-url form-control note-form-control note-input" type="text" />',"</div>",].join("");var cp="btn btn-primary note-btn note-btn-primary note-video-btn";var cr='<input type="button" href="#" class="'+cp+'" value="'+this.lang.video.insert+'" disabled>';this.$dialog=this.ui.dialog({title:this.lang.video.insert,fade:this.options.dialogsFade,body:co,footer:cr}).render().appendTo(cq)};e.prototype.destroy=function(){this.ui.hideDialog(this.$dialog);this.$dialog.remove()};e.prototype.bindEnterKey=function(cp,co){cp.on("keypress",function(cq){if(cq.keyCode===i.code.ENTER){cq.preventDefault();co.trigger("click")}})};e.prototype.createVideoNode=function(cx){var cJ=/\/\/(?:www\.)?(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))([\w|-]{11})(?:(?:[\?&]t=)(\S+))?$/;var cT=/^(?:(\d+)h)?(?:(\d+)m)?(?:(\d+)s)?$/;var cq=cx.match(cJ);var cp=/(?:www\.|\/\/)instagram\.com\/p\/(.[a-zA-Z0-9_-]*)/;var cS=cx.match(cp);var cF=/\/\/vine\.co\/v\/([a-zA-Z0-9]+)/;var cs=cx.match(cF);var cA=/\/\/(player\.)?vimeo\.com\/([a-z]*\/)*(\d+)[?]?.*/;var cG=cx.match(cA);var ct=/.+dailymotion.com\/(video|hub)\/([^_]+)[^#]*(#video=([^_&]+))?/;var co=cx.match(ct);var cR=/\/\/v\.youku\.com\/v_show\/id_(\w+)=*\.html/;var cP=cx.match(cR);var cN=/\/\/v\.qq\.com.*?vid=(.+)/;var cK=cx.match(cN);var cr=/\/\/v\.qq\.com\/x?\/?(page|cover).*?\/([^\/]+)\.html\??.*/;var cu=cx.match(cr);var cL=/^.+.(mp4|m4v)$/;var cD=cx.match(cL);var cC=/^.+.(ogg|ogv)$/;var cU=cx.match(cC);var cQ=/^.+.(webm)$/;var cz=cx.match(cQ);var cy=/(?:www\.|\/\/)facebook\.com\/([^\/]+)\/videos\/([0-9]+)/;var cV=cx.match(cy);var cB;if(cq&&cq[1].length===11){var cv=cq[1];var cw=0;if(typeof cq[2]!=="undefined"){var cI=cq[2].match(cT);if(cI){for(var cH=[3600,60,1],cM=0,cE=cH.length;cM<cE;cM++){cw+=(typeof cI[cM+1]!=="undefined"?cH[cM]*parseInt(cI[cM+1],10):0)}}}cB=a3("<iframe>").attr("frameborder",0).attr("src","//www.youtube.com/embed/"+cv+(cw>0?"?start="+cw:"")).attr("width","640").attr("height","360")}else{if(cS&&cS[0].length){cB=a3("<iframe>").attr("frameborder",0).attr("src","https://instagram.com/p/"+cS[1]+"/embed/").attr("width","612").attr("height","710").attr("scrolling","no").attr("allowtransparency","true")}else{if(cs&&cs[0].length){cB=a3("<iframe>").attr("frameborder",0).attr("src",cs[0]+"/embed/simple").attr("width","600").attr("height","600").attr("class","vine-embed")}else{if(cG&&cG[3].length){cB=a3("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("src","//player.vimeo.com/video/"+cG[3]).attr("width","640").attr("height","360")}else{if(co&&co[2].length){cB=a3("<iframe>").attr("frameborder",0).attr("src","//www.dailymotion.com/embed/video/"+co[2]).attr("width","640").attr("height","360")}else{if(cP&&cP[1].length){cB=a3("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("height","498").attr("width","510").attr("src","//player.youku.com/embed/"+cP[1])}else{if((cK&&cK[1].length)||(cu&&cu[2].length)){var cO=((cK&&cK[1].length)?cK[1]:cu[2]);cB=a3("<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>").attr("frameborder",0).attr("height","310").attr("width","500").attr("src","http://v.qq.com/iframe/player.html?vid="+cO+"&amp;auto=0")}else{if(cD||cU||cz){cB=a3("<video controls>").attr("src",cx).attr("width","640").attr("height","360")}else{if(cV&&cV[0].length){cB=a3("<iframe>").attr("frameborder",0).attr("src","https://www.facebook.com/plugins/video.php?href="+encodeURIComponent(cV[0])+"&show_text=0&width=560").attr("width","560").attr("height","301").attr("scrolling","no").attr("allowtransparency","true")}else{return false}}}}}}}}}cB.addClass("note-video-clip");return cB[0]};e.prototype.show=function(){var cp=this;var co=this.context.invoke("editor.getSelectedText");this.context.invoke("editor.saveRange");this.showVideoDialog(co).then(function(cr){cp.ui.hideDialog(cp.$dialog);cp.context.invoke("editor.restoreRange");var cq=cp.createVideoNode(cr);if(cq){cp.context.invoke("editor.insertNode",cq)}}).fail(function(){cp.context.invoke("editor.restoreRange")})};e.prototype.showVideoDialog=function(co){var cp=this;return a3.Deferred(function(cq){var cr=cp.$dialog.find(".note-video-url");var cs=cp.$dialog.find(".note-video-btn");cp.ui.onDialogShown(cp.$dialog,function(){cp.context.triggerEvent("dialog.shown");cr.val(co).on("input",function(){cp.ui.toggleBtn(cs,cr.val())});if(!aj.isSupportTouch){cr.trigger("focus")}cs.click(function(ct){ct.preventDefault();cq.resolve(cr.val())});cp.bindEnterKey(cr,cs)});cp.ui.onDialogHidden(cp.$dialog,function(){cr.off("input");cs.off("click");if(cq.state()==="pending"){cq.reject()}});cp.ui.showDialog(cp.$dialog)})};return e}());var bo=(function(){function e(co){this.context=co;this.ui=a3.summernote.ui;this.$body=a3(document.body);this.$editor=co.layoutInfo.editor;this.options=co.options;this.lang=this.options.langInfo}e.prototype.initialize=function(){var cp=this.options.dialogsInBody?this.$body:this.$editor;var co=['<p class="text-center">','<a href="http://summernote.org/" target="_blank">Summernote 0.8.11</a> · ','<a href="https://github.com/summernote/summernote" target="_blank">Project</a> · ','<a href="https://github.com/summernote/summernote/issues" target="_blank">Issues</a>',"</p>",].join("");this.$dialog=this.ui.dialog({title:this.lang.options.help,fade:this.options.dialogsFade,body:this.createShortcutList(),footer:co,callback:function(cq){cq.find(".modal-body,.note-modal-body").css({"max-height":300,"overflow":"scroll"})}}).render().appendTo(cp)};e.prototype.destroy=function(){this.ui.hideDialog(this.$dialog);this.$dialog.remove()};e.prototype.createShortcutList=function(){var cp=this;var co=this.options.keyMap[aj.isMac?"mac":"pc"];return Object.keys(co).map(function(cr){var cs=co[cr];var cq=a3('<div><div class="help-list-item"/></div>');cq.append(a3("<label><kbd>"+cr+"</kdb></label>").css({"width":180,"margin-right":10})).append(a3("<span/>").html(cp.context.memo("help."+cs)||cs));return cq.html()}).join("")};e.prototype.showHelpDialog=function(){var co=this;return a3.Deferred(function(cp){co.ui.onDialogShown(co.$dialog,function(){co.context.triggerEvent("dialog.shown");cp.resolve()});co.ui.showDialog(co.$dialog)}).promise()};e.prototype.show=function(){var co=this;this.context.invoke("editor.saveRange");this.showHelpDialog().then(function(){co.context.invoke("editor.restoreRange")})};return e}());var ad=20;var bk=(function(){function e(co){var cp=this;this.context=co;this.ui=a3.summernote.ui;this.options=co.options;this.events={"summernote.keyup summernote.mouseup summernote.scroll":function(){cp.update()},"summernote.disable summernote.change summernote.dialog.shown":function(){cp.hide()},"summernote.focusout":function(cq,cr){if(aj.isFF){return}if(!cr.relatedTarget||!bO.ancestor(cr.relatedTarget,am.eq(cp.$popover[0]))){cp.hide()}}}}e.prototype.shouldInitialize=function(){return this.options.airMode&&!n.isEmpty(this.options.popover.air)};e.prototype.initialize=function(){this.$popover=this.ui.popover({className:"note-air-popover"}).render().appendTo(this.options.container);var co=this.$popover.find(".popover-content");this.context.invoke("buttons.build",co,this.options.popover.air)};e.prototype.destroy=function(){this.$popover.remove()};e.prototype.update=function(){var cq=this.context.invoke("editor.currentStyle");if(cq.range&&!cq.range.isCollapsed()){var cp=n.last(cq.range.getClientRects());if(cp){var co=am.rect2bnd(cp);this.$popover.css({display:"block",left:Math.max(co.left+co.width/2,0)-ad,top:co.top+co.height});this.context.invoke("buttons.updateCurrentStyle",this.$popover)}}else{this.hide()}};e.prototype.hide=function(){this.$popover.hide()};return e}());var bR=5;var av=(function(){function e(co){var cp=this;this.context=co;this.ui=a3.summernote.ui;this.$editable=co.layoutInfo.editable;this.options=co.options;this.hint=this.options.hint||[];this.direction=this.options.hintDirection||"bottom";this.hints=a3.isArray(this.hint)?this.hint:[this.hint];this.events={"summernote.keyup":function(cq,cr){if(!cr.isDefaultPrevented()){cp.handleKeyup(cr)}},"summernote.keydown":function(cq,cr){cp.handleKeydown(cr)},"summernote.disable summernote.dialog.shown":function(){cp.hide()}}}e.prototype.shouldInitialize=function(){return this.hints.length>0};e.prototype.initialize=function(){var co=this;this.lastWordRange=null;this.$popover=this.ui.popover({className:"note-hint-popover",hideArrow:true,direction:""}).render().appendTo(this.options.container);this.$popover.hide();this.$content=this.$popover.find(".popover-content,.note-popover-content");this.$content.on("click",".note-hint-item",function(cp){co.$content.find(".active").removeClass("active");a3(cp.currentTarget).addClass("active");co.replace()})};e.prototype.destroy=function(){this.$popover.remove()};e.prototype.selectItem=function(co){this.$content.find(".active").removeClass("active");co.addClass("active");this.$content[0].scrollTop=co[0].offsetTop-(this.$content.innerHeight()/2)};e.prototype.moveDown=function(){var cq=this.$content.find(".note-hint-item.active");var co=cq.next();if(co.length){this.selectItem(co)}else{var cp=cq.parent().next();if(!cp.length){cp=this.$content.find(".note-hint-group").first()}this.selectItem(cp.find(".note-hint-item").first())}};e.prototype.moveUp=function(){var cp=this.$content.find(".note-hint-item.active");var co=cp.prev();if(co.length){this.selectItem(co)}else{var cq=cp.parent().prev();if(!cq.length){cq=this.$content.find(".note-hint-group").last()}this.selectItem(cq.find(".note-hint-item").last())}};e.prototype.replace=function(){var co=this.$content.find(".note-hint-item.active");if(co.length){var cp=this.nodeFromItem(co);this.lastWordRange.insertNode(cp);T.createFromNode(cp).collapse().select();this.lastWordRange=null;this.hide();this.context.triggerEvent("change",this.$editable.html(),this.$editable[0]);this.context.invoke("editor.focus")}};e.prototype.nodeFromItem=function(co){var cr=this.hints[co.data("index")];var cq=co.data("item");var cp=cr.content?cr.content(cq):cq;if(typeof cp==="string"){cp=bO.createText(cp)}return cp};e.prototype.createItemTemplates=function(cp,co){var cq=this.hints[cp];return co.map(function(ct,cr){var cs=a3('<div class="note-hint-item"/>');cs.append(cq.template?cq.template(ct):ct+"");cs.data({"index":cp,"item":ct});return cs})};e.prototype.handleKeydown=function(co){if(!this.$popover.is(":visible")){return}if(co.keyCode===i.code.ENTER){co.preventDefault();this.replace()}else{if(co.keyCode===i.code.UP){co.preventDefault();this.moveUp()}else{if(co.keyCode===i.code.DOWN){co.preventDefault();this.moveDown()}}}};e.prototype.searchKeyword=function(cp,co,cs){var cr=this.hints[cp];if(cr&&cr.match.test(co)&&cr.search){var cq=cr.match.exec(co);cr.search(cq[1],cs)}else{cs()}};e.prototype.createGroup=function(co,cp){var cr=this;var cq=a3('<div class="note-hint-group note-hint-group-'+co+'"/>');this.searchKeyword(co,cp,function(cs){cs=cs||[];if(cs.length){cq.html(cr.createItemTemplates(co,cs));cr.show()}});return cq};e.prototype.handleKeyup=function(cq){var cs=this;if(!n.contains([i.code.ENTER,i.code.UP,i.code.DOWN],cq.keyCode)){var cr=this.context.invoke("editor.getLastRange").getWordRange();var cp=cr.toString();if(this.hints.length&&cp){this.$content.empty();var co=am.rect2bnd(n.last(cr.getClientRects()));if(co){this.$popover.hide();this.lastWordRange=cr;this.hints.forEach(function(cu,ct){if(cu.match.test(cp)){cs.createGroup(ct,cp).appendTo(cs.$content)}});this.$content.find(".note-hint-item:first").addClass("active");if(this.direction==="top"){this.$popover.css({left:co.left,top:co.top-this.$popover.outerHeight()-bR})}else{this.$popover.css({left:co.left,top:co.top+co.height+bR})}}}else{this.hide()}}};e.prototype.show=function(){this.$popover.show()};e.prototype.hide=function(){this.$popover.hide()};return e}());a3.summernote=a3.extend(a3.summernote,{version:"0.8.11",plugins:{},dom:bO,range:T,options:{langInfo:a3.summernote.lang["en-US"],modules:{"editor":l,"clipboard":ae,"dropzone":bh,"codeview":a8,"statusbar":aO,"fullscreen":bj,"handle":t,"hintPopover":av,"autoLink":ah,"autoSync":bv,"autoReplace":D,"placeholder":aF,"buttons":aq,"toolbar":bx,"linkDialog":x,"linkPopover":m,"imageDialog":aD,"imagePopover":bc,"tablePopover":bY,"videoDialog":I,"helpDialog":bo,"airPopover":bk},buttons:{},lang:"en-US",followingToolbar:true,otherStaticBar:"",toolbar:[["style",["style"]],["font",["bold","underline","clear"]],["fontsize",["fontsize"]],["fontname",["fontname"]],["color",["color"]],["para",["ul","ol","paragraph"]],["table",["table"]],["insert",["link","picture","video"]],["view",["fullscreen","codeview","help"]],],popatmouse:true,popover:{image:[["resize",["resizeFull","resizeHalf","resizeQuarter","resizeNone"]],["float",["floatLeft","floatRight","floatNone"]],["remove",["removeMedia"]],],link:[["link",["linkDialogShow","unlink"]],],table:[["add",["addRowDown","addRowUp","addColLeft","addColRight"]],["delete",["deleteRow","deleteCol","deleteTable"]],],air:[["color",["color"]],["font",["bold","underline","clear"]],["para",["ul","paragraph"]],["table",["table"]],["insert",["link","picture"]],]},airMode:false,width:null,height:null,linkTargetBlank:true,focus:false,tabSize:4,styleWithSpan:true,shortcuts:true,textareaAutoSync:true,hintDirection:"bottom",tooltip:"auto",container:"body",maxTextLength:0,blockquoteBreakingLevel:2,spellCheck:true,styleTags:["p","blockquote","pre","h1","h2","h3","h4","h5","h6"],fontNames:["Arial","Arial Black","Comic Sans MS","Courier New","Helvetica Neue","Helvetica","Impact","Lucida Grande","Tahoma","Times New Roman","Verdana",],fontNamesIgnoreCheck:[],fontSizes:["8","9","10","11","12","14","18","24","36"],colors:[["#000000","#424242","#636363","#9C9C94","#CEC6CE","#EFEFEF","#F7F7F7","#FFFFFF"],["#FF0000","#FF9C00","#FFFF00","#00FF00","#00FFFF","#0000FF","#9C00FF","#FF00FF"],["#F7C6CE","#FFE7CE","#FFEFC6","#D6EFD6","#CEDEE7","#CEE7F7","#D6D6E7","#E7D6DE"],["#E79C9C","#FFC69C","#FFE79C","#B5D6A5","#A5C6CE","#9CC6EF","#B5A5D6","#D6A5BD"],["#E76363","#F7AD6B","#FFD663","#94BD7B","#73A5AD","#6BADDE","#8C7BC6","#C67BA5"],["#CE0000","#E79439","#EFC631","#6BA54A","#4A7B8C","#3984C6","#634AA5","#A54A7B"],["#9C0000","#B56308","#BD9400","#397B21","#104A5A","#085294","#311873","#731842"],["#630000","#7B3900","#846300","#295218","#083139","#003163","#21104A","#4A1031"],],colorsName:[["Black","Tundora","Dove Gray","Star Dust","Pale Slate","Gallery","Alabaster","White"],["Red","Orange Peel","Yellow","Green","Cyan","Blue","Electric Violet","Magenta"],["Azalea","Karry","Egg White","Zanah","Botticelli","Tropical Blue","Mischka","Twilight"],["Tonys Pink","Peach Orange","Cream Brulee","Sprout","Casper","Perano","Cold Purple","Careys Pink"],["Mandy","Rajah","Dandelion","Olivine","Gulf Stream","Viking","Blue Marguerite","Puce"],["Guardsman Red","Fire Bush","Golden Dream","Chelsea Cucumber","Smalt Blue","Boston Blue","Butterfly Bush","Cadillac"],["Sangria","Mai Tai","Buddha Gold","Forest Green","Eden","Venice Blue","Meteorite","Claret"],["Rosewood","Cinnamon","Olive","Parsley","Tiber","Midnight Blue","Valentino","Loulou"],],colorButton:{foreColor:"#000000",backColor:"#FFFF00"},lineHeights:["1.0","1.2","1.4","1.5","1.6","1.8","2.0","3.0"],tableClassName:"table table-bordered",insertTableMaxSize:{col:10,row:10},dialogsInBody:false,dialogsFade:false,maximumImageFileSize:null,callbacks:{onBeforeCommand:null,onBlur:null,onBlurCodeview:null,onChange:null,onChangeCodeview:null,onDialogShown:null,onEnter:null,onFocus:null,onImageLinkInsert:null,onImageUpload:null,onImageUploadError:null,onInit:null,onKeydown:null,onKeyup:null,onMousedown:null,onMouseup:null,onPaste:null,onScroll:null},codemirror:{mode:"text/html",htmlMode:true,lineNumbers:true},codeviewFilter:false,codeviewFilterRegex:/<\/*(?:applet|b(?:ase|gsound|link)|embed|frame(?:set)?|ilayer|l(?:ayer|ink)|meta|object|s(?:cript|tyle)|t(?:itle|extarea)|xml)[^>]*?>/gi,codeviewIframeFilter:true,codeviewIframeWhitelistSrc:[],codeviewIframeWhitelistSrcBase:["www.youtube(?:-nocookie)?.com","www.facebook.com","vine.co","instagram.com","player.vimeo.com","www.dailymotion.com","player.youku.com","v.qq.com",],keyMap:{pc:{"ENTER":"insertParagraph","CTRL+Z":"undo","CTRL+Y":"redo","TAB":"tab","SHIFT+TAB":"untab","CTRL+B":"bold","CTRL+I":"italic","CTRL+U":"underline","CTRL+SHIFT+S":"strikethrough","CTRL+BACKSLASH":"removeFormat","CTRL+SHIFT+L":"justifyLeft","CTRL+SHIFT+E":"justifyCenter","CTRL+SHIFT+R":"justifyRight","CTRL+SHIFT+J":"justifyFull","CTRL+SHIFT+NUM7":"insertUnorderedList","CTRL+SHIFT+NUM8":"insertOrderedList","CTRL+LEFTBRACKET":"outdent","CTRL+RIGHTBRACKET":"indent","CTRL+NUM0":"formatPara","CTRL+NUM1":"formatH1","CTRL+NUM2":"formatH2","CTRL+NUM3":"formatH3","CTRL+NUM4":"formatH4","CTRL+NUM5":"formatH5","CTRL+NUM6":"formatH6","CTRL+ENTER":"insertHorizontalRule","CTRL+K":"linkDialog.show"},mac:{"ENTER":"insertParagraph","CMD+Z":"undo","CMD+SHIFT+Z":"redo","TAB":"tab","SHIFT+TAB":"untab","CMD+B":"bold","CMD+I":"italic","CMD+U":"underline","CMD+SHIFT+S":"strikethrough","CMD+BACKSLASH":"removeFormat","CMD+SHIFT+L":"justifyLeft","CMD+SHIFT+E":"justifyCenter","CMD+SHIFT+R":"justifyRight","CMD+SHIFT+J":"justifyFull","CMD+SHIFT+NUM7":"insertUnorderedList","CMD+SHIFT+NUM8":"insertOrderedList","CMD+LEFTBRACKET":"outdent","CMD+RIGHTBRACKET":"indent","CMD+NUM0":"formatPara","CMD+NUM1":"formatH1","CMD+NUM2":"formatH2","CMD+NUM3":"formatH3","CMD+NUM4":"formatH4","CMD+NUM5":"formatH5","CMD+NUM6":"formatH6","CMD+ENTER":"insertHorizontalRule","CMD+K":"linkDialog.show"}},icons:{"align":"note-icon-align","alignCenter":"note-icon-align-center","alignJustify":"note-icon-align-justify","alignLeft":"note-icon-align-left","alignRight":"note-icon-align-right","rowBelow":"note-icon-row-below","colBefore":"note-icon-col-before","colAfter":"note-icon-col-after","rowAbove":"note-icon-row-above","rowRemove":"note-icon-row-remove","colRemove":"note-icon-col-remove","indent":"note-icon-align-indent","outdent":"note-icon-align-outdent","arrowsAlt":"note-icon-arrows-alt","bold":"note-icon-bold","caret":"note-icon-caret","circle":"note-icon-circle","close":"note-icon-close","code":"note-icon-code","eraser":"note-icon-eraser","floatLeft":"note-icon-float-left","floatRight":"note-icon-float-right","font":"note-icon-font","frame":"note-icon-frame","italic":"note-icon-italic","link":"note-icon-link","unlink":"note-icon-chain-broken","magic":"note-icon-magic","menuCheck":"note-icon-menu-check","minus":"note-icon-minus","orderedlist":"note-icon-orderedlist","pencil":"note-icon-pencil","picture":"note-icon-picture","question":"note-icon-question","redo":"note-icon-redo","rollback":"note-icon-rollback","square":"note-icon-square","strikethrough":"note-icon-strikethrough","subscript":"note-icon-subscript","superscript":"note-icon-superscript","table":"note-icon-table","textHeight":"note-icon-text-height","trash":"note-icon-trash","underline":"note-icon-underline","undo":"note-icon-undo","unorderedlist":"note-icon-unorderedlist","video":"note-icon-video"}}});a3.summernote=a3.extend(a3.summernote,{ui:S})}));
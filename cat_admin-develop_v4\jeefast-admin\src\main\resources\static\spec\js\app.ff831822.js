(function(e){function t(t){for(var r,o,s=t[0],u=t[1],c=t[2],p=0,f=[];p<s.length;p++)o=s[p],a[o]&&f.push(a[o][0]),a[o]=0;for(r in u)Object.prototype.hasOwnProperty.call(u,r)&&(e[r]=u[r]);l&&l(t);while(f.length)f.shift()();return i.push.apply(i,c||[]),n()}function n(){for(var e,t=0;t<i.length;t++){for(var n=i[t],r=!0,s=1;s<n.length;s++){var u=n[s];0!==a[u]&&(r=!1)}r&&(i.splice(t--,1),e=o(o.s=n[0]))}return e}var r={},a={app:0},i=[];function o(t){if(r[t])return r[t].exports;var n=r[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,o),n.l=!0,n.exports}o.m=e,o.c=r,o.d=function(e,t,n){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},o.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)o.d(n,r,function(t){return e[t]}.bind(null,r));return n},o.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="";var s=window["webpackJsonp"]=window["webpackJsonp"]||[],u=s.push.bind(s);s.push=t,s=s.slice();for(var c=0;c<s.length;c++)t(s[c]);var l=u;i.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("56d7")},"16e8":function(e,t,n){},"56d7":function(e,t,n){"use strict";n.r(t);n("cadf"),n("551c"),n("f751"),n("097d");var r=n("2b0e"),a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{attrs:{id:"app"}},[n("div",{staticClass:"sku_container"},[e._l(e.specification,function(t,r){return n("div",{key:t.id,staticClass:"sku_group mb10"},[n("div",{staticClass:"spec_title"},[n("span",{staticClass:"label"},[e._v("规格名："+e._s(t.value))]),n("span",{staticClass:"remove",on:{click:function(t){return e.delSepc(r)}}},[e._v("×")])]),n("div",{staticClass:"group_container"},[n("span",{staticClass:"label"},[e._v("规格值：")]),e._l(t.leaf,function(t){return n("el-tag",{key:t.id},[e._v(e._s(t.value))])})],2)])}),n("div",{directives:[{name:"show",rawName:"v-show",value:e.openTemp,expression:"openTemp"}],staticClass:"sku_group mb10"},[n("div",{staticClass:"spec_title"},[n("span",{staticClass:"label"},[e._v("规格名：")]),n("el-select",{staticClass:"input",attrs:{placeholder:"请选择规格名",value:e.tempGroup.value},on:{change:e.changeGroup}},e._l(e.groups,function(e){return n("el-option",{key:e.id,attrs:{label:e.value,value:e}})}),1)],1),n("div",{staticClass:"group_container"},[n("span",{staticClass:"label"},[e._v("规格值：")]),e._l(e.tempGroup.leaf,function(t,r){return n("el-checkbox",{attrs:{label:t.value},model:{value:t.pick,callback:function(n){e.$set(t,"pick",n)},expression:"item.pick"}})})],2)]),n("div",{staticClass:"spec_title"},[n("el-button",{directives:[{name:"show",rawName:"v-show",value:!e.openTemp,expression:"!openTemp"}],attrs:{type:"primary",disabled:e.disabled},on:{click:e.addTemp}},[e._v("添加规格项目")]),n("el-button",{directives:[{name:"show",rawName:"v-show",value:e.openTemp,expression:"openTemp"}],attrs:{type:"primary",disabled:e.confirm},on:{click:e.confirmTemp}},[e._v("确认")]),n("el-button",{directives:[{name:"show",rawName:"v-show",value:e.openTemp,expression:"openTemp"}],on:{click:e.cancelTemp}},[e._v("取消")])],1)],2),n("div",{staticClass:"sku_container"},[n("SkuSelect",{attrs:{skusData:e.specificationFilter}})],1),n("div",{staticClass:"sku_container"},[n("SkuTable",{ref:"skuTable",attrs:{skusData:e.specificationFilter},on:{save:e.save}})],1)])},i=[],o=n("75fc"),s=(n("7514"),n("28a5"),n("a8db")),u=(n("386d"),n("96cf"),n("3b8d")),c=n("cebc"),l=(n("ac6a"),n("5df3"),n("f400"),n("4328")),p=n.n(l),f=n("d225"),d=n("b0b4"),v=n("bc3a"),h=n.n(v),m=function(){function e(t){var n=t.base;Object(f["a"])(this,e),this.base=n;var r=new h.a.create({baseURL:n,timeout:3e4});r.defaults.headers.post["Content-Type"]="application/json;charset=UTF-8",r.defaults.withCredentials=!0,r.interceptors.request.use(function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t){var n,r;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return t.data,t.params,t.method,n=t.headers,e.prev=1,r=localStorage.getItem("token"),r&&(n.token=r),e.abrupt("return",t);case 7:return e.prev=7,e.t0=e["catch"](1),e.abrupt("return",Promise.reject(e.t0));case 10:case"end":return e.stop()}},e,null,[[1,7]])}));return function(t){return e.apply(this,arguments)}}(),function(e){return Promise.reject(e)}),r.interceptors.response.use(function(e){var t=e.status,n=e.data,r=e.statusText,a=n.code;return 200===t&&0==a?Promise.resolve(n.data||n):Promise.reject(n.retDesc||r)},function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",Promise.reject(t));case 1:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}()),this.service=r}return Object(d["a"])(e,[{key:"request",value:function(e){var t=e.method,n=void 0===t?"get":t,r=e.url,a=e.params,i=void 0===a?"":a,o=e.data,s=void 0===o?{}:o;return this.service.request({method:n,url:r,params:i,data:s})}},{key:"post",value:function(e){var t=e.url,n=e.params,r=e.data;return this.request({method:"post",url:t,params:n,data:r})}},{key:"get",value:function(e){var t=e.url,n=e.params;return this.request({url:t,params:n})}}]),e}(),b=new m({base:""}),k=b,g=function(e){return k.post({url:"/cat/productSku/getByProductId",data:{productId:e}})},w=function(e){return k.post({url:"/cat/productSku/updExt",data:e})},_=function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t){var n,r,a,i,o;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return n=new FormData,n.append("file",t),e.next=4,h.a.post("".concat(k.base,"/common/upload"),n,{headers:{"Content-Type":"multipart/form-data;boundary="+(new Date).getTime(),token:localStorage.getItem("token")}});case 4:if(r=e.sent,a=r.data,i=r.status,o=r.statusText,200!=i){e.next=12;break}return e.abrupt("return",a);case 12:throw o;case 13:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}(),y=function(){return k.post({url:"/cat/productSku/groupList"})},j=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"container"},e._l(e.skusData,function(t,r){return n("div",{key:t.id+"_id",staticClass:"spec_title"},[n("span",{staticClass:"label"},[e._v(e._s(t.value)+"：")]),e._l(t.leaf,function(t){return n("el-button",{key:t.id,attrs:{type:e.activeSku[r].option.id===t.id?"primary":""},on:{click:function(n){return e.selectSku(r,t)}}},[e._v(e._s(t.value))])})],2)}),0)},x=[],T={data:function(){return{activeSku:[]}},props:{skusData:{type:Array,default:function(){return[]}}},watch:{skusData:{deep:!0,immediate:!0,handler:function(e){this.activeSku=e.map(function(e){return{spec:{value:e.value,id:e.id},option:{}}})}}},methods:{selectSku:function(e,t){if(this.activeSku[e].option&&this.activeSku[e].option.id===t.id)return this.activeSku[e].option={};this.activeSku[e].option=t}}},O=T,S=(n("c727"),n("2877")),P=Object(S["a"])(O,j,x,!1,null,"26bbfa21",null),I=P.exports,C=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"container"},[n("div",{staticClass:"flex"},[n("el-form",{staticClass:"guide_coefficient",attrs:{inline:!0,model:e.coefficient}},[n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("保存")])],1)],1)],1),n("egrid",e._g(e._b({attrs:{border:"","max-height":"800",data:e.data,columns:e.columns,"columns-props":e.columnsProps}},"egrid",e.$attrs,!1),e.$listeners))],1)},E=[];function $(e){for(var t=[],n=e.length-1;n>=0;n--)e[n+1]&&e[n+1].leaf?t[n]=e[n+1].leaf.length*t[n+1]||1:t[n]=1;return t}function D(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,r=n||{},a=r.optionValue,i=void 0===a?"id":a,o=r.optionText,u=void 0===o?"value":o,l=[],p=0,f={},d=$(e);if(0===e.length)return l;e.forEach(function(e){var t=e.leaf;if(!t||0===t.length)return!0;p=(p||1)*t.length}),t.forEach(function(e){var t=e.skus,n=Object(s["a"])(e,["skus"]);f[t.map(function(e){return"".concat(e.k_id,"_").concat(e.v_id)}).join("|")]=n});for(var v=function(t){var n=[],r=[];e.forEach(function(a,o){var s=a.leaf,c={};if(!s||0===s.length)return!0;if(s.length>1){var l=parseInt(t/d[o],10)%s.length;c=e[o].leaf[l]}else c=e[o].leaf[0];a[i]&&c[i]&&(r.push("".concat(a[i],"_").concat(c[i])),n.push({k_id:a[i],k:a[u],v_id:c[i],v:c[u]}))});var a=f[r.join("|")]||{},o=Object.assign({},a);l.push(Object(c["a"])({},o,{skus:n}))},h=0;h<p;h++)v(h);return l}n("6762"),n("2fdb"),n("4f7f"),n("1c4c"),n("6b54");var G={props:{skusData:{type:Array,default:function(){return[]}}},computed:{columns:function(){this.$createElement;var e=this.skusData.map(function(e){return{label:e.value,formater:function(t){var n=t.skus.find(function(t){return t.k===e.value});return n.v}}});return[].concat(Object(o["a"])(e),[{label:"sku图片",component:r["default"].extend({props:["row"],render:function(){var e=this,t=arguments[0];return t("el-upload",{class:"avatar-uploader",attrs:{action:"1","show-file-list":!1,accept:"image/*","http-request":function(){var t=Object(u["a"])(regeneratorRuntime.mark(function t(n){var r,a,i;return regeneratorRuntime.wrap(function(t){while(1)switch(t.prev=t.next){case 0:return r=n.file,t.next=3,_(r);case 3:a=t.sent,i=a.url,e.row.imageUrl=i;case 6:case"end":return t.stop()}},t)}));function n(e){return t.apply(this,arguments)}return n}()}},[this.row.imageUrl?t("img",{attrs:{src:this.row.imageUrl},class:"avatar"}):t("i",{class:"el-icon-plus avatar-uploader-icon"})])}})},{label:"库存",component:r["default"].extend({props:["row"],render:function(){var e=this,t=arguments[0];return t("ElInputNumber",{attrs:{placeholder:"请输入库存",value:this.row.stock,step:1,min:0,controls:!0,precision:0},on:{input:function(t){return e.row.stock=t}}})}})},{label:"价格",component:r["default"].extend({props:["row"],render:function(){var e=this,t=arguments[0];return t("ElInputNumber",{attrs:{placeholder:"请输入价格",value:this.row.price,step:1,min:0,controls:!1,precision:2},on:{input:function(t){return e.row.price=t}}})}})},{label:"原价",component:r["default"].extend({props:["row"],render:function(){var e=this,t=arguments[0];return t("ElInputNumber",{attrs:{placeholder:"请输入原价",value:this.row.originalPrice,step:1,min:0,controls:!1,precision:2},on:{input:function(t){return e.row.originalPrice=t}}})}})},{label:"会员价",component:r["default"].extend({props:["row"],render:function(){var e=this,t=arguments[0];return t("ElInputNumber",{attrs:{placeholder:"请输入会员价",value:this.row.vipPrice,step:1,min:0,controls:!1,precision:2},on:{input:function(t){return e.row.vipPrice=t}}})}})},{label:"销量",component:r["default"].extend({props:["row"],render:function(){var e=this,t=arguments[0];return t("ElInputNumber",{attrs:{placeholder:"请输入销量",value:this.row.sales,step:1,min:0,controls:!0,precision:0},on:{input:function(t){return e.row.sales=t}}})}})}])}},data:function(){return{data:[],coefficient:{purchase_coefficient:0,guide_coefficient:1.2},columnsProps:{align:"center",minWidth:100}}},methods:{initData:function(e){if(e)this.data=e;else{var t=D(this.skusData).map(function(e){return{skus:e.skus,id:e.skus.map(function(e){var t=e.k_id,n=e.v_id;return"".concat(t,"-").concat(n)}).join("_")}});this.data=t.map(function(e){return Object(c["a"])({},e,{stock:0,price:0,originalPrice:0,vipPrice:0,sales:0,imageUrl:void 0})}),console.log(this.data)}},save:function(){this.$emit("save",this.data)}}},R=G,N=(n("fc81"),n("e488"),Object(S["a"])(R,C,E,!1,null,"2ade0254",null)),U=N.exports,M=new Map,q={components:{SkuSelect:I,SkuTable:U},data:function(){return{productId:"",openTemp:!0,allGroups:[],tempGroup:{},specification:[]}},computed:{confirm:function(){return!this.tempGroup.leaf||!this.tempGroup.leaf.some(function(e){return e.pick})},disabled:function(){return this.specification.some(function(e){return!e.value})},groups:function(){var e=this;return this.allGroups.filter(function(t){return!e.specification.some(function(e){return e.id==t.id})}).map(function(e){return Object(c["a"])({},e,{leaf:e.leaf.map(function(e){return Object(c["a"])({},e,{pick:!1})})})})},specificationFilter:function(){return this.specification.filter(function(e){return e.value&&e.leaf.length})}},created:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return t=p.a.parse(location.search.slice(1)),t.productId||alert("productId不能为空"),this.productId=t.productId,e.next=5,y();case 5:this.allGroups=e.sent,this.allGroups.forEach(function(e){M.set(e.id,e)}),this.productId&&this.restore(),console.log(this.$refs.skuTable);case 9:case"end":return e.stop()}},e,this)}));function t(){return e.apply(this,arguments)}return t}(),methods:{changeGroup:function(e){this.tempGroup=e},addTemp:function(){this.openTemp=!0,this.tempGroup={}},confirmTemp:function(){var e=this,t=Object(c["a"])({},this.tempGroup,{leaf:this.tempGroup.leaf.filter(function(e){return e.pick})});this.specification.push(t),this.cancelTemp(),this.$nextTick(function(){e.$refs.skuTable.initData()})},cancelTemp:function(){this.openTemp=!1},delSepc:function(e){var t=this;this.specification.splice(e,1),this.$nextTick(function(){t.$refs.skuTable.initData()})},save:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(t){var n;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return n=[],t.forEach(function(e){var t={groupKeyId:"",groupValId:"",groupSpace:"",price:e.price,originalPrice:e.originalPrice,vipPrice:e.vipPrice,stock:e.stock,mainimage:e.imageUrl},r=[],a=[],i=[];e.skus.forEach(function(e){r.push(e["k_id"]),a.push(e["v_id"]),i.push("".concat(e.k,":").concat(e.v))}),t.groupKeyId=r.join("_"),t.groupValId=a.join("_"),t.groupSpace=i.join(";"),n.push(t)}),console.log(n),e.next=5,w({productId:this.productId,skuList:n});case 5:alert("保存成功");case 6:case"end":return e.stop()}},e,this)}));function t(t){return e.apply(this,arguments)}return t}(),restore:function(){var e=Object(u["a"])(regeneratorRuntime.mark(function e(){var t,n,r,a=this;return regeneratorRuntime.wrap(function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,g(this.productId);case 2:t=e.sent,console.log(t),n=new Map,r=[],t.forEach(function(e){var t=e.groupKeyId,a=(e.groupSpace,e.groupValId),i=Object(s["a"])(e,["groupKeyId","groupSpace","groupValId"]),o=t.split("_"),u=a.split("_"),l={id:i.id,imageUrl:i.mainimage,price:i.price,originalPrice:i.originalPrice,vipPrice:i.vipPrice,sales:i.totalSales,stock:i.stock,skus:[]};o.forEach(function(e,t){var r=M.get(e),a=u[t],i=r.leaf.find(function(e){return e.id==a}),o=n.get(e);o||(o=Object(c["a"])({},r,{leaf:new Map}),n.set(e,o)),o.leaf.set(a,Object(c["a"])({},i,{pick:!0})),l.skus.push({k:r.value,k_id:r.id,v:i.value,v_id:i.id})}),r.push(l)}),n.forEach(function(e,t){a.specification.push(Object(c["a"])({},e,{leaf:Object(o["a"])(e.leaf.values())}))}),this.$nextTick(function(){a.$refs.skuTable.initData(r)}),this.openTemp=!1;case 10:case"end":return e.stop()}},e,this)}));function t(){return e.apply(this,arguments)}return t}()}},F=q,V=(n("9421"),Object(S["a"])(F,a,i,!1,null,"1b964688",null)),K=V.exports,L=n("f05a"),A=n.n(L),J=n("5c96"),z=n.n(J);n("0fae");r["default"].use(z.a,{size:"small"}),r["default"].use(A.a),r["default"].config.productionTip=!1,new r["default"]({render:function(e){return e(K)}}).$mount("#app")},"7d69":function(e,t,n){},9421:function(e,t,n){"use strict";var r=n("16e8"),a=n.n(r);a.a},a19a:function(e,t,n){},b190:function(e,t,n){},c727:function(e,t,n){"use strict";var r=n("a19a"),a=n.n(r);a.a},e488:function(e,t,n){"use strict";var r=n("7d69"),a=n.n(r);a.a},fc81:function(e,t,n){"use strict";var r=n("b190"),a=n.n(r);a.a}});
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('兑换商品日志列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>兑换人id：</p>
                                <input type="text" name="userId"/>
                            </li>
                            <li>
                                <p>支付方式：</p>
                                <select name="payType" th:with="type=${@dict.getType('cat_pay_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <!--<a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:ExchangeGoodsLogBackstage:add">
                    <i class="fa fa-plus"></i> 添加
                </a>-->
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:ExchangeGoodsLogBackstage:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:ExchangeGoodsLogBackstage:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:ExchangeGoodsLogBackstage:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var defineFlag = [[${@permission.hasPermi('cat:ExchangeGoodsLogBackstage:define')}]];
        var editFlag = [[${@permission.hasPermi('cat:ExchangeGoodsLogBackstage:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:ExchangeGoodsLogBackstage:remove')}]];
        var catPayType = [[${@dict.getType('cat_pay_type')}]];
        var prefix = ctx + "cat/ExchangeGoodsLogBackstage";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                sortName: "createDate",
                sortOrder: "desc",
                modalName: "兑换商品日志",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : 'id',
                    visible: false
                },
                {
                    field : 'goodsId', 
                    title : '兑换商品id',
                    visible: false
                },
                {
                    field : 'goodsName',
                    title : '兑换商品'
                },
                {
                    field : 'userId',
                    title : '兑换人id'
                },
                {
                    field : 'userName',
                    title : '兑换人'
                },
                {
                    field : 'payType', 
                    title : '支付方式',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(catPayType, value);
                    }
                },
                {
                    field : 'price',
                    title : '价格',
                    sortable: true
                },
                {
                    field : 'remark', 
                    title : '备注'
                },
                {
                    field : 'createDate',
                    title : '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs ' + defineFlag + '" href="javascript:void(0)" onclick="defineData(\'' + row.id + '\')"><i class="fa fa-edit"></i>确定订单</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
        /* 角色管理-分配数据权限 */
        function defineData(roleId) {
            var url = prefix + '/define/' + roleId;
            $.modal.open("确定兑换信息", url);
        }
    </script>
</body>
</html>
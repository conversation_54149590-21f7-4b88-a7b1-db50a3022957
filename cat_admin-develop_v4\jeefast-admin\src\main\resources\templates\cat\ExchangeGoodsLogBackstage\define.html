<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('确定兑换商品日志')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="ibox-title">
            <h5>用户收货地址信息</h5>
        </div>
        <form class="form-horizontal m" id="form-ExchangeGoodsLogBackstage-edit">
            <input name="id" th:field="${shopping.id}" type="hidden">
            <table>
                <tr>
                    <td >姓名：</td>
                    <td ><span name="userName" th:text="${shopping.userName}" /></td>
                </tr>
                <tr>
                    <td>手机号：</td>
                    <td><span name="mobile" th:text="${shopping.mobile}" /></td>
                </tr>
                <tr>
                    <td>收货地区：</td>
                    <td>
                        <span name="province" th:text="${shopping.province}" />
                        <span name="city" th:text="${shopping.city}" />
                        <span name="district" th:text="${shopping.district}" />
                        <span name="street" th:text="${shopping.street}" />
                    </td>
                </tr>
                <tr>
                    <td>详细地址：</td>
                    <td>
                        <span name="address" th:text="${shopping.address}" />
                    </td>
                </tr>
                <tr>
                    <td>快递名称：</td>
                    <td><input  name="logistics" th:field="${shopping.logistics}"  type="text" /></td>
                </tr>
                <tr>
                    <td>快递单号：</td>
                    <td><input name="logisticsNum" th:field="${shopping.logisticsNum}" type="text" /></td>
                </tr>
            </table>
        </form>
        <hr>
        <div class="form-horizontal m" th:object="${exchangeGoodsLogBackstage}">
            <div class="form-group">
                <label class="col-sm-3 control-label">价格：</label>
                <div class="col-sm-8">
                    <span name="price" th:text="*{price}" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">兑换人id：</label>
                <div class="col-sm-8">
                    <span name="userId" th:text="*{userId}" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">兑换商品id：</label>
                <div class="col-sm-8">
                    <span name="goodsId" th:text="*{goodsId}"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">兑换商品名称：</label>
                <div class="col-sm-8">
                    <span name="goodsName" th:text="*{goodsName}" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">支付方式：</label>
                <div class="col-sm-8">
                    <select name="payType" class="form-control m-b" th:with="type=${@dict.getType('cat_pay_type')}" disabled>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{payType}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <span name="createDate" th:text="${#dates.format(exchangeGoodsLogBackstage.createDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" disabled="disabled"/>
                    </div>
                </div>
            </div>
            <!--<div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <span name="remark" th:text="*{remark}" />
                </div>
            </div>-->
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/ExchangeGoodsLogBackstage";
        $("#form-ExchangeGoodsLogBackstage-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/defineSave", $('#form-ExchangeGoodsLogBackstage-edit').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单退款列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>订单ID：</p>
                                <input type="text" name="orderId"/>
                            </li>
                            <li>
                                <p>订单编号：</p>
                                <input type="text" name="orderNo"/>
                            </li>
                            <!--<li>
                                <p>子订单项ID：</p>
                                <input type="text" name="orderItemId"/>
                            </li>-->
                            <li>
                                <p>退款编号：</p>
                                <input type="text" name="refundNo"/>
                            </li>
                            <!--<li>
                                <p>支付订单号：</p>
                                <input type="text" name="outTradeNo"/>
                            </li>-->
                            <li>
                                <p>三方退款单：</p>
                                <input type="text" name="outRefundNo"/>
                            </li>
                            <!--<li>
                                <p>支付平台：</p>
                                <select name="payplatForm" th:with="type=${@dict.getType('cat_payplat_form')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>-->
                            <li>
                                <p>买家ID：</p>
                                <input type="text" name="userId"/>
                            </li>
                            <!--<li>
                                <p>申请类型：</p>
                                <select name="applyType" th:with="type=${@dict.getType('cat_order_refund_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>-->
                            <li>
                                <p>处理状态：</p>
                                <select name="refundSts" th:with="type=${@dict.getType('cat_order_return_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>退货物流号：</p>
                                <input type="text" name="logisticsNum"/>
                            </li>
                            <!--<li>
                                <p>处理退款状态：</p>
                                <select name="returnMoneySts" th:with="type=${@dict.getType('cat_order_return_money_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>-->
                            <!--<li class="select-time">
                                <p>退款时间：</p>
                                <input type="text" class="time-input" id="beginRefundDate" placeholder="开始时间" name="params[beginRefundDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endRefundDate" placeholder="结束时间" name="params[endRefundDate]"/>
                            </li>-->
                            <!--<li>
                                <p>店铺ID：</p>
                                <input type="text" name="shopId"/>
                            </li>-->
                            <!--<li class="select-time">
                                <p>更新时间：</p>
                                <input type="text" class="time-input" id="beginUpdateDate" placeholder="开始时间" name="params[beginUpdateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endUpdateDate" placeholder="结束时间" name="params[endUpdateDate]"/>
                            </li>-->
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="beginCreateDate" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endCreateDate" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <!--<a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:OrderRefund:add">
                    <i class="fa fa-plus"></i> 添加
                </a>-->
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:OrderRefund:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <!--<a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:OrderRefund:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:OrderRefund:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:OrderRefund:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:OrderRefund:remove')}]];
        var applyTypeDatas = [[${@dict.getType('cat_order_refund_type')}]];
        var refundStsDatas = [[${@dict.getType('cat_order_return_status')}]];
        var returnMoneyStsDatas = [[${@dict.getType('cat_order_return_money_status')}]];
        var payplatFormDatas = [[${@dict.getType('cat_payplat_form')}]];
        var prefix = ctx + "cat/OrderRefund";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "订单退款",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : '记录ID',
                    visible: false
                },
                {
                    field : 'orderId', 
                    title : '订单ID',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'orderNo', 
                    title : '订单编号',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'orderAmount', 
                    title : '订单总金额'
                },
                {
                    field : 'orderItemId', 
                    title : '子订单项ID',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'refundNo', 
                    title : '退款编号',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'outTradeNo', 
                    title : '支付订单号',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'outRefundNo', 
                    title : '第三方退款单号',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'payplatForm', 
                    title : '支付平台',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(payplatFormDatas, value);
                    }
                },
                {
                    field : 'userId', 
                    title : '买家ID',
                    visible: false
                },
                {
                    field : 'goodsNum', 
                    title : '退货数量'
                },
                {
                    field : 'refundAmount', 
                    title : '退款金额'
                },
                {
                    field : 'applyType', 
                    title : '申请类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(applyTypeDatas, value);
                    }
                },
                {
                    field : 'refundSts', 
                    title : '处理状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(refundStsDatas, value);
                    }
                },
                {
                    field : 'returnMoneySts', 
                    title : '处理退款状态',
                    visible: false,
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(returnMoneyStsDatas, value);
                    }
                },
                {
                    field : 'handelDate', 
                    title : '卖家处理时间',
                    visible: false
                },
                /*{
                    field : 'refundDate', 
                    title : '退款时间'
                },
                {
                    field : 'photoFiles', 
                    title : '文件凭证json'
                },
                {
                    field : 'buyerMsg', 
                    title : '申请原因'
                },
                {
                    field : 'sellerMsg', 
                    title : '卖家备注'
                },*/
                /*{
                    field : 'logistics', 
                    title : '物流名称'
                },
                {
                    field : 'logisticsNum', 
                    title : '物流号码'
                },
                {
                    field : 'shipDate', 
                    title : '发货时间'
                },*/
                /*{
                    field : 'receiveDate', 
                    title : '收货时间',
                },
                {
                    field : 'receiveMessage', 
                    title : '收货备注'
                },*/
                {
                    field : 'shopId', 
                    title : '店铺ID',
                    visible: false
                },
                {
                    field : 'updateDate', 
                    title : '更新时间',
                    visible: false
                },
                {
                    field : 'createDate', 
                    title : '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        //处理状态:0待审核 1同意 2不同意 3已补充 4确认退款
                        //申请类型:1,仅退款,2退款退货
                        if ((row.refundSts==3 || row.applyType==1)&&row.refundSts!=4){
                            actions.push('<a class="btn btn-warning btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="on_returnMoney(\'' + row.id + '\')"><i class="fa fa-edit"></i>退款</a> ');
                        }
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });


        //退款
        function on_returnMoney(id) {
            $.modal.open("退款/退货","/cat/OrderRefund/returnMoneyViwe?id="+id,1200);
        }
    </script>
</body>
</html>
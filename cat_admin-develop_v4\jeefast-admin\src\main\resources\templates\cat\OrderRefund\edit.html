<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改订单退款')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: bootstrap-fileinput-css" />
</head>
<body class="white-bg">

    <div class="tabs-container">
        <ul class="nav nav-tabs">
            <li class="active"><a data-toggle="tab" href="#tab-1" aria-expanded="true">申请信息</a>
            </li>
            <li class=""><a data-toggle="tab" href="#tab-2" aria-expanded="false">申请附件</a>
            </li>
        </ul>
        <div class="tab-content">
            <div id="tab-1" class="tab-pane active">
                <div class="wrapper wrapper-content animated fadeInRight ibox-content">
                    <form class="form-horizontal m" id="form-OrderRefund-edit" th:object="${orderRefund}">
                        <input name="id" th:field="*{id}" type="hidden">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">订单ID：</label>
                            <div class="col-sm-8">
                                <input name="orderId" th:field="*{orderId}" disabled class="form-control" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">订单编号：</label>
                            <div class="col-sm-8">
                                <input name="orderNo" th:field="*{orderNo}" disabled class="form-control" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">订单总金额：</label>
                            <div class="col-sm-8">
                                <input name="orderAmount" th:field="*{orderAmount}" disabled class="form-control" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">子订单项ID：</label>
                            <div class="col-sm-8">
                                <input name="orderItemId" th:field="*{orderItemId}" disabled class="form-control" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">退款编号：</label>
                            <div class="col-sm-8">
                                <input name="refundNo" th:field="*{refundNo}" disabled class="form-control" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">支付订单号：</label>
                            <div class="col-sm-8">
                                <input name="outTradeNo" th:field="*{outTradeNo}" disabled class="form-control" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">第三方退款单号：</label>
                            <div class="col-sm-8">
                                <input name="outRefundNo" th:field="*{outRefundNo}" class="form-control" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">支付平台：</label>
                            <div class="col-sm-8">
                                <select disabled name="payplatForm" class="form-control m-b"  th:with="type=${@dict.getType('cat_payplat_form')}">
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">买家ID：</label>
                            <div class="col-sm-8">
                                <input disabled name="userId" th:field="*{userId}" class="form-control" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">退货数量：</label>
                            <div class="col-sm-8">
                                <input name="goodsNum" th:field="*{goodsNum}" class="form-control" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">退款金额：</label>
                            <div class="col-sm-8">
                                <input name="refundAmount" th:field="*{refundAmount}" class="form-control" type="text" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">申请类型：</label>
                            <div class="col-sm-8">
                                <div disabled class="radio-box" th:each="dict : ${@dict.getType('cat_order_refund_type')}">
                                    <input type="radio" th:id="${'applyType_' + dict.dictCode}" name="applyType" th:value="${dict.dictValue}" th:field="*{applyType}" required>
                                    <label th:for="${'applyType_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">处理状态：</label>
                            <div class="col-sm-8">
                                <div class="radio-box" th:each="dict : ${@dict.getType('cat_order_return_status')}">
                                    <input type="radio" th:id="${'refundSts_' + dict.dictCode}" name="refundSts" th:value="${dict.dictValue}" th:field="*{refundSts}" required>
                                    <label th:for="${'refundSts_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">处理退款状态：</label>
                            <div class="col-sm-8">
                                <div class="radio-box" th:each="dict : ${@dict.getType('cat_order_return_money_status')}">
                                    <input disabled type="radio" th:id="${'returnMoneySts_' + dict.dictCode}" name="returnMoneySts" th:value="${dict.dictValue}" th:field="*{returnMoneySts}" required>
                                    <label th:for="${'returnMoneySts_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">卖家处理时间：</label>
                            <div class="col-sm-8">
                                <div class="input-group date">
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                    <input disabled name="handelDate" th:value="${#dates.format(orderRefund.handelDate, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">退款时间：</label>
                            <div class="col-sm-8">
                                <div class="input-group date">
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                    <input disabled name="refundDate" th:value="${#dates.format(orderRefund.refundDate, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                                </div>
                            </div>
                        </div>
                        <!--<div class="form-group">
                            <label class="col-sm-3 control-label">文件凭证json：</label>
                            <div class="col-sm-8">
                                <textarea name="photoFiles" class="form-control">[[*{photoFiles}]]</textarea>
                            </div>
                        </div>-->
                        <div class="form-group">
                            <label class="col-sm-3 control-label">申请原因：</label>
                            <div class="col-sm-8">
                                <textarea disabled name="buyerMsg" class="form-control">[[*{buyerMsg}]]</textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">卖家备注：</label>
                            <div class="col-sm-8">
                                <textarea name="sellerMsg" class="form-control">[[*{sellerMsg}]]</textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">物流名称：</label>
                            <div class="col-sm-8">
                                <input disabled name="logistics" th:field="*{logistics}" class="form-control" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">物流号码：</label>
                            <div class="col-sm-8">
                                <input disabled name="logisticsNum" th:field="*{logisticsNum}" class="form-control" type="text">
                            </div>
                        </div>
                        <!--<div class="form-group">
                            <label class="col-sm-3 control-label">发货时间：</label>
                            <div class="col-sm-8">
                                <div class="input-group date">
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                    <input name="shipDate" th:value="${#dates.format(orderRefund.shipDate, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                                </div>
                            </div>
                        </div>-->
                        <!--<div class="form-group">
                            <label class="col-sm-3 control-label">收货时间：</label>
                            <div class="col-sm-8">
                                <div class="input-group date">
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                    <input name="receiveDate" th:value="${#dates.format(orderRefund.receiveDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">收货备注：</label>
                            <div class="col-sm-8">
                                <input name="receiveMessage" th:field="*{receiveMessage}" class="form-control" type="text">
                            </div>
                        </div>-->
                        <div class="form-group">
                            <label class="col-sm-3 control-label">店铺ID：</label>
                            <div class="col-sm-8">
                                <input disabled name="shopId" th:field="*{shopId}" class="form-control" type="text" required>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div id="tab-2" class="tab-pane">
                <div class="form-group">
                    <div class="file-loading">
                        <input id="fileinput-demo-1" type="file" multiple>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: bootstrap-fileinput-js" />
    <script th:src="@{/ruoyi/js/test.js}" id="testScript" th:data="${mediaStrList}"></script>
    <script type="text/javascript">
        //申请媒体url列表
        var mediaList = document.getElementById('testScript').getAttribute('data');
        mediaList = JSON.parse(mediaList);
        var prefix = ctx + "cat/OrderRefund";
        $("#form-OrderRefund-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-OrderRefund-edit').serialize());
            }
        }

        $("input[name='handelDate']").datetimepicker({
            /*format: "yyyy-mm-dd",
            minView: "month",*/
            autoclose: true
        });

        $("input[name='refundDate']").datetimepicker({
            /*format: "yyyy-mm-dd",
            minView: "month",*/
            autoclose: true
        });

        $("input[name='shipDate']").datetimepicker({
            /*format: "yyyy-mm-dd",
            minView: "month",*/
            autoclose: true
        });

        /*$("input[name='receiveDate']").datetimepicker({
            /!*format: "yyyy-mm-dd",
            minView: "month",*!/
            autoclose: true
        });*/


        /*文件上传*/
        $(document).ready(function () {
            $("#fileinput-demo-1").fileinput({
                'theme': 'explorer-fas',
                dropZoneEnabled:false,
                showUpload:false, //是否显示上传按钮
                uploadAsync:false,//false 同步上传，后台用数组接收，true 异步上传，每次上传一个file,会调用多次接口
                dropZoneEnabled: false,//是否显示拖拽区域
                showRemove : false, //显示移除按钮
                overwriteInitial: false,
                initialPreviewAsData: true,
                showRemove:false,//是否显示移除按钮
                showCancel:false,//是否显示文件上传取消按钮。默认为true。只有在AJAX上传过程中，才会启用和显示
                initialPreview: mediaList,
            });

        });

    </script>
</body>
</html>
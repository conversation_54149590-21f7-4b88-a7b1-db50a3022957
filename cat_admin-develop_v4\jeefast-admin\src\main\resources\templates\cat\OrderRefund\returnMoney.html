<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('退款确定')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse" >
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li style="display: none">
                            <p>订单id：</p>
                            <input type="text" name="orderId" th:value="${orderId}"/>
                        </li>
                    </ul>
                </div>
            </form>
            <div th:object="${shopping}" th:if="${shopping != null}">
                <h3>买家信息</h3>
                <table border="0">
                    <tr style="height: 30px">
                        <td style="text-align:right;">姓名：</td>
                        <td style="min-width:80px"><span th:text="*{userName}"/></td>
                        <td>联系电话：</td>
                        <td><span th:text="*{mobile}"/></td>
                    </tr>
                    <tr style="height: 30px">
                        <td>所在地区：</td>
                        <td colspan="3"><span th:text="*{province + city + district + street}"/></td>
                    </tr>
                    <tr style="height: 30px" >
                        <td>详细地址：</td>
                        <td colspan="3"><span th:text="*{address}"/></td>
                    </tr>
                </table>
            </div>
            <div th:object="${orderInfo}">
                <h3>订单信息</h3>
                <table border="0">
                    <tr style="height: 30px">
                        <td>订单编号：<span th:text="*{orderNo}"/></td>
                        <td>支付编号：<span th:text="*{outTradeNo}"/></td>
                        <td>创建时间：<span th:text="*{#dates.format(createDate, 'yyyy-MM-dd HH:mm:ss')}"/></td>
                    </tr>
                    <tr style="height: 30px">
                        <td>付款时间：<span th:text="*{#dates.format(paymentDate, 'yyyy-MM-dd HH:mm:ss')}"/></td>
                        <td>完成时间：<span th:text="*{#dates.format(endDate, 'yyyy-MM-dd HH:mm:ss')}"/></td>
                    </tr>
                </table>
            </div>
            <div th:object="${orderInfo}">
                <h3>买家退货物流信息</h3>
                <table border="0">
                    <tr style="height: 30px">
                        <td>物流名称：<span th:text="*{logistics}"/></td>
                        <td>物流号码：<span th:text="*{logisticsNum}"/></td>
                    </tr>
                </table>
            </div>
            <form class="form-horizontal m" id="refundSub" th:object="${orderRefund}">
                <input style="display: none" type="text" name="id" th:value="*{id}"/>
                <h3>退款/退货信息</h3>
                <table border="0">
                    <tr>
                        <td>退款金额：</td>
                        <td>
                            <input type="text" name="refundAmount" class="form-control" placeholder="请输退款金额" th:field="*{refundAmount}" required/>
                        </td>
                        <td>收货时间：</td>
                        <td>
                            <input  placeholder="请输入收货时间，不对外显示" name="receiveDate" th:value="${#dates.format(receiveDate, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" type="text">
                        </td>
                    </tr>
                    <tr>
                        <td>收货备注：</td>
                        <td colspan="3">
                            <textarea name="buyerMsg" class="form-control" style="width: 600px" placeholder="请输收货备注，该备注不对外显示">[[*{receiveMessage}]]</textarea>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: datetimepicker-js" />
<style>
    td{border:16px solid white;}
</style>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('cat:orderItem:edit')}]];
    var removeFlag = [[${@permission.hasPermi('cat:orderItem:remove')}]];

    $("input[name='receiveDate']").datetimepicker({
        /*format: "yyyy-mm-dd",
        minView: "month",*/
        autoclose: true
    });

    function submitHandler() {
        if ($.validate.form("refundSub")) {
            $.operate.save("/cat/OrderRefund/returnMoney", $('#refundSub').serialize());
        }
    }

</script>
</body>
</html>
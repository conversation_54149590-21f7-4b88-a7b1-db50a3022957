<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('支付信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>订单编号：</p>
                                <input type="text" name="orderNo"/>
                            </li>
                            <li>
                                <p>用户id：</p>
                                <input type="text" name="userId"/>
                            </li>
                            <li>
                                <p>支付平台：</p>
                                <select name="payplatForm" th:with="type=${@dict.getType('cat_payplat_form')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>支付流水号：</p>
                                <input type="text" name="platforMnumber"/>
                            </li>
                            <li>
                                <p>支付金额：</p>
                                <input type="text" name="money"/>
                            </li>
                            <li>
                                <p>支付状态：</p>
                                <select name="platformStatus" th:with="type=${@dict.getType('cat_platform_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li class="select-time">
                                <p>更新时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginUpdateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endUpdateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <!--<a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:PayInfoBackstage:add">
                    <i class="fa fa-plus"></i> 添加
                </a>-->
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:PayInfoBackstage:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:PayInfoBackstage:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:PayInfoBackstage:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:PayInfoBackstage:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:PayInfoBackstage:remove')}]];
        var payplatFormDatas = [[${@dict.getType('cat_payplat_form')}]];
        var platformStatusDatas = [[${@dict.getType('cat_platform_status')}]];
        var prefix = ctx + "cat/PayInfoBackstage";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "支付信息",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : 'id',
                    visible: false
                },
                {
                    field : 'orderNo', 
                    title : '订单编号'
                },
                {
                    field : 'userId', 
                    title : '用户id'
                },
                {
                    field : 'payplatForm', 
                    title : '支付平台',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(payplatFormDatas, value);
                    }
                },
                {
                    field : 'platforMnumber', 
                    title : '支付流水号'
                },
                {
                    field : 'money', 
                    title : '支付金额'
                },
                {
                    field : 'platformStatus', 
                    title : '支付状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(platformStatusDatas, value);
                    }
                },
                {
                    field : 'sucRemark',
                    title : '备注',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'createDate', 
                    title : '创建时间'
                },
                {
                    field : 'updateDate', 
                    title : '更新时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
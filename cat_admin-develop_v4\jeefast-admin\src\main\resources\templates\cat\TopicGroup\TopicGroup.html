<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('话题分组列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>名称：</p>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <p>是否显示：</p>
                                <select name="isShow" th:with="type=${@dict.getType('cat_recommend')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>是否推荐：</p>
                                <select name="isRecommend" th:with="type=${@dict.getType('cat_recommend')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:TopicGroup:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:TopicGroup:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:TopicGroup:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:TopicGroup:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:TopicGroup:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:TopicGroup:remove')}]];
        var isShowDatas = [[${@dict.getType('cat_recommend')}]];
        var isRecommendDatas = [[${@dict.getType('cat_recommend')}]];
        var prefix = ctx + "cat/TopicGroup";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                sortName: "sortNo",
                sortOrder: "asc",
                modalName: "话题分组",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : 'id',
                    visible: false
                },
                {
                    field : 'name', 
                    title : '名称'
                },
                {
                    field : 'isShow', 
                    title : '是否显示',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(isShowDatas, value);
                    }
                },
                {
                    field : 'topicCount', 
                    title : '话题数',
                    sortable: true
                },
                {
                    field : 'sortNo', 
                    title : '排序值',
                    sortable: true
                },
                {
                    field : 'isRecommend', 
                    title : '是否推荐',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(isRecommendDatas, value);
                    }
                },
                {
                    field : 'isDelete', 
                    title : '是否删除',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(isRecommendDatas, value);
                    }
                },
                {
                    field : 'remark',
                    title : '备注'
                },
                {
                    field : 'createDate', 
                    title : '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-primary btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="byTpicList(\'' + row.id + '\')"><i class="fa fa-edit"></i>话题列表</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        //设置话题
        function byTpicList(id){
            debugger;
            $.modal.open("话题列表","/cat/topicInfoBackstage?groupId="+id);
        }
    </script>
</body>
</html>
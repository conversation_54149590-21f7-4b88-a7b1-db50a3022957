<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增话题分组')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-TopicGroup-add">
            <div class="form-group">
                <label class="col-sm-3 control-label">封面：</label>
                <div class="col-sm-8">
                    <div class="fileinput fileinput-new" data-provides="fileinput">
                        <div class="fileinput-new thumbnail" style="width: 130px; height: 130px;">
                            <img />
                        </div>
                        <div class="fileinput-preview fileinput-exists thumbnail" style="max-width: 130px; max-height: 130px;"></div>
                        <div>
                            <span class="btn btn-white btn-file"><span class="fileinput-new">选择图片</span><span class="fileinput-exists">更改</span>
                                <input id="file" name="file" class="form-control" type="file" required>
                            </span>
                            <a href="#" class="btn btn-white fileinput-exists" data-dismiss="fileinput">清除</a>
                        </div>
                    </div>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 尺寸：480 x 480</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">名称：</label>
                <div class="col-sm-8">
                    <input name="name" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否显示：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                        <input type="radio" th:id="${'isShow_' + dict.dictCode}" name="isShow" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${'isShow_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">排序值：</label>
                <div class="col-sm-8">
                    <input name="sortNo" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否推荐：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                        <input type="radio" th:id="${'isRecommend_' + dict.dictCode}" name="isRecommend" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${'isRecommend_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input name="remark" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否删除：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                        <input type="radio" th:id="${'isDelete_' + dict.dictCode}" name="isDelete" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${'isDelete_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: jasny-bootstrap-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/TopicGroup"
        $("#form-TopicGroup-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            /*if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-TopicGroup-add').serialize());
            }*/
            if ($.validate.form()) {
                var form = $('#form-TopicGroup-add')[0];
                var formdata = new FormData(form);
                $.ajax({
                    url: prefix + "/add",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    success: function(result) {
                        $.operate.successCallback(result);
                    }
                })
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('用户提现记录列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>用户ID：</p>
                                <input type="text" name="userId"/>
                            </li>
                            <li>
                                <p>提款平台：</p>
                                <select name="extractForm" th:with="type=${@dict.getType('cat_extract_form')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>提现状态：</p>
                                <select name="status" th:with="type=${@dict.getType('cat_extract_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <!--<a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:UserExtractLogBackstage:add">
                    <i class="fa fa-plus"></i> 添加
                </a>-->
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:UserExtractLogBackstage:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:UserExtractLogBackstage:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:UserExtractLogBackstage:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:UserExtractLogBackstage:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:UserExtractLogBackstage:remove')}]];
        var extractFormDatas = [[${@dict.getType('cat_extract_form')}]];
        var statusDatas = [[${@dict.getType('cat_extract_status')}]];
        var prefix = ctx + "cat/UserExtractLogBackstage";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                sortName: "createDate",
                sortOrder: "desc",
                modalName: "用户提现记录",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : 'id',
                    visible: false
                },
                {
                    field : 'userId', 
                    title : '用户id',
                    visible: false
                },
                {
                    field : 'userName',
                    title : '用户名称'
                },
                {
                    field : 'title', 
                    title : '标题'
                },
                {
                    field : 'extractForm', 
                    title : '提款平台',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(extractFormDatas, value);
                    }
                },
                {
                    field : 'cardNum', 
                    title : '卡账号'
                },
                {
                    field : 'cardName', 
                    title : '卡名称'
                },
                {
                    field : 'cardUserName', 
                    title : '卡持人'
                },
                {
                    field : 'money', 
                    title : '金额'
                },
                {
                    field : 'status', 
                    title : '提现状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field : 'createDate', 
                    title : '创建时间',
                    sortable: true
                },
                {
                    field : 'remark', 
                    title : '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push(`<a class="btn btn-primary btn-xs '${editFlag}'" href="javascript:void(0)" onclick="auth('${row.id}','${row.status}')"><i class="fa fa-edit"></i>审核</a> `);
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });


        function auth(id,status){
            if(status!=1){
                $.modal.alertWarning("该记录不在审核范围！");
                return;
            }
            $.modal.open("审核资料",prefix + "/auth/"+id);
        }

    </script>
</body>
</html>
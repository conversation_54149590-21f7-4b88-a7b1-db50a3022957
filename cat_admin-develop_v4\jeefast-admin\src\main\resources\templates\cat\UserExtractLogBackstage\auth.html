<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('提现审核')" />
    <meta name="referrer" content="no-referrer">
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-UserExtractLogBackstage-auth" th:object="${userExtractLogBackstage}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label">用户id：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="*{userId}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">提款平台：</label>
                <div class="col-sm-8">
                    <select name="extractForm" class="form-control m-b" th:with="type=${@dict.getType('cat_extract_form')}" readonly="readonly">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{extractForm}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">卡账号：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="*{cardNum}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">卡名称：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="*{cardName}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">卡持人：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="*{cardUserName}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">金额：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="*{money}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">审批状态：</label>
                <div class="col-sm-8">
                    <!--
                        <div class="radio-box" th:each="dict : ${@dict.getType('cat_extract_status')}">
                            <input type="radio" th:id="${'isMute_' + dict.dictCode}" name="isMute" th:value="${dict.dictValue}" th:field="*{isMute}" required>
                            <label th:for="${'isMute_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                        </div>
                    -->
                    <div class="radio-box">
                        <input type="radio" th:id="status_3" name="status" value="3" checked="checked" required>
                        <label for="status_3">通过</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" th:id="status_5" name="status" value="5" required>
                        <label for="status_5">驳回</label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">驳回原因：</label>
                <div class="col-sm-8">
                    <input name="reason"  class="form-control" type="text">
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i>用户可见原因</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input name="remark"  class="form-control" type="text">
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i>仅管理端可见</span>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "cat/UserExtractLogBackstage";
        $("#form-UserExtractLogBackstage-auth").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/auth", $('#form-UserExtractLogBackstage-auth').serialize());
            }
        }

    </script>
</body>
</html>
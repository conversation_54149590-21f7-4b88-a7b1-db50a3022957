<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('广告信息列表')" />
    <th:block th:include="include :: cropbox-css" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>广告位置：</p>
<!--                                <p>1文章 2动态 3首页 4开屏：</p>-->
                                <select name="type" th:with="type=${@dict.getType('cat_advert_position')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
<!--                            <li>-->
<!--                                <p>广告图片id：</p>-->
<!--                                <input type="text" name="fileId"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <p>广告图片：</p>-->
<!--                                <input type="text" name="fileUrl"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <p>内部链接：</p>-->
<!--                                <input type="text" name="linkInside"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <p>外部链接：</p>-->
<!--                                <input type="text" name="linkOutside"/>-->
<!--                            </li>-->
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="advert:advert:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="advert:advert:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="advert:advert:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
     <th:block th:include="include :: cropbox-js" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:advert:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:advert:remove')}]];
        var advertPositionFlag = [[${@dict.getType('cat_advert_position')}]];
        var hrefTypeDatas = [[${@dict.getType('cat_href_type')}]];
        var prefix = ctx + "cat/advert";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "广告信息",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : '主键',
                    visible: false
                },
                {
                    field : 'type', 
                    title : '广告位置',
                    formatter: function(value, row, index) {
                        //1文章 2动态 3首页 4开屏
                        return $.table.selectDictLabel(advertPositionFlag, value);
                    }
                },


                {
                    field : 'href',
                    title : '链接'
                },
                    {
                        field : 'hrefType',
                        title : '链接类型',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabel(hrefTypeDatas, value);
                        }
                    },
                {
                    field : 'createDate', 
                    title : '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改广告信息')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-advert-edit" th:object="${advertInfo}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">
                <!--                <label class="col-sm-3 control-label">1文章 2动态 3首页 4开屏：</label>-->
                <label class="col-sm-3 control-label">广告位置：</label>
                <div class="col-sm-8">
                    <select name="type" th:with="type=${@dict.getType('cat_advert_position')}" class="form-control m-b" required >
                        <option value="">--请选择广告位置--</option>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{type}"></option>
                    </select>
                    <!--                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 代码生成请选择字典属性</span>-->
                </div>
            </div>
            <!--            <div class="form-group">    -->
            <!--                <label class="col-sm-3 control-label">广告图片id：</label>-->
            <!--                <div class="col-sm-8">-->
            <!--                    <input name="fileId" class="form-control" type="text">-->
            <!--                </div>-->
            <!--            </div>-->
            <div class="form-group">
                <label class="col-sm-3 control-label">广告图片：</label>
                <div class="col-sm-8">
<!--                    <a href="javascript:uploadAdvert()">上传图片</a>-->
                    <div>
                        <div class="fileinput fileinput-new" data-provides="fileinput">
                            <div class="fileinput-new thumbnail" style="width: 336px; height: 140px;">
                                <img th:src="*{fileUrl}">
                            </div>
                            <div class="fileinput-preview fileinput-exists thumbnail" style="max-width: 200px; max-height: 150px;"></div>
                            <div>
                            <span class="btn btn-white btn-file"><span class="fileinput-new">选择图片</span><span class="fileinput-exists">更改</span>
                                <input id="bannerFile" name="file" class="form-control" type="file">
                            </span>
                                <a href="#" class="btn btn-white fileinput-exists" data-dismiss="fileinput">清除</a>
                            </div>
                        </div>
                        <!--<img id="advertlogo" class="game-logo" th:src="*{fileUrl}">
                        <input name="fileUrl" class="form-control" type="hidden">-->
                    </div>
                </div>
            </div>
            <div class="form-group">
                <!--                <label class="col-sm-3 control-label">1文章 2动态 3首页 4开屏：</label>-->
                <label class="col-sm-3 control-label">链接类型：</label>
                <div class="col-sm-8">
                    <select name="hrefType" class="form-control m-b" th:with="type=${@dict.getType('cat_href_type')}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{hrefType}"></option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">链接：</label>
                <div class="col-sm-8">
                    <input name="href" th:field="*{href}"  class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: jasny-bootstrap-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/advert";
        $("#form-advert-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                var form = $('#form-advert-edit')[0];
                var formdata = new FormData(form);
                $.ajax({
                    url: prefix + "/edit",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    success: function(result) {
                        $.operate.successCallback(result);
                    }
                })
                //$.operate.save(prefix + "/edit", $('#form-advert-edit').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增APP更新')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-appUpdateMessageBackstage-add">
            <!--<div class="form-group">
                <label class="col-sm-3 control-label">版本号：</label>
                <div class="col-sm-8">
                    <input name="version" class="form-control" type="text" required>
                </div>
            </div>-->
            <div class="alert alert-info">
                版本名称格式：X.Y.Z
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">版本号：</label>
                <div class="col-sm-8">
                    <input name="versionName" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">热更最小版本号：</label>
                <div class="col-sm-8">
                    <input name="minVersionName" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">强制更新：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                        <input type="radio" th:id="${'status_' + dict.dictCode}" name="forceUpdate" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">是否弹框：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                        <input type="radio" th:id="${'status_' + dict.dictCode}" name="popStr" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label">更新类型：</label>
                <div class="col-sm-8">
                    <select name="updateType" class="form-control m-b" th:with="type=${@dict.getType('cat_app_update_type')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">更新说明：</label>
                <div class="col-sm-8">
<!--                    <input name="upDesc" class="form-control" type="text" required>-->
                    <textarea type="text" rows="6" name="upDesc" class="form-control" placeholder="请输入文本" required></textarea>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">热更新下载地址：</label>
                <!-- 热更新下载地址 -->
                <div class="col-sm-8">
                    <input name="apkUrl" class="form-control" type="text">
                </div>
                <label class="col-sm-3 control-label"></label>
                <div class="col-sm-8">
                    <input name="file" class="form-control" type="file">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">整包更新下载地址：</label>
                <!-- 整包安装下载地址 -->
                <div class="col-sm-8">
                    <input name="apkEntireUrl" class="form-control" type="text">
                </div>
                <label class="col-sm-3 control-label"></label>
                <div class="col-sm-8">
                    <input name="apkEntireFile" class="form-control" type="file">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">客户端：</label>
                <div class="col-sm-8">
                    <!--<input name="client" class="form-control" type="text" required>-->
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_app_client_type')}">
                        <input type="radio" th:id="${'status_' + dict.dictCode}" name="client" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">更新包大小（上传文件自动计算）：</label>
                <div class="col-sm-8">
                    <input name="fileSize" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">摘要：</label>
                <div class="col-sm-8">
                    <input name="remark" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "cat/appUpdateMessageBackstage"
        $("#form-appUpdateMessageBackstage-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                var form = $('#form-appUpdateMessageBackstage-add')[0];
                var formdata = new FormData(form);
                $.ajax({
                    url: prefix + "/add",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function(result) {
                        $.operate.successCallback(result);
                    }
                })

                //$.operate.save(prefix + "/add", $('#form-appUpdateMessageBackstage-add').serialize());
            }
        }
    </script>
</body>
</html>
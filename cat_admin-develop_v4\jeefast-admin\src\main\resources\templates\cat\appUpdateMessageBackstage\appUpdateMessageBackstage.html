<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('APP更新列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>版本名称：</p>
                                <input type="text" name="versionName"/>
                            </li>
                            <li>
                                <p>强制更新：</p>
                                <select name="forceUpdate" th:with="type=${@dict.getType('cat_recommend')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                                <!--<input type="text" name="forceUpdate"/>-->
                            </li>
                            <li>
                                <p>更新类型：</p>
                                <select name="updateType" th:with="type=${@dict.getType('cat_app_update_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>客户端：</p>
                                <select name="forceUpdate" th:with="type=${@dict.getType('cat_app_client_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                                <!--<input type="text" name="client"/>-->
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:appUpdateMessageBackstage:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:appUpdateMessageBackstage:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:appUpdateMessageBackstage:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:appUpdateMessageBackstage:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:appUpdateMessageBackstage:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:appUpdateMessageBackstage:remove')}]];
        var forceUpdateDatas = [[${@dict.getType('cat_recommend')}]];
        var updateTypeDatas = [[${@dict.getType('cat_app_update_type')}]];
        var clientDatas = [[${@dict.getType('cat_app_client_type')}]];
        var prefix = ctx + "cat/appUpdateMessageBackstage";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "APP更新",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : 'id',
                    visible: false
                },
                {
                    field : 'version', 
                    title : '版本号'
                },
                {
                    field : 'versionName', 
                    title : '版本名称'
                },
                {
                    field : 'forceUpdate', 
                    title : '强制更新',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(forceUpdateDatas, value);
                    }
                },
                {
                    field : 'updateType', 
                    title : '更新类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(updateTypeDatas, value);
                    }
                },
                {
                    field : 'upDesc', 
                    title : '更新说明',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'apkUrl', 
                    title : '下载地址',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'client', 
                    title : '客户端',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(clientDatas, value);
                    }
                },
                {
                    field : 'remark', 
                    title : '摘要'
                },
                {
                    field : 'createDate', 
                    title : '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
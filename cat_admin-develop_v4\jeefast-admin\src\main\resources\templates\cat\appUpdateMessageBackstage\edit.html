<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改APP更新')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-appUpdateMessageBackstage-edit" th:object="${appUpdateMessageBackstage}">
            <input name="id" th:field="*{id}" type="hidden">
            <!--<div class="form-group">
                <label class="col-sm-3 control-label">版本号：</label>
                <div class="col-sm-8">
                    <input name="version" th:field="*{version}" class="form-control" type="text" required>
                </div>
            </div>-->
            <div class="alert alert-info">
                版本名称格式：X.Y.Z
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">版本号：</label>
                <div class="col-sm-8">
                    <input name="versionName" th:field="*{versionName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <div class="alert alert-info">
                    版本号格式：XYZ
                </div>
                <label class="col-sm-3 control-label">热更最小版本号：</label>
                <div class="col-sm-8">
                    <input name="minVersion" th:field="*{minVersion}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">强制更新：</label>
                <div class="col-sm-8">
                    <!--<input name="forceUpdate" th:field="*{forceUpdate}" class="form-control" type="text" required>-->
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                        <input type="radio" th:id="${'status_' + dict.dictCode}" name="forceUpdate" th:value="${dict.dictValue}" th:field="*{forceUpdate}" >
                        <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">是否弹框：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                        <input type="radio" th:id="${'status_' + dict.dictCode}" name="popStr" th:value="${dict.dictValue}" th:field="*{popStr}">
                        <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">更新类型：</label>
                <div class="col-sm-8">
                    <select name="updateType" class="form-control m-b" th:with="type=${@dict.getType('cat_app_update_type')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{updateType}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">更新说明：</label>
                <div class="col-sm-8">
<!--                    <input name="upDesc" th:field="*{upDesc}" class="form-control" type="text" required>-->
                    <textarea type="text" rows="6" name="upDesc" class="form-control" placeholder="请输入文本" th:field="*{upDesc}" required></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">热更新下载地址：</label>
                <div class="col-sm-8">
                    <input name="apkUrl" th:field="*{apkUrl}" class="form-control" type="text">
                </div>
                <label class="col-sm-3 control-label"></label>
                <div class="col-sm-8">
                    <input name="file" class="form-control" type="file">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">整包更新下载地址：</label>
                <!-- 整包安装下载地址 -->
                <div class="col-sm-8">
                    <input name="apkEntireUrl" th:field="*{apkEntireUrl}" class="form-control" type="text">
                </div>
                <label class="col-sm-3 control-label"></label>
                <div class="col-sm-8">
                    <input name="apkEntireFile" class="form-control" type="file">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">客户端：</label>
                <div class="col-sm-8">
                    <!--<input name="client" th:field="*{client}" class="form-control" type="text" required>-->
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_app_client_type')}">
                        <input type="radio" th:id="${'status_' + dict.dictCode}" name="client" th:value="${dict.dictValue}" th:field="*{client}" >
                        <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">更新包大小（上传文件自动计算）：</label>
                <div class="col-sm-8">
                    <input name="fileSize" class="form-control" type="text" th:field="*{fileSize}">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">摘要：</label>
                <div class="col-sm-8">
                    <input name="remark" th:field="*{remark}" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "cat/appUpdateMessageBackstage";
        $("#form-appUpdateMessageBackstage-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                var form = $('#form-appUpdateMessageBackstage-edit')[0];
                var formdata = new FormData(form);
                $.ajax({
                    url: prefix + "/edit",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function(result) {
                        $.operate.successCallback(result);
                    }
                })
                //$.operate.save(prefix + "/edit", $('#form-appUpdateMessageBackstage-edit').serialize());
            }
        }
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('文章列表')" />
    <meta name="referrer" content="no-referrer">
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>文章id：</p>
                                <input type="text" name="id"/>
                            </li>
                            <li>
                                <p>文章标题：</p>
                                <input type="text" name="title"/>
                            </li>
                            <li>
                                <p>文章类型：</p>
                                <select name="type" th:with="type=${@dict.getType('cat_article_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>文章状态：</p>
                                <select name="state" th:with="type=${@dict.getType('cat_article_state')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>是否推荐：</p>
                                <select name="isRecommend" th:with="type=${@dict.getType('cat_recommend')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>审核通过：</p>
                                <select name="isCheck" th:with="type=${@dict.getType('cat_recommend')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addFull()" shiro:hasPermission="cat:articleInfoBackstage:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editFull()" shiro:hasPermission="cat:articleInfoBackstage:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:articleInfoBackstage:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info single disabled" onclick="bindTpic()" shiro:hasPermission="cat:game:remove">
                    <i class="fa fa-wrench"></i> 绑定话题
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:articleInfoBackstage:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:articleInfoBackstage:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:articleInfoBackstage:remove')}]];
        var typeDatas = [[${@dict.getType('cat_article_type')}]];
        var stateDatas = [[${@dict.getType('cat_article_state')}]];
        var isRecommendDatas = [[${@dict.getType('cat_recommend')}]];
        var isCheckDatas = [[${@dict.getType('cat_recommend')}]];
        var prefix = ctx + "cat/articleInfoBackstage";


        //设置话题
        function bindTpic(){
            var id = $.table.selectColumns('articleId');
            debugger;
            if(id.indexOf(",") != -1){
                alert("只能选中一个进行绑定");
                return;
            }
            $.modal.open("绑定话题",prefix + "/bindTopicList?id="+id);
        }

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                sortName: "createDate",
                sortOrder: "desc",
                modalName: "文章",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'articleId', 
                    title : 'id',
                    visible: false
                },
                {
                    field : 'coverImage', 
                    title : '封面图',
                    formatter: function(value, row, index) {
                        // 图片预览（注意：如存储在本地直接获取数据库路径，如有配置context-path需要使用ctx+路径）
                        // 如：/profile/upload/2019/08/08/3b7a839aced67397bac694d77611ce72.png
                        if(value){
                            return $.table.imageView(value);
                        }else {
                            return $.table.imageView('/jeefast.png');
                        }
                    }
                },
                {
                    field : 'title', 
                    title : '文章标题',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'type', 
                    title : '文章类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(typeDatas, value);
                    }
                },
                {
                    field : 'state', 
                    title : '文章状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(stateDatas, value);
                    }
                },
                {
                    field : 'visitCount', 
                    title : '访问量',
                    sortable: true
                },
                {
                    field : 'commentCount', 
                    title : '评论数',
                    sortable: true
                },
                {
                    field : 'praiseCount', 
                    title : '点赞数',
                    sortable: true
                },
                {
                    field : 'isRecommend', 
                    title : '推荐',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(isRecommendDatas, value);
                    }
                },
                {
                    field : 'sortOn', 
                    title : '排序值',
                    sortable: true
                },
                {
                    field : 'author', 
                    title : '作者名称'
                },
                {
                    field : 'createDate', 
                    title : '创建时间',
                    sortable: true
                },
                {
                    field : 'weight', 
                    title : '权重',
                    sortable: true
                },
                {
                    field : 'isCheck', 
                    title : '审核通过',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(isCheckDatas, value);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.editFull(\'' + row.articleId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.articleId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
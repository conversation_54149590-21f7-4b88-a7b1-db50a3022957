<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改文章')" />
    <meta name="referrer" content="no-referrer">
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="row">
            <div class="col-sm-5" style="padding-left:0px;">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>基础信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="alert alert-info">
                            如有不懂请查看操作文档说
                        </div>
                        <form class="form-horizontal m" id="form-articleInfoBackstage-edit" th:object="${articleInfoBackstage}">
                            <input name="articleId" th:field="*{articleId}" type="hidden">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">封面图：</label>
                                <div class="col-sm-8">
                                    <div class="fileinput fileinput-new" data-provides="fileinput">
                                        <div class="fileinput-new thumbnail" style="width: 336px; height: 140px;">
                                            <img />
                                            <img th:src="*{coverImage}">
                                        </div>
                                        <div class="fileinput-preview fileinput-exists thumbnail" style="max-width: 200px; max-height: 150px;"></div>
                                        <div>
                                            <span class="btn btn-white btn-file"><span class="fileinput-new">选择图片</span><span class="fileinput-exists">更改</span>
                                                <input id="bannerFile" name="file" class="form-control" type="file">
                                            </span>
                                            <a href="#" class="btn btn-white fileinput-exists" data-dismiss="fileinput">清除</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">文章标题：</label>
                                <div class="col-sm-8">
                                    <input name="title" th:field="*{title}" class="form-control" type="text" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">文章类型：</label>
                                <div class="col-sm-8">
                                    <select name="type" class="form-control m-b" th:with="type=${@dict.getType('cat_article_type')}" required>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{type}"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">文章状态：</label>
                                <div class="col-sm-8">
                                    <select name="state" class="form-control m-b" th:with="type=${@dict.getType('cat_article_state')}" required>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{state}"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">内容摘要：</label>
                                <div class="col-sm-8">
                                    <textarea name="summary" class="form-control">[[*{summary}]]</textarea>
                                </div>
                            </div>
                            <!--<div class="form-group">
                                <label class="col-sm-3 control-label">文章内容：</label>
                                <div class="col-sm-8">
                                    <input name="content" th:field="*{content}" class="form-control" type="text">
                                </div>
                            </div>-->
                            <div class="form-group">
                                <label class="col-sm-3 control-label">访问量：</label>
                                <div class="col-sm-8">
                                    <input name="visitCount" th:field="*{visitCount}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">评论数：</label>
                                <div class="col-sm-8">
                                    <input name="commentCount" th:field="*{commentCount}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">点赞数：</label>
                                <div class="col-sm-8">
                                    <input name="praiseCount" th:field="*{praiseCount}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">是否推荐：</label>
                                <div class="col-sm-8">
                                    <select name="isRecommend" class="form-control m-b" th:with="type=${@dict.getType('cat_recommend')}" required>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{isRecommend}"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">排序值：</label>
                                <div class="col-sm-8">
                                    <input name="sortOn" th:field="*{sortOn}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">作者名称：</label>
                                <div class="col-sm-8">
                                    <input name="author" th:field="*{author}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">来源连接：</label>
                                <div class="col-sm-8">
                                    <input name="sourceUrl" th:field="*{sourceUrl}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">权重：</label>
                                <div class="col-sm-8">
                                    <input name="weight" th:field="*{weight}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">审核状态：</label>
                                <div class="col-sm-8">
                                    <select name="isCheck" class="form-control m-b" th:with="type=${@dict.getType('cat_recommend')}" required>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{isCheck}"></option>
                                    </select>
                                </div>
                            </div>
                        </form>
                        <div class="clearfix"></div>
                    </div>
                </div>
            </div>
            <div class="col-sm-7" style="padding-left:0px;">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>文章内容</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row form-body form-horizontal m-t">
                            <div class="col-sm-8">
                                <script  id="editor" name="content" type="text/plain" style="width:640px;height:750px;" th:utext="${articleInfoBackstage.content}"></script>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: jasny-bootstrap-js" />
    <script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.all.min.js"> </script>
    <!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
    <!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
    <script type="text/javascript" charset="utf-8" src="/ueditor/lang/zh-cn/zh-cn.js"></script>
    <script type="text/javascript">

        //实例化编辑器
        //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
        var ue = UE.getEditor('editor');

        var prefix = ctx + "cat/articleInfoBackstage";
        $("#form-articleInfoBackstage-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                var form = $('#form-articleInfoBackstage-edit')[0];
                var formdata = new FormData(form);
                formdata.append("content",UE.getEditor('editor').getContent());
                $.ajax({
                    url: prefix + "/edit",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    success: function(result) {
                        $.operate.successCallback(result);
                    }
                })
                //$.operate.save(prefix + "/edit", $('#form-articleInfoBackstage-edit').serialize());
            }
        }
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('绑定话题')" />
    <th:block th:include="include :: select2-css" />
    <meta name="referrer" content="no-referrer">
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-article-bind">
            <div class="form-group">
                <label class="col-sm-3 control-label">栏目：</label>
                <div class="col-sm-8">
                    <select id="topicSelect" class="form-control select2-multiple" multiple name="topicId">
                        <option th:each="item:${topics}" th:value="${item.topicId}" th:text="${item.topicName}" th:selected="${item.flag}"></option>
                    </select>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <script type="text/javascript">
        function submitHandler() {
            var articleIds = parent.$.table.selectColumns("articleId").join(",");
            debugger;
            var formdata = new FormData();
            formdata.append("articleIds", articleIds);
            formdata.append("topicIds", $("#topicSelect").val());
            $.ajax({
                url: ctx + "cat/articleInfoBackstage/bindTopic",
                data: formdata,
                type: "post",
                processData: false,
                contentType: false,
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        parent.$.table.refresh();
                        $.modal.close();
                    }
                },
                error: function(error) {
                    console.error(error);
                }

            })
        }

    </script>
</body>
</html>
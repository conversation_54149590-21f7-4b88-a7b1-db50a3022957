<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('认证审核列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
<!--                            <li>-->
<!--                                <p>认证记录id：</p>-->
<!--                                <input type="text" name="authId"/>-->
<!--                            </li>-->

<!--                            <li>-->
<!--                                <p>审核人：</p>-->
<!--                                <input type="text" name="userId"/>-->
<!--                            </li>-->
                            <li>
                                <p>审核人名称：</p>
                                <input type="text" name="truename"/>
                            </li>
                            <li>
                                <p>审核状态：</p>
                                <select name="status" th:with="type=${@dict.getType('cat_audit_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
<!--                            <li>-->
<!--                                <p>审核描述：</p>-->
<!--                                <input type="text" name="description"/>-->
<!--                            </li>-->
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
<!--                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="audit:audit:add">-->
<!--                    <i class="fa fa-plus"></i> 添加-->
<!--                </a>-->
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="audit:audit:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="audit:audit:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
<!--                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="audit:audit:export">-->
<!--                    <i class="fa fa-download"></i> 导出-->
<!--                 </a>-->
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:audit:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:audit:remove')}]];
        var prefix = ctx + "cat/audit";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "认证审核",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : '主键',
                    visible: false
                },
                {
                    field : 'authId', 
                    title : '认证记录id'
                },
                {
                    field : 'status', 
                    title : '审核状态'
                },
                {
                    field : 'userId', 
                    title : '审核人'
                },
                {
                    field : 'truename', 
                    title : '审核人名称'
                },
                {
                    field : 'description', 
                    title : '审核描述'
                },
                {
                    field : 'createDate', 
                    title : '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改认证审核')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-audit-edit" th:object="${authAuditInfo}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">认证记录id：</label>
                <div class="col-sm-8">
                    <input name="authId" th:field="*{authId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">审核状态 1已通过 2未通过 3待审核：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" name="status" value="" required>
                        <label th:for="status" th:text="未知"></label>
                    </div>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 代码生成请选择字典属性</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">审核人：</label>
                <div class="col-sm-8">
                    <input name="userId" th:field="*{userId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">审核人名称：</label>
                <div class="col-sm-8">
                    <input name="truename" th:field="*{truename}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">审核描述：</label>
                <div class="col-sm-8">
                    <input name="description" th:field="*{description}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" th:value="${#dates.format(authAuditInfo.createDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/audit";
        $("#form-audit-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-audit-edit').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
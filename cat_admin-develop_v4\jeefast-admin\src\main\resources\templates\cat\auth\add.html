<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增认证信息')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-auth-add">
            <div class="form-group">    
<!--                <label class="col-sm-3 control-label">认证类型 1个人认证 2机构认证：</label>-->
                <label class="col-sm-3 control-label">认证类型：</label>
                <div class="col-sm-8">
                    <select id="authType"  class="form-control"  name="authType" th:with="type=${@dict.getType('cat_auth_type')}">
                        <option value="">--请选择认证类型--</option>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
<!--                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 代码生成请选择字典属性</span>-->
                </div>
            </div>
            <div class="form-group org">
                <label class="col-sm-3 control-label">名称：</label>
                <div class="col-sm-8">
                    <input name="name" class="form-control" type="text">
                </div>
            </div>

            <div class="form-group both">
                <label class="col-sm-3 control-label">姓名：</label>
                <div class="col-sm-8">
                    <input name="truename" class="form-control" type="text" required>
                </div>
            </div>

            <div class="form-group both">
                <label class="col-sm-3 control-label">手机号：</label>
                <div class="col-sm-8">
                    <input name="mobile" class="form-control" type="text" required>
                </div>
            </div>

            <div class="form-group both">
                <label class="col-sm-3 control-label">电子邮箱：</label>
                <div class="col-sm-8">
                    <input name="email" class="form-control" type="text" required>
                </div>
            </div>


            <div class="form-group org">
                <label class="col-sm-3 control-label">网站链接：</label>
                <div class="col-sm-8">
                    <input name="link" class="form-control" type="text">
                </div>
            </div>

            <!--<div class="form-group org">
                <label class="col-sm-3 control-label">telegram：</label>
                <div class="col-sm-8">
                    <input name="telegram" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group org">
                <label class="col-sm-3 control-label">skype：</label>
                <div class="col-sm-8">
                    <input name="skype" class="form-control" type="text">
                </div>
            </div>-->

            <div class="form-group org">
                <label class="col-sm-3 control-label">地址：</label>
                <div class="col-sm-8">
                    <input name="address" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group both">
                <label class="col-sm-3 control-label">认证描述：</label>
                <div class="col-sm-8">
                    <input name="authDesc" class="form-control" type="text">
                </div>
            </div>
<!--            <div class="form-group">    -->
<!--                <label class="col-sm-3 control-label">创建时间：</label>-->
<!--                <div class="col-sm-8">-->
<!--                    <div class="input-group date">-->
<!--                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>-->
<!--                        <input name="createDate" class="form-control" placeholder="yyyy-MM-dd" type="text">-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
        </form>
    </div>
    <th:block th:include="include :: footer" />
<!--    <th:block th:include="include :: datetimepicker-js" />-->
    <script type="text/javascript">
        var prefix = ctx + "cat/auth"
        $("#form-auth-add").validate({
            focusCleanup: true
        });

        $("#authType").on('change',function(e){
            var selectVal = $(this).val();
            // $("#form-auth-add .form-group").show();
            // if("1" == selectVal) {
            //     //个人认证
            //     $(".org").hide();
            // }else if("2" == selectVal){
            //     //企业认证
            //     $(".org").show();
            // }
            ctl(selectVal);
        })

        function ctl(selectVal){
            $("#form-auth-add .form-group").show();
            if("1" == selectVal) {
                //个人认证
                $(".org").hide();
            }else if("2" == selectVal){
                //企业认证
                $(".org").show();
            }
        }

        ctl();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-auth-add').serialize());
            }
        }
        //
        // $("input[name='createDate']").datetimepicker({
        //     format: "yyyy-mm-dd",
        //     minView: "month",
        //     autoclose: true
        // });
    </script>
</body>
</html>
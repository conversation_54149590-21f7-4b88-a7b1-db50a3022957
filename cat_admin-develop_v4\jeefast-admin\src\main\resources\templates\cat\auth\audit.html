<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('绑定栏目')" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
	<form class="form-horizontal m" id="form-game-add">

		<div class="form-group">
			<label class="col-sm-3 control-label">审核结果：</label>
			<div class="col-sm-8">
				<select id="status" class="form-control" name="status" th:with="type=${@dict.getType('cat_audit_status')}">
					<option value="">--请选择审核结果--</option>
					<option value="1">通过</option>
					<option value="2">不通过</option>
				</select>
			</div>
		</div>

		<div class="form-group">
			<label class="col-sm-3 control-label">备注：</label>
			<div class="col-sm-8">
				<textarea id="description" name="description" class="form-control" ></textarea>
			</div>
			<input type="hidden" id = "gameIds" name="gameIds">
		</div>

	</form>
</div>
<th:block th:include="include :: footer" />
<script type="text/javascript">
function submitHandler() {
	var authIds = parent.$.table.selectColumns("id").join(",");
	var status = $("#status").val();
	var description = $("#description").val();
	var formdata = new FormData();
	formdata.append("authIds", authIds);
	formdata.append("status", status);
	formdata.append("description", description);
    $.ajax({
        url: ctx + "cat/audit/edit",
        data: formdata,
        type: "post",
        processData: false,
        contentType: false,
        success: function(result) {
			if (result.code == web_status.SUCCESS) {
				parent.$.table.refresh();
				$.modal.close();
			}
        },
		error: function(error) {
			console.error(error);
		}

    })


}
</script>
</body>
</html>

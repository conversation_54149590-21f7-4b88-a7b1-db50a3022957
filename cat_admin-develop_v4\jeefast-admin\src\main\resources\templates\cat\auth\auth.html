<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('认证信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
<!--                                <p>认证类型 1个人认证 2机构认证：</p>-->
                                <p>认证类型：</p>
                                <select name="authType" th:with="type=${@dict.getType('cat_auth_type')}">
                                    <option value="">--请选择认证类型--</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>

                            <li>
                                <!--                                <p>认证类型 1个人认证 2机构认证：</p>-->
                                <p>审核状态：</p>
                                <select name="status" th:with="type=${@dict.getType('cat_audit_status')}">
                                    <option value="">--请选择--</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
<!--                            <li>-->
<!--                                <p>认证描述：</p>-->
<!--                                <input type="text" name="authDesc"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <p>电子邮箱：</p>-->
<!--                                <input type="text" name="email"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <p>手机号：</p>-->
<!--                                <input type="text" name="mobile"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <p>网站链接：</p>-->
<!--                                <input type="text" name="link"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--                                <p>名称：</p>-->
<!--                                <input type="text" name="name"/>-->
<!--                            </li>-->
<!--                            <li>-->
<!--&lt;!&ndash;                                <p>机构认真公司负责人，个人认证真实姓名：</p>&ndash;&gt;-->
<!--                                <p>姓名：</p>-->
<!--                                <input type="text" name="truename"/>-->
<!--                            </li>-->
<!--                            <li class="select-time">-->
<!--                                <p>创建时间：</p>-->
<!--                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>-->
<!--                                <span>-</span>-->
<!--                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>-->
<!--                            </li>-->
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:auth:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:auth:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:auth:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>

                <a class="btn btn-info multiple disabled" onclick="audit()" shiro:hasPermission="cat:game:remove">
                    <i class="fa fa-wrench"></i> 审核
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:auth:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:auth:remove')}]];
        var authTypeFlag = [[${@dict.getType('cat_auth_type')}]];
        var prefix = ctx + "cat/auth";
        //设置栏目
        function audit(){

            $.modal.open("审核",prefix + "/audit");
        }
        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "认证信息",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : 'id',
                    visible: false
                },
                {
                    field : 'authType', 
                    title : '认证类型',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(authTypeFlag, value);
                    }
                },
                {
                    field : 'authDesc', 
                    title : '认证描述',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'email', 
                    title : '电子邮箱',
                    visible: false
                },
                {
                    field : 'mobile', 
                    title : '手机号'
                },
                {
                    field : 'link', 
                    title : '网站链接',
                    visible: false
                },
                {
                    field : 'name', 
                    title : '名称'
                },
                {
                    field : 'truename', 
                    title : '姓名'
                },
                {
                    field : 'idCard',
                    title : '证件号'
                },
                {
                    field : 'address',
                    title : '地址'
                },
                {
                    field : 'status',
                    title : '审核状态',
                    formatter: function(value, row, index) {
                        return ["","已通过","未通过","待审核"][value];
                    }
                },
                {
                    field : 'description',
                    title : '审核备注',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'createDate', 
                    title : '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
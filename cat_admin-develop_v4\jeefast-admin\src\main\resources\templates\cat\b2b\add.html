<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增渠道医院配置')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-b-add">
            <div class="form-group">
                <label class="col-sm-3 control-label">渠道：</label>
                <div class="col-sm-8">
                    <select name="channelId" class="form-control m-b">
                        <option th:each="channel:${channel_list}" th:value="${channel.id}" th:text="${channel.channelName}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">医院：</label>
                <div class="col-sm-8">
                    <select name="hospitalId" class="form-control m-b">
                        <option th:each="hospital:${hospital_list}" th:value="${hospital.id}" th:text="${hospital.hospitalName}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">渠道医院：</label>
                <div class="col-sm-8">
                    <input name="writeUid" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">配置编码：</label>
                <div class="col-sm-8">
                    <input name="createUid" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">渠道功能配置：</label>
                <div class="col-sm-8">
                    <input name="functionConfig" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">其他配置：</label>
                <div class="col-sm-8">
                    <input name="other" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">用户id：</label>
                <div class="col-sm-8">
                    <input name="userId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">签名：</label>
                <div class="col-sm-8">
                    <input name="sign" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="ext1" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="ext2" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/b2b"
        $("#form-b-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-b-add').serialize());
            }
        }
    </script>
</body>
</html>
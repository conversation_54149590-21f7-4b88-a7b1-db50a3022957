<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('渠道医院配置列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>渠道医院：</p>
                                <input type="text" name="writeUid"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:b2b:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:b2b:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:b2b:remove')}]];
        var prefix = ctx + "cat/b2b";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "外部调用",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'channelName',
                    title : '渠道'
                },
                {
                    field : 'hospitalName',
                    title : '医院'
                },
                {
                    field : 'writeUid', 
                    title : '渠道医院'
                },
                {
                    field : 'createUid',
                    title : '配置编码'
                },
                {
                    field : 'other', 
                    title : '其他配置'
                },
                {
                    field : 'functionConfig',
                    title : '渠道功能配置'
                },
                {
                    field : 'userId', 
                    title : '用户id'
                },
                {
                    field : 'sign', 
                    title : '签名'
                },
                {
                    field : 'ext1', 
                    title : '扩展字段1'
                },
                // {
                //     field : 'ext2',
                //     title : '扩展字段2'
                // },
                // {
                //     field : 'createDate',
                //     title : '创建时间'
                // },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.userId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.userId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
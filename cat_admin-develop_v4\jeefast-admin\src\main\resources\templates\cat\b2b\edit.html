<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改渠道医院配置')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-b-edit" th:object="${bToB}">
            <div class="form-group">
                <label class="col-sm-3 control-label">渠道：</label>
                <div class="col-sm-8">
                    <select name="channelId" class="form-control m-b">
                        <option th:each="channel:${channel_list}" th:value="${channel.id}" th:text="${channel.channelName}"  th:field="*{channelId}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">医院：</label>
                <div class="col-sm-8">
                    <select name="hospitalId" class="form-control m-b">
                        <option th:each="hospital:${hospital_list}" th:value="${hospital.id}" th:text="${hospital.hospitalName}" th:field="*{hospitalId}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">渠道医院：</label>
                <div class="col-sm-8">
                    <input name="writeUid" th:field="*{writeUid}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">配置编码：</label>
                <div class="col-sm-8">
                    <input name="createUid" th:field="*{createUid}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">渠道功能配置：</label>
                <div class="col-sm-8">
                    <input name="functionConfig" th:field="*{functionConfig}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">其他配置：</label>
                <div class="col-sm-8">
                    <input name="other" th:field="*{other}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">用户Id：</label>
                <div class="col-sm-8">
                    <input name="userId" th:field="*{userId}" readonly class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">签名：</label>
                <div class="col-sm-8">
                    <input name="sign" th:field="*{sign}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="ext1" th:field="*{ext1}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="ext2" th:field="*{ext2}" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/b2b";
        $("#form-b-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-b-edit').serialize());
            }
        }
    </script>
</body>
</html>
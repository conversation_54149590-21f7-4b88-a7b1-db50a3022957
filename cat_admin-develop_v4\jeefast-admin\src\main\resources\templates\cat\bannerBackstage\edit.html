<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改首页banner图')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-bannerBackstage-edit" th:object="${bannerInfoBackstage}">
            <input name="bannerId" th:field="*{bannerId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">图片地址：</label>
                <div class="col-sm-8">
                    <div class="fileinput fileinput-new" data-provides="fileinput">
                        <div class="fileinput-new thumbnail" style="width: 336px; height: 140px;">
                            <img th:src="*{imageUrl}">
                        </div>
                        <div class="fileinput-preview fileinput-exists thumbnail" style="max-width: 200px; max-height: 150px;"></div>
                        <div>
                            <span class="btn btn-white btn-file"><span class="fileinput-new">选择图片</span><span class="fileinput-exists">更改</span>
                                <input id="bannerFile" name="file" class="form-control" type="file">
                            </span>
                            <a href="#" class="btn btn-white fileinput-exists" data-dismiss="fileinput">清除</a>
                        </div>
                    </div>
                </div>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i>325*120像素尺寸比例</span>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">连接地址：</label>
                <div class="col-sm-8">
                    <input name="href" th:field="*{href}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">链接类型：</label>
                <div class="col-sm-8">
                    <select name="hrefType" class="form-control m-b" th:with="type=${@dict.getType('cat_href_type')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{hrefType}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">业务类型：</label>
                <div class="col-sm-8">
                    <select name="businessType" class="form-control m-b" th:with="type=${@dict.getType('cat_app_type')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{businessType}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">排序值：</label>
                <div class="col-sm-8">
                    <input name="sortNo" th:field="*{sortNo}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input name="remark" th:field="*{remark}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" th:value="${#dates.format(bannerInfoBackstage.createDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: jasny-bootstrap-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/bannerBackstage";
        $("#form-bannerBackstage-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                var form = $('#form-bannerBackstage-edit')[0];
                var formdata = new FormData(form);
                $.ajax({
                    url: prefix + "/edit",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    success: function(result) {
                        $.operate.successCallback(result);
                    }
                })


                //$.operate.save(prefix + "/edit", $('#form-bannerBackstage-edit').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
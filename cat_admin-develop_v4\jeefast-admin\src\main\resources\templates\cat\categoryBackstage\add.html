<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增商品类别')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-categoryBackstage-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">父类级ID：</label>
                <div class="col-sm-8">
                    <input name="parentId" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">类别名称：</label>
                <div class="col-sm-8">
                    <input name="name" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">分类图标：</label>
                <div class="col-sm-8">
                    <!--<input name="image" class="form-control" type="text">-->
                    <div class="fileinput fileinput-new" data-provides="fileinput">
                        <div class="fileinput-new thumbnail" style="width: 336px; height: 140px;">
                            <img />
                        </div>
                        <div class="fileinput-preview fileinput-exists thumbnail" style="max-width: 200px; max-height: 150px;"></div>
                        <div>
                            <span class="btn btn-white btn-file"><span class="fileinput-new">选择图片</span><span class="fileinput-exists">更改</span>
                                <input id="bannerFile" name="file" class="form-control" type="file">
                            </span>
                            <a href="#" class="btn btn-white fileinput-exists" data-dismiss="fileinput">清除</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">类别状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_app_menu_status')}">
                        <input type="radio" th:id="${'status_' + dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}" required>
                        <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">排序值：</label>
                <div class="col-sm-8">
                    <input name="sortOn" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: jasny-bootstrap-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/categoryBackstage"
        $("#form-categoryBackstage-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                var form = $('#form-categoryBackstage-add')[0];
                var formdata = new FormData(form);
                $.ajax({
                    url: prefix + "/add",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    success: function(result) {
                        $.operate.successCallback(result);
                    }
                })
                //$.operate.save(prefix + "/add", $('#form-categoryBackstage-add').serialize());
            }
        }
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改动态分类')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-category-edit" th:object="${dynamicCategory}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">标题：</label>
                <div class="col-sm-8">
                    <input name="title" th:field="*{title}"  maxlength="6" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">关键词：</label>
                <div class="col-sm-8">
                    <textarea name="keyword" class="form-control"  maxlength="255">[[*{keyword}]]</textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">排序：</label>
                <div class="col-sm-8">
                    <input name="sortNo" th:field="*{sortNo}" class="form-control" type="number" min="1" step="1" max="9999999999" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否默认：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                        <input type="radio" th:id="${'beDefault_' + dict.dictCode}" name="beDefault" th:value="${dict.dictValue}" th:field="*{beDefault}" required>
                        <label th:for="${'beDefault_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否禁用：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                        <input type="radio" th:id="${'beDisabled_' + dict.dictCode}" name="beDisabled" th:value="${dict.dictValue}" th:field="*{beDisabled}" required>
                        <label th:for="${'beDisabled_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/category";
        $("#form-category-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-category-edit').serialize());
            }
        }
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增AI分诊渠道')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-channel-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">渠道编码：</label>
                <div class="col-sm-8">
                    <input name="channelCode" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">渠道名称：</label>
                <div class="col-sm-8">
                    <input name="channelName" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" name="status" value="1">
                        <label th:for="status" th:text="启用"></label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" name="status" value="2">
                        <label th:for="status" th:text="停止"></label>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/channel"
        $("#form-channel-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-channel-add').serialize());
            }
        }
    </script>
</body>
</html>
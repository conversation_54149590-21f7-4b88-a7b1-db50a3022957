<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('AI分诊渠道列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>渠道名称：</p>
                                <input type="text" name="channelName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:channel:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:channel:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:channel:remove')}]];
        var prefix = ctx + "cat/channel";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "AI分诊渠道",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'channelCode',
                    title : '渠道编码'
                },
                {
                    field : 'channelName',
                    title : '渠道名称'
                },
                {
                    field : 'createDate', 
                    title : '创建时间'
                },
                {
                    visible: editFlag == 'hidden' ? false : true,
                    title: '状态',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return statusTools(row);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        /* 用户状态显示 */
        function statusTools(row) {
            if (row.status == 2) {
                return '<i class=\"fa fa-toggle-off text-info fa-2x\" onclick="enable(\'' + row.id + '\')"></i> ';
            } else {
                return '<i class=\"fa fa-toggle-on text-info fa-2x\" onclick="disable(\'' + row.id + '\')"></i> ';
            }
        }

        /* 停用 */
        function disable(id) {
            $.modal.confirm("确认要停用吗？", function() {
                $.operate.post(prefix + "/changeStatus", { "id": id, "status": 2 });
            })
        }

        /* 启用 */
        function enable(id) {
            $.modal.confirm("确认要启用吗？", function() {
                $.operate.post(prefix + "/changeStatus", { "id": id, "status": 1 });
            })
        }

    </script>
</body>
</html>
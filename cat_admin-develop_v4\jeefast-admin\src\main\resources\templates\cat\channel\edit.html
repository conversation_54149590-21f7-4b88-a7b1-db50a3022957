<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改AI分诊渠道')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-channel-edit" th:object="${channel}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">渠道编码：</label>
                <div class="col-sm-8">
                    <input name="channelCode" th:field="*{channelCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">渠道名称：</label>
                <div class="col-sm-8">
                    <input name="channelName" th:field="*{channelName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">状态 1启用 2停止：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" name="status" value="1" th:checked="${channel.status == 1 ? true : false}">
                        <label th:for="status" th:text="启用"></label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" name="status" value="2" th:checked="${channel.status == 2 ? true : false}">
                        <label th:for="status" th:text="停止"></label>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/channel";
        $("#form-channel-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-channel-edit').serialize());
            }
        }
    </script>
</body>
</html>
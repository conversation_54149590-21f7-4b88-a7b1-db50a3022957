<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增评论管理')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-commentInfoBackstage-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">父评论id：</label>
                <div class="col-sm-8">
                    <input name="parentId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">父评论ids：</label>
                <div class="col-sm-8">
                    <input name="parentIds" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">评论内容：</label>
                <div class="col-sm-8">
                    <textarea name="content" class="form-control"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">评论对象ID：</label>
                <div class="col-sm-8">
                    <input name="businessId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">评论对象类型：</label>
                <div class="col-sm-8">
                    <select name="businessType" class="form-control m-b" th:with="type=${@dict.getType('cat_business_type')}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">评论者ID：</label>
                <div class="col-sm-8">
                    <input name="userId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">点赞数：</label>
                <div class="col-sm-8">
                    <input name="praiseCount" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">回复数：</label>
                <div class="col-sm-8">
                    <input name="replyCount" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">评论时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/commentInfoBackstage"
        $("#form-commentInfoBackstage-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-commentInfoBackstage-add').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
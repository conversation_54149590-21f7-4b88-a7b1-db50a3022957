<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('评论管理列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>父评论ID：</p>
                                <input type="text" name="parentId"/>
                            </li>
                            <li>
                                <p>用户ID：</p>
                                <input type="text" name="userId"/>
                            </li>
                            <li>
                                <p>主题ID：</p>
                                <input type="text" name="businessId"/>
                            </li>
                            <li>
                                <p>主题类型：</p>
                                <select name="businessType" th:with="type=${@dict.getType('cat_business_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li class="select-time">
                                <p>评论时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:commentInfoBackstage:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:commentInfoBackstage:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:commentInfoBackstage:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:commentInfoBackstage:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:commentInfoBackstage:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:commentInfoBackstage:remove')}]];
        var businessTypeDatas = [[${@dict.getType('cat_business_type')}]];
        var isCheckDatas = [[${@dict.getType('cat_recommend')}]];
        var prefix = ctx + "cat/commentInfoBackstage";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                sortName: "createDate",
                sortOrder: "desc",
                modalName: "评论管理",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'commentId', 
                    title : 'comment_id',
                    visible: false
                },
                {
                    field : 'content', 
                    title : '评论内容',
                    // width : '200px',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'parentId',
                    title : '类型',
                    formatter: function(value, row, index) {
                        if(value){
                            return '回复';
                        }else {
                            return '评论';
                        }
                    }
                },
                {
                    field : 'businessId',
                    title : '主题id',
                    visible: false
                },
                {
                    field : 'businessType', 
                    title : '主题类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(businessTypeDatas, value);
                    }
                },
                {
                    field : 'praiseCount', 
                    title : '点赞数',
                    sortable: true
                },
                {
                    field : 'replyCount', 
                    title : '回复数',
                    sortable: true
                },
                {
                    field : 'userId',
                    title : '作者id',
                    visible: false
                },
                {
                    field : 'userName',
                    title : '作者'
                },
                {
                    field : 'createDate', 
                    title : '评论时间',
                    sortable: true
                },
                {
                    field : 'isCheck',
                    title : '审核通过',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(isCheckDatas, value);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.commentId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.commentId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
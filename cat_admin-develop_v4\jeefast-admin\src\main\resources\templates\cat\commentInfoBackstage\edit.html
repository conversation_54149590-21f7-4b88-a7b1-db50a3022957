<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改评论管理')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-commentInfoBackstage-edit" th:object="${commentInfoBackstage}">
            <input name="commentId" th:field="*{commentId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">父评论id：</label>
                <div class="col-sm-8">
                    <input name="parentId" th:field="*{parentId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">父评论ids：</label>
                <div class="col-sm-8">
                    <input name="parentIds" th:field="*{parentIds}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">评论内容：</label>
                <div class="col-sm-8">
                    <textarea name="content" class="form-control">[[*{content}]]</textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">评论对象ID：</label>
                <div class="col-sm-8">
                    <input name="businessId" th:field="*{businessId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">评论对象类型：</label>
                <div class="col-sm-8">
                    <select name="businessType" class="form-control m-b" th:with="type=${@dict.getType('cat_business_type')}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{businessType}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">评论者ID：</label>
                <div class="col-sm-8">
                    <input name="userId" th:field="*{userId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">点赞数：</label>
                <div class="col-sm-8">
                    <input name="praiseCount" th:field="*{praiseCount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">回复数：</label>
                <div class="col-sm-8">
                    <input name="replyCount" th:field="*{replyCount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">评论时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" th:value="${#dates.format(commentInfoBackstage.createDate, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">审核状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                        <input type="radio" th:id="${'isCheck_' + dict.dictCode}" name="isCheck" th:value="${dict.dictValue}" th:field="*{isCheck}" required>
                        <label th:for="${'isCheck_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/commentInfoBackstage";
        $("#form-commentInfoBackstage-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-commentInfoBackstage-edit').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            // format: "yyyy-mm-dd",
            // minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('用户投诉列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>用户ID：</p>
                                <input type="text" name="userId"/>
                            </li>
                            <li>
                                <p>业务id：</p>
                                <input type="text" name="businessId"/>
                            </li>
                            <li>
                                <p>业务类型：</p>
                                <select name="businessType">
                                    <option value="">所有</option>
                                    <option value="1">动态</option>
                                    <option value="2">文章</option>
                                    <option value="3">评论</option>
                                    <option value="4">宠物</option>
                                </select>
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <!--<a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:complainInfo:add">
                    <i class="fa fa-plus"></i> 添加
                </a>-->
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:complainInfo:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:complainInfo:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:complainInfo:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:complainInfo:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:complainInfo:remove')}]];
        var businessTypeDatas = [{"dictValue":"1","dictLabel":"动态","listClass":"default"},{"dictValue":"2","dictLabel":"文章","listClass":"default"},{"dictValue":"3","dictLabel":"评论","listClass":"default"}];
        var prefix = ctx + "cat/complainInfo";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "用户投诉",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'complainId', 
                    title : 'id',
                    visible: false
                },
                {
                    field : 'userId', 
                    title : '用户ID'
                },
                {
                    field : 'businessId', 
                    title : '业务id'
                },
                {
                    field : 'businessType', 
                    title : '业务类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(businessTypeDatas, value);
                    }
                },
                {
                    field : 'type', 
                    title : '类型'
                },
                {
                    field : 'depict', 
                    title : '描述'
                },
                {
                    field : 'phone', 
                    title : '手机号'
                },
                {
                    field : 'createDate', 
                    title : '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.complainId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.complainId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
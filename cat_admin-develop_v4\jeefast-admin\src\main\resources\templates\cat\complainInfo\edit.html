<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改用户投诉')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-complainInfo-edit" th:object="${complainInfo}">
            <input name="complainId" th:field="*{complainId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">用户ID：</label>
                <div class="col-sm-8">
                    <input name="userId" th:field="*{userId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">业务id：</label>
                <div class="col-sm-8">
                    <input name="businessId" th:field="*{businessId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">业务类型：</label>
                <div class="col-sm-8">
                    <!--<select name="businessType" class="form-control m-b" th:with="type=${@dict.getType('cat_app_client_type')}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{businessType}"></option>
                    </select>-->
                    <select name="businessType" class="form-control m-b">
                        <option value="1" th:field="*{businessType}">动态</option>
                        <option value="2" th:field="*{businessType}">文章</option>
                        <option value="3" th:field="*{businessType}">评论</option>
                        <option value="4" th:field="*{businessType}">宠物</option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">类型：</label>
                <div class="col-sm-8">
                    <input name="type" th:field="*{type}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">描述：</label>
                <div class="col-sm-8">
                    <textarea name="depict" class="form-control">[[*{depict}]]</textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">手机号：</label>
                <div class="col-sm-8">
                    <input name="phone" th:field="*{phone}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">图片集合：</label>
                <div class="col-sm-8">
                    <textarea name="imgs" class="form-control">[[*{imgs}]]</textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" th:value="${#dates.format(complainInfo.createDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/complainInfo";
        $("#form-complainInfo-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-complainInfo-edit').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
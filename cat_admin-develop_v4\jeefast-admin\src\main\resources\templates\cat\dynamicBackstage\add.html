<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增动态内容')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: bootstrap-fileinput-css" />
</head>
<body class="white-bg">

    <div class="tabs-container">
        <ul class="nav nav-tabs">
            <li class="active"><a data-toggle="tab" href="#tab-1" aria-expanded="true">基础信息</a>
            </li>
            <li id="media" class=""><a data-toggle="tab" href="#tab-2" aria-expanded="false">媒体信息</a>
            </li>
        </ul>
        <div class="tab-content">
            <div id="tab-1" class="tab-pane active">
                <!--基础信息-->
                <form class="form-horizontal m" id="form-dynamicBackstage-add">
                    <!--<div class="form-group">
                        <label class="col-sm-3 control-label">所属宠物ID：</label>
                        <div class="col-sm-8">
                            <input name="petId" class="form-control" type="text">
                        </div>
                    </div>-->
                    <div class="form-group">
                        <label class="col-sm-3 control-label">文章类型：</label>
                        <div class="col-sm-8">
                            <div class="col-sm-8">
                                <select name="articleType" class="form-control m-b" th:with="type=${@dict.getType('article_type')}" required>
                                    <option th:each="dict: ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">动态类型：</label>
                        <div class="col-sm-8">
                            <select id="type" name="type" class="form-control m-b" th:with="type=${@dict.getType('cat_dynamic_type')}" required>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">动态分类：</label>
                        <div class="col-sm-8">
                            <select id="dynamicCategoryId" name="dynamicCategoryId" class="form-control m-b">
                                <option value="">请选择动态分类</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">用户ID：</label>
                        <div class="col-sm-8">
                            <input name="userId" class="form-control" type="text" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">标题：</label>
                        <div class="col-sm-8">
                            <input name="title" class="form-control" type="text" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">内容：</label>
                        <div class="row form-body form-horizontal m-t">
                            <div class="col-sm-8">
                                <script id="editor" name="content" type="text/plain" style="width:750px;height:750px;"></script>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">所在位置：</label>
                        <div class="col-sm-8">
                            <input name="addr" class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">经度：</label>
                        <div class="col-sm-8">
                            <input name="longitude" class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">纬度：</label>
                        <div class="col-sm-8">
                            <input name="latitude" class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">点赞数：</label>
                        <div class="col-sm-8">
                            <input name="praiseCount" class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">评论数：</label>
                        <div class="col-sm-8">
                            <input name="commentCount" class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">动态来源：</label>
                        <div class="col-sm-8">
                            <select name="source" class="form-control m-b" th:with="type=${@dict.getType('cat_dynamic_source')}" required>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">权重：</label>
                        <div class="col-sm-8">
                            <input name="weight" class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">权重系数：</label>
                        <div class="col-sm-8">
                            <input name="scale" class="form-control" type="text">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">发布时间：</label>
                        <div class="col-sm-8">
                            <div class="input-group date">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                <input name="createDate" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">直播时间：</label>
                        <div class="col-sm-8">
                            <div class="input-group date">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                <input name="liveTime" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">审核状态：</label>
                        <div class="col-sm-8">
                            <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                                <input type="radio" th:id="${'isCheck_' + dict.dictCode}" name="isCheck" th:value="${dict.dictValue}" required>
                                <label th:for="${'isCheck_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">添加链接：</label>
                        <div class="col-sm-8" style="display: flex;">
                            <select name="thirdType" class="form-control m-b" style="width: 100px;" th:with="type=${@dict.getType('cat_link_type')}" required>
                                <option th:each="dict: ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                            <input name="thirdUrl" class="form-control" placeholder="输入链接" type="text">
                        </div>
                    </div>
                </form>
            </div>
            <div id="tab-2" class="tab-pane">
                <div class="form-group">
                    <div class="file-loading">
                        <input id="fileinput-demo-1" type="file" multiple>
                    </div>
                </div>
            </div>
        </div>


    </div>


    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: bootstrap-fileinput-js" />
    <script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.all.min.js"> </script>
    <script type="text/javascript">
        var ue = UE.getEditor('editor');
        //动态媒体url列表
        var dynamicMediaList;

        // 动态分类
        var catApi = ctx + "cat/category/list";
        var categoryList = [];

        // 加载动态分类数据
        function loadCategoryData() {
            $.ajax({
                url: catApi + "?pageNo=1&pageSize=999",
                type: "post",
                dataType: "json",
                success: function(result) {
                    if (result.code == 0) {
                        categoryList = result.rows || [];
                        // 清空下拉框
                        $("#dynamicCategoryId").empty();
                        $("#dynamicCategoryId").append('<option value="" >请选择动态分类</option>');
                        // 填充下拉框选项
                        $.each(categoryList, function(index, item) {
                            $("#dynamicCategoryId").append('<option value="' + item.id + '">' + item.title + '</option>');
                        });
                    } else {
                        $.modal.alertError("获取动态分类失败：" + result.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("获取动态分类失败，请检查网络连接");
                }
            });
        }

        var prefix = ctx + "cat/dynamicBackstage"
        $("#form-dynamicBackstage-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                console.log("submit提交的dynamicMediaList",dynamicMediaList)
                //动态类型（1：文字、2：图文、3：视频）
                var type = $("select[name='type']").val();
                if ((type==2 || type==4) && (!dynamicMediaList || dynamicMediaList.size<=0)){
                    alert("类型图片 请先上传【图片】");
                    return
                }
                if (type==3 && (!dynamicMediaList ||dynamicMediaList.size<=0)){
                    alert("类型视频 请先上传【视频】");
                    return
                }else if (type==3 && dynamicMediaList.size>1){
                    alert("类型视频 只允许上传一个【视频】");
                    return
                }

                //var form = $('#form-dynamicBackstage-add')[0];
                //var formdata = new FormData(form);
                var obj = $('#form-dynamicBackstage-add').serializeObject();
                obj.dynamicMediaList=dynamicMediaList;
                //formdata.append("dynamicMediaList[]",dynamicMediaList);
                $.ajax({
                    url: prefix + "/add",
                    data: JSON.stringify(obj),
                    type: "post",
                    dataType: "json",
                    contentType: "application/json;charset=utf-8",
                    success: function(result) {
                        $.operate.successCallback(result);
                    }
                })
                //$.operate.save(prefix + "/add", obj);
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd hh:ii:ss",
            autoclose: true
        });

        $("input[name='liveTime']").datetimepicker({
            format: "yyyy-mm-dd hh:ii:ss",
            autoclose: true
        });

        /*页面初始化*/
        $(document).ready(function () {
            // 加载动态分类数据
            loadCategoryData();

            $("#fileinput-demo-1").fileinput({
                'theme': 'explorer-fas',
                'uploadUrl': '/common/uploadMany',
                dropZoneEnabled:true,
                uploadAsync:false,//false 同步上传，后台用数组接收，true 异步上传，每次上传一个file,会调用多次接口
                /*overwriteInitial: false,
                initialPreviewAsData: true,
                initialPreview: [
                    "/img/profile.jpg"
                ]*/
                uploadExtraData:function(){//向后台传递参数
                    var data={
                        fileType:$("select[name='type']").val()
                    }
                    console.info("type", $("select[name='type']").val())
                    return data;
                },
            });

            $("#fileinput-demo-1").on("filebatchselected", function(event, files) {
                console.info("选择文件后处理事件")
            });
            $("#fileinput-demo-1").on("fileuploaded", function(event, files) {
                // debugger
                console.info("上传成功后处理方法",dynamicMediaList)
            });
            $('#fileinput-demo-1').on('filebatchuploadsuccess', function(event, data, previewId, index) {
                if(dynamicMediaList!=null && dynamicMediaList.size>0){
                    dynamicMediaList = dynamicMediaList.push(data.response.fileUrlList);
                }else{
                    dynamicMediaList = data.response.fileUrlList;
                }
                if (dynamicMediaList == undefined) {
                    alert("请检查动态类型，删除图片后，并且重新上传");
                }
                console.info("批量上传成功结果处理",dynamicMediaList)
            });
            $('#fileinput-demo-1').on('filebatchuploaderror', function(event, data, msg) {
                // debugger
                console.info("批量上传错误结果处理")
            });
        });



        /**
         * 将Form的数据转化成Javascript的Json对象
         */
        $.fn.serializeObject = function(){
            var o = {};
            var a = this.serializeArray();
            $.each(a, function() {
                if (o[this.name] !== undefined) {
                    if (!o[this.name].push) {
                        o[this.name] = [o[this.name]];
                    }
                    o[this.name].push(this.value || '');
                } else {
                    o[this.name] = this.value || '';
                }
            });
            return o;
        }
        $('#type').change(function () {
            // 当输入框内容修改时执行这个函数
            if ($('#type').val() === '1'){
                $('#media').hide();
            }else {
                $('#media').show()
            }
        });


    </script>
</body>
</html>
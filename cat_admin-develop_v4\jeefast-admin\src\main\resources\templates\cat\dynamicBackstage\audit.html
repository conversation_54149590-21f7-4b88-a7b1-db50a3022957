<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('动态内容安全审核')" />
</head>
<style>
    #bootstrap-table tbody img{
        max-width: 100%;
    }
    #bootstrap-table td p {
        word-break: break-all;
    }

    .layui-layer-page {
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
    }

    .layui-layer-page #div_img  img {
        width: 500px !important;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse" style="display: none;">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li style="display: none;">
                            <p>动态内容id：</p>
                            <input type="hidden" name="dynamicId"/>
                        </li>
                        <li>
                            <p>类型</p>
                            <select name="type" th:with="type=${@dict.getType('cat_dynamic_security')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('cat:security:edit')}]];
    var removeFlag = [[${@permission.hasPermi('cat:security:remove')}]];
    var typeDatas = [[${@dict.getType('cat_dynamic_security')}]];
    var statusDatas = [[${@dict.getType('cat_dynamic_security_status')}]];
    var suggestionDatas = [[${@dict.getType('cat_dynamic_security_suggestion')}]];
    var labelsDatas = [[${@dict.getType('cat_dynamic_security_label')}]];
    var prefix = ctx + "cat/security";

    $(function() {
        const urlParams = new URLSearchParams(window.location.search);
        const dynamicId = urlParams.get('dynamicId');
        $('input[name="dynamicId"]').val(dynamicId)

        var options = {
            url: prefix + "/list",
            modalName: "动态内容安全审核",
            columns: [
                {
                    field : 'id',
                    title : '主键id',
                    visible: false
                },
                {
                    field : 'dynamicId',
                    title : '动态内容id',
                    visible: false
                },
                {
                    field : 'type',
                    title : '类型',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(typeDatas, value);
                    }
                },
                {
                    field : 'taskId',
                    title : '任务taskId',
                    visible: false
                },
                {
                    field : 'relateId',
                    title : '动态内容媒体id',
                    visible: false
                },
                {
                    field : 'relateUrl',
                    title : '图片',
                    formatter: function (value, row, index) {
                        if (row.type == 3) {
                            return $.table.imageView(value);
                        } else if(row.type == 4) {
                            return $.table.imageView(row.content);
                        }
                    }
                },
                {
                    field : 'content',
                    title : '文字内容',
                    width: '240px',
                    formatter: function (value, row, index) {
                        if(row.type == 4) {
                            return
                        } else {
                            return value;
                        }
                    }
                },
                {
                    field : 'bizType',
                    title : '识别策略',
                    visible: false
                },
                {
                    field : 'suggestion',
                    title : '后续操作建议',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(suggestionDatas, value);
                    }
                },
                {
                    field : 'labels',
                    title : '返回检测结果',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(labelsDatas, value);
                    }
                },
                {
                    field : 'keywords',
                    title : '返回检测结果，命中的关键词。',
                    visible: false
                },
                {
                    field : 'score',
                    title : '置信度'
                },
                {
                    field : 'respContent',
                    title : '内容安全检查返回信息',
                    visible: false
                },
                {
                    field : 'auditStatus',
                    title : '最终审核状态',
                    visible: false
                },
                {
                    field : 'auditReason',
                    title : '审核原因',
                    visible: false
                },
                {
                    field : 'sortNo',
                    title : '排序',
                    visible: false
                },
                {
                    field : 'createDate',
                    title : '创建时间',
                    visible: false
                },
                {
                    field : 'status',
                    title : '任务状态',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                },
            ]
        };
        $.table.init(options);
    });
</script>
</body>
</html>
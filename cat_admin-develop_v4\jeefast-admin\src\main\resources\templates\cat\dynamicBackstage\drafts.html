<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('动态内容列表')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div id="search-box" class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li style="display:none">
                            <p>圈子ID：</p>
                            <input type="text" name="params[circleId]" th:value="${circleId}"/>
                        </li>
                        <li style="display:none">
                            <p>话题ID：</p>
                            <input type="text" name="params[topicId]" th:value="${topicId}"/>
                        </li>
                        <li>
                            <p>文章类型：</p>
                            <select name="articleType" th:with="type=${@dict.getType('article_type')}">
                                <option value="">无</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li style="display: none;">
                            <p>是否草稿：</p>
                            <input type="text" name="isCheck" th:value="${isCheck}" />
                        </li>
                        <li style="display: none;">
                            <p>审核状态：</p>
                            <input type="text" name="auditStatus" th:value="${auditStatus}" />
                        </li>
                        <li id="userName">
                            <p>用户昵称：</p>
                            <input type="text" name="userName"/>
                        </li>
                        <li style="display: none;">
                            <p>不需要置顶：</p>
                            <input type="text" name="needQueryTop" value="0" />
                        </li>
                        <li>
                            <p>标题：</p>
                            <input type="text" name="title"/>
                        </li>
                        <li>
                            <p>内容：</p>
                            <input type="text" name="content"/>
                        </li>
                        <li>
                            <p>手机号：</p>
                            <input type="text" name="mobile"/>
                        </li>
                        <li style="display: none;">
                            <p>用户：</p>
                            <input type="text" name="sysUserId" th:value="${isCheck} != 1 ? ${userId} : ''"/>
                        </li>
                        <li>
                            <p>动态类型：</p>
                            <select name="type" th:with="type=${@dict.getType('cat_dynamic_type')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <p>动态来源：</p>
                            <select name="source" th:with="type=${@dict.getType('cat_dynamic_source')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <p>动态分类：</p>
                            <select id="dynamicCategoryIdSearch" name="dynamicCategoryId">
                                <option value="">所有</option>
                            </select>
                        </li>
                        <li class="select-time" style="display: none;">
                            <p>发布时间：</p>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
        <div class="modal fade" id="classificationModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
                        <h4 class="modal-title" id="myModalLabel">
                            批量修改动态分类
                        </h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal m" id="form-classification-edit">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">动态分类：</label>
                                <div class="col-sm-8">
                                    <input id="dynamicIds" name="dynamicIds" type="hidden" />
                                    <select id="dynamicCategoryId" name="dynamicCategoryId" class="form-control m-b" required>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <a class="btn btn-default" data-dismiss="modal">关闭</a>
                        <a class="btn btn-success" onclick="submitCategory()">提交</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <style>
        .pinned-row {
            background-color: #ffff99; /* 示例颜色 */
        }
    </style>
    <th:block th:include="include :: footer"/>
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:dynamicBackstage:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:dynamicBackstage:remove')}]];
        var typeDatas = [[${@dict.getType('cat_dynamic_type')}]];
        var sourceDatas = [[${@dict.getType('cat_dynamic_source')}]];
        var isCheckDatas = [[${@dict.getType('cat_recommend')}]];
        var articleTypeDatas = [[${@dict.getType('article_type')}]];
        var auditStatusDatas = [[${@dict.getType('cat_dynamic_audit_status')}]];
        var prefix = ctx + "cat/dynamicBackstage";
        // 动态分类
        var catApi = ctx + "cat/category/list";
        var categoryList = [];
        // 加载动态分类数据
        function loadCategoryData() {
            $.ajax({
                url: catApi + "?pageNo=1&pageSize=999",
                type: "post",
                dataType: "json",
                success: function(result) {
                    if (result.code == 0) {
                        categoryList = result.rows || [];
                        // 清空下拉框
                        $("#dynamicCategoryIdSearch").empty();
                        // 填充下拉框选项
                        $("#dynamicCategoryIdSearch").append('<option value="" >所有</option>');
                        $.each(categoryList, function(index, item) {
                            $("#dynamicCategoryIdSearch").append('<option value="' + item.id + '" >' + item.title + '</option>');
                        });
                    }
                }
            });
        }
        $(function () {
            // 判断是否为草稿
            if($("input[name='isCheck']").val() == '0'){
                // 判断是否是admin角色
                const roles = [[${roles}]]
                let isAdmin = false;
                roles.forEach(role => {
                    if(role.roleKey == "admin"){
                        isAdmin = true;
                    }
                })
                // $("input[name='userId']").val([[${userId}]]);
                // $("#search-box").hide()
            }else {
                $("input[name='sysUserId']").val();
                $("#search-box").show()
            }
            // 加载动态分类数据
            loadCategoryData();

            var options = {
                pinned: function () {
                    $.operate.post($.table._option.pinnedUrl + $.table.selectColumns($.table._option.uniqueId), undefined, () => $.modal.alertSuccess('置顶成功！'))
                },
                unPinned: function () {
                    $.operate.post($.table._option.unPinnedUrl + $.table.selectColumns($.table._option.uniqueId), undefined, () => $.modal.alertSuccess('置顶成功！'))
                },
                rowStyle: function (row, index) {
                    if (row.isPinned) {
                        return {
                            classes: 'pinned-row'
                        };
                    }
                    return {};
                },
                url: prefix + "/list",
                createUrl: prefix + "/add",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                pinnedUrl: prefix + "/pinned/",
                unPinnedUrl: prefix + "/unPinned/",
                uniqueId: "dynamicId",
                sortName: "createDate",
                sortOrder: "desc",
                modalName: "动态内容",
                columns: [{
                    checkbox: true
                },
                    {
                        field : 'dynamicId',
                        title : 'id',
                        visible: false
                    },
                    {
                        field: 'articleType',
                        title: '类别',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(articleTypeDatas, value);
                        },
                        visible: false
                    },
                    {
                        field: 'userId',
                        title: '用户id',
                        visible: false
                    },
                    {
                        field: 'userName',
                        title: '用户名'
                    },
                    {
                        field: 'title',
                        title: '标题',
                        formatter: function (value, row, index) {
                            return $.table.tooltip(value);
                        }
                    },
                    {
                        field: 'content',
                        title: '内容',
                        formatter: function (value, row, index) {
                            let show_value = value;
                            if (value !== undefined) {
                                show_value = value.replace(/\s*/g, "");
                            }
                            if (show_value !== undefined && value.length > 20) {
                                show_value = show_value.replace(/<[^>]+>/g, "");
                                show_value = show_value.replace(/[\r\n]/g, "");
                                show_value = show_value.replace(/<img[^>]*>/g, "");
                                show_value = show_value.replace(/&nbsp;/g, "").slice(0, 20) + "...";
                            }
                            return $.table.tooltip(show_value);
                        }
                    },
                    {
                        field: 'coverImage',
                        title: '封面图',
                        formatter: function (value, row, index) {
                            if (value) {
                                return $.table.imageView(value);
                            } else {
                                return $.table.imageView('/jeefast.png');
                            }
                        }
                    },
                    {
                        field: 'praiseCount',
                        title: '点赞数',
                        sortable: true,
                        visible: false
                    },
                    {
                        field: 'commentCount',
                        title: '评论数',
                        sortable: true,
                        visible: false
                    },
                    {
                        field: 'type',
                        title: '动态类型',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(typeDatas, value);
                        }
                    },
                    {
                        field : 'dynamicCategoryId',
                        title : '动态分类',
                        formatter: function(value, row, index) {
                            let show_value = "";
                            for (let i = 0; i < categoryList.length; i++) {
                                if (categoryList[i].id == value) {
                                    show_value = categoryList[i].title;
                                    break;
                                }
                            }
                            return show_value;
                        }
                    },
                    {
                        field: 'source',
                        title: '动态来源',
                        visible: false,
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(sourceDatas, value);
                        }
                    },
                    {
                        field: 'createDate',
                        title: '用户上传时间',
                        sortable: true
                    },
                    {
                        field: 'auditStatus',
                        title: '审核状态',
                        visible: [[${isCheck}]] == 1 ? true : false,
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(auditStatusDatas, value);
                        }
                    },
                    {
                        field: 'mediaCount',
                        title: '媒体数',
                        sortable: true,
                        visible: false
                    },
                    {
                        field: 'weight',
                        title: '权重',
                        sortable: true,
                        visible: false
                    },
                    {
                        field: 'isCheck',
                        title: '审核通过',
                        visible: false,
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(isCheckDatas, value);
                        }
                    },
                    {
                        field: 'isDelete',
                        title: '删除',
                        visible: false,
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(isCheckDatas, value);
                        }
                    },
                    {
                        field: 'isPinned',
                        title: '是否置顶',
                        visible: false,
                        formatter: function (value, row, index) {
                            if (value) {
                                return '是';
                            } else {
                                return '否';
                            }
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function (value, row, index) {
                            var actions = [];
                            if([[${isCheck}]] == 0){
                                actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="editNew(\'' + row.dynamicId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                                actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.dynamicId + '\')"><i class="fa fa-remove"></i>删除</a>');
                            } else if([[${isCheck}]] == 1) {
                                if([[${auditStatus}]] == 0) {
                                    actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="auditFn(\'' + row.dynamicId + '\')"><i class="fa fa-edit"></i>查看</a> ');
                                    actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="viewDetail(\'' + row.dynamicId + '\')"><i class="fa fa-edit"></i>详情</a> ');
                                } else if([[${auditStatus}]] == 1) {
                                    actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="auditFn(\'' + row.dynamicId + '\')"><i class="fa fa-edit"></i>查看</a> ');
                                    actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="viewDetail(\'' + row.dynamicId + '\')"><i class="fa fa-edit"></i>详情</a> ');
                                    actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.dynamicId + '\')"><i class="fa fa-remove"></i>删除</a>');
                                } else if([[${auditStatus}]] == 3) {
                                    actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="viewDetail(\'' + row.dynamicId + '\')"><i class="fa fa-edit"></i>详情</a> ');
                                    actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="auditFn(\'' + row.dynamicId + '\')"><i class="fa fa-edit"></i>审核</a> ');
                                    actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.dynamicId + '\')"><i class="fa fa-remove"></i>删除</a>');
                                }
                            }
                            return actions.join('');
                        }
                    }]
            };
            $.table.init(options);
        });

        // 批量修改分类
        function batchModifyClassification(){
            var rows = $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条动态内容");
                return;
            }
            $('#dynamicIds').val(rows.join(","));

            // 清空下拉框
            $("#dynamicCategoryId").empty();
            // 填充下拉框选项
            $("#dynamicCategoryId").append('<option value="" >请选择动态分类</option>');
            $.each(categoryList, function(index, item) {
                $("#dynamicCategoryId").append('<option value="' + item.id + '" >' + item.title + '</option>');
            });
            // 打开选择分类的模态框id=myModal
            $('#classificationModal').modal('show');
        };

        // 确定提交分类更改
        function submitCategory(){
            var dynamicIds = $("#dynamicIds").val();
            var categoryId = $("#dynamicCategoryId").val();
            // 初始化表单验证
            var validator = $("#form-classification-edit").validate({
                focusCleanup: true,
                rules: {
                    dynamicCategoryId: {
                        required: true
                    }
                },
                messages: {
                    dynamicCategoryId: {
                        required: "请选择动态分类"
                    }
                }
            });

            // 执行表单验证
            if(validator.form()) {
                let dynamicIdList = dynamicIds.split(',')
                let formdata = {
                    dynamicIdList: dynamicIdList,
                    categoryId: categoryId
                }
                // 提交AJAX请求
                $.ajax({
                    url: ctx + "cat/dynamicBackstage/batch/edit/category",
                    data: JSON.stringify(formdata),
                    type: "post",
                    contentType: "application/json",
                    dataType: "json",
                    success: function (result) {
                        if (result.code == 0) {
                            $.modal.alertSuccess(result.msg);
                            // 刷新表格
                            $.table.refresh();
                            // 关闭模态框
                            $('#classificationModal').modal('hide');
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    }
                })
            }
        };

        // 编辑- 跳转内容发布页
        function editNew(dynamicId) {
            var url = prefix + "/new/addPage?dynamicId=" + dynamicId + '&from=' + prefix + "/drafts";
            $.modal.openTab("修改动态内容", url);
            switchMenu(prefix + "/new/addPage")
        };

        // 详情
        function viewDetail(dynamicId) {
            let fromUrl = prefix + "/drafts";
            if([[${isCheck}]] == 1){
                if([[${auditStatus}]] == 0) {
                    fromUrl = prefix + "/audit/ai"
                } else if([[${auditStatus}]] == 1) {
                    fromUrl = prefix + "/audit/approved"
                } else if([[${auditStatus}]] == 3) {
                    fromUrl = prefix + "/audit/manual"
                }
            }
            var url = prefix + "/new/addPage?dynamicId=" + dynamicId + '&isView=true' + '&from=' + fromUrl;
            $.modal.openTab("修改动态内容", url);
            switchMenu(prefix + "/new/addPage")
        };

        // 切换菜单选中状态
        function switchMenu(url) {
            var parentDoc = window.parent.document;
            $(parentDoc).find(".menuItem").parent().removeClass("selected");
            $(parentDoc).find(".menuItem").each(function () {
                if ($(this).attr("href") == url) {
                    $(this).parent().addClass("selected");
                }
            });
        };

        // 审核
        function auditFn(dynamicId) {
            var url = prefix + "/audit/audit?dynamicId=" + dynamicId;
            // 根据审核状态配置按钮
            const btnConfig = [[${auditStatus}]] == 3 
            ? ['审核通过', '审核失败']  // 待审核状态显示操作按钮
            : ['关闭'];   
            const title = [[${auditStatus}]] == 3 ? '审核' : '查看';
            const layerOptions = {
                type: 2,
                area: [800 + 'px', ($(window).height() - 50) + 'px'],
                title: title,
                content: url,
                btn: btnConfig,
                shadeClose: true
            };
             // 仅当审核状态为3时添加操作回调
            if ([[${auditStatus}]] == 3) {
                layerOptions.yes = function(index) {
                    showReasonForm('通过', dynamicId, index);
                };
                layerOptions.btn2 = function(index) {
                    showReasonForm('拒绝', dynamicId, index);
                    return false;
                };
            } else {
                layerOptions.yes = function(index) {
                    layer.close(index);
                };
            }

            layer.open(layerOptions);
        };

        // 显示原因输入弹窗
        function showReasonForm(actionType, dynamicId, parentIndex) {
            layer.open({
                type: 1,
                title: '填写' + actionType + '原因',
                area: ['500px', '300px'],
                content: `
                    <div class="modal-body" style="padding:20px;">
                        <form id="reasonForm">
                            <div class="form-group">
                                <label>${actionType}原因：</label>
                                <textarea id="auditReason" class="form-control" rows="5" 
                                    placeholder="请输入${actionType}原因（必填）"></textarea>
                            </div>
                        </form>
                    </div>`,
                btn: ['提交', '取消'],
                yes: function(index) {
                    const reasons = $('#auditReason').val().trim();
                    if (!reasons) {
                        layer.msg('原因不能为空', {icon: 2});
                        return false;
                    }
                    
                    submitAudit(actionType, dynamicId, reasons, () => {
                        layer.close(parentIndex); // 关闭父窗口
                        layer.close(index);       // 关闭原因窗口
                        $.table.refresh();
                    });
                }
            });
        };

        // 提交审核结果
        function submitAudit(actionType, dynamicId, reasons, callback) {
            const apiUrl = actionType === '通过' 
                ? prefix + "/audit/pass"
                : prefix + "/reasonsSub";

            const formdata = new FormData();
            formdata.append("dynamicId", dynamicId);
            formdata.append("reasons", reasons);
            $.modal.loading("正在提交，请稍后...");
            $.ajax({
                url: apiUrl,
                data: formdata,
                processData: false,
                contentType: false,
                type: "post",
                dataType: "json",
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == 0) {
                        $.modal.alertSuccess(result.msg);
                        if (typeof callback === 'function') callback();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                },
                error: function() {
                    $.modal.closeLoading();
                    $.modal.alertError("请求失败，请检查网络");
                }
            });
        };
    </script>
</body>
</html>
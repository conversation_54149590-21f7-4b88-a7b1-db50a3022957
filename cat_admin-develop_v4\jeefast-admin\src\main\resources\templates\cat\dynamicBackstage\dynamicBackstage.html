<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('动态内容列表')"/>
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li style="display:none">
                                <p>圈子ID：</p>
                                <input type="text" name="params[circleId]" th:value="${circleId}"/>
                            </li>
                            <li style="display:none">
                                <p>话题ID：</p>
                                <input type="text" name="params[topicId]" th:value="${topicId}"/>
                            </li>
                            <li>
                                <p>文章类型：</p>
                                <select name="articleType" th:with="type=${@dict.getType('article_type')}">
                                    <option value="">无</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>标题：</p>
                                <input type="text" name="title"/>
                            </li>
                            <li>
                                <p>内容：</p>
                                <input type="text" name="content"/>
                            </li>
                            <li>
                                <p>手机号：</p>
                                <input type="text" name="mobile"/>
                            </li>
                            <li>
                                <p>用户名：</p>
                                <input type="text" name="userName"/>
                            </li>
                            <li>
                                <p>动态类型：</p>
                                <select name="type" th:with="type=${@dict.getType('cat_dynamic_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>动态来源：</p>
                                <select name="source" th:with="type=${@dict.getType('cat_dynamic_source')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>动态分类：</p>
                                <select id="dynamicCategoryIdSearch" name="dynamicCategoryId">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <p>审核状态：</p>
                                <select name="auditStatus" th:with="type=${@dict.getType('cat_dynamic_audit_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li class="select-time">
                                <p>发布时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="openNew()" shiro:hasPermission="cat:dynamicBackstage:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="editNew()"
               shiro:hasPermission="cat:dynamicBackstage:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-primary multiple disabled" onclick="batchModifyClassification()" shiro:hasPermission="cat:dynamicBackstage:batchEditCategory">
                <i class="fa fa-edit"></i> 批量修改分类
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="cat:dynamicBackstage:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()"
               shiro:hasPermission="cat:dynamicBackstage:export">
                <i class="fa fa-download"></i> 导出
            </a>
            <a class="btn btn-primary multiple disabled" onclick="$.table._option.pinned()"
               shiro:hasPermission="cat:dynamicBackstage:edit">
                <i class="fa fa-edit"></i> 置顶
            </a>
            <a class="btn btn-primary multiple disabled" onclick="$.table._option.unPinned()"
               shiro:hasPermission="cat:dynamicBackstage:edit">
                <i class="fa fa-remove"></i> 取消置顶
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
        <div class="modal fade" id="classificationModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
                        <h4 class="modal-title" id="myModalLabel">
                            批量修改动态分类
                        </h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal m" id="form-classification-edit">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">动态分类：</label>
                                <div class="col-sm-8">
                                    <input id="dynamicIds" name="dynamicIds" type="hidden" />
                                    <select id="dynamicCategoryId" name="dynamicCategoryId" class="form-control m-b" required>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <a class="btn btn-default" data-dismiss="modal">关闭</a>
                        <a class="btn btn-success" onclick="submitCategory()">提交</a>
                    </div>
                </div>
            </div>
    </div>
</div>
<style>
    .pinned-row {
        background-color: #ffff99; /* 示例颜色 */
    }
</style>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('cat:dynamicBackstage:edit')}]];
    var removeFlag = [[${@permission.hasPermi('cat:dynamicBackstage:remove')}]];
    var typeDatas = [[${@dict.getType('cat_dynamic_type')}]];
    var sourceDatas = [[${@dict.getType('cat_dynamic_source')}]];
    var isCheckDatas = [[${@dict.getType('cat_recommend')}]];
    var articleTypeDatas = [[${@dict.getType('article_type')}]];
    var auditStatusDatas = [[${@dict.getType('cat_dynamic_audit_status')}]];
    var prefix = ctx + "cat/dynamicBackstage";
    // 动态分类
    var catApi = ctx + "cat/category/list";
    var categoryList = [];
    // 加载动态分类数据
    function loadCategoryData() {
        $.ajax({
            url: catApi + "?pageNo=1&pageSize=999",
            type: "post",
            dataType: "json",
            success: function(result) {
                if (result.code == 0) {
                    categoryList = result.rows || [];
                    // 清空下拉框
                    $("#dynamicCategoryIdSearch").empty();
                    // 填充下拉框选项
                    $("#dynamicCategoryIdSearch").append('<option value="" >所有</option>');
                    $.each(categoryList, function(index, item) {
                        $("#dynamicCategoryIdSearch").append('<option value="' + item.id + '" >' + item.title + '</option>');
                    });
                }
            }
        });
    }
    $(function () {
        // 加载动态分类数据
        loadCategoryData();

        var options = {
            pinned: function () {
                $.operate.post($.table._option.pinnedUrl + $.table.selectColumns($.table._option.uniqueId), undefined, () => $.modal.alertSuccess('置顶成功！'))
            },
            unPinned: function () {
                $.operate.post($.table._option.unPinnedUrl + $.table.selectColumns($.table._option.uniqueId), undefined, () => $.modal.alertSuccess('置顶成功！'))
            },
            rowStyle: function (row, index) {
                if (row.isPinned) {
                    return {
                        classes: 'pinned-row'
                    };
                }
                return {};
            },
            url: prefix + "/list",
            createUrl: prefix + "/new/addPage",
            updateUrl: prefix + "/new/addPage?dynamicId={id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            pinnedUrl: prefix + "/pinned/",
            unPinnedUrl: prefix + "/unPinned/",
            uniqueId: "dynamicId",
            sortName: "createDate",
            sortOrder: "desc",
            modalName: "动态内容",
            columns: [{
                checkbox: true
            },
                {
                    field : 'dynamicId',
                    title : 'id',
                    visible: false
                },
                {
                    field: 'articleType',
                    title: '类别',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(articleTypeDatas, value);
                    },
                    visible: false
                },
                {
                    field: 'userId',
                    title: '用户id',
                    visible: false
                },
                {
                    field: 'userName',
                    title: '用户名'
                },
                {
                    field: 'title',
                    title: '标题',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field: 'content',
                    title: '内容',
                    formatter: function (value, row, index) {
                        let show_value = value;
                        if (value !== undefined) {
                            show_value = value.replace(/\s*/g, "");
                        }
                        if (show_value !== undefined && value.length > 20) {
                            show_value = show_value.replace(/<[^>]+>/g, "");
                            show_value = show_value.replace(/[\r\n]/g, "");
                            show_value = show_value.replace(/<img[^>]*>/g, "");
                            show_value = show_value.replace(/&nbsp;/g, "").slice(0, 20) + "...";
                        }
                        return $.table.tooltip(show_value);
                    }
                },
                {
                    field: 'praiseCount',
                    title: '点赞数',
                    sortable: true
                },
                {
                    field: 'commentCount',
                    title: '评论数',
                    sortable: true
                },
                {
                    field: 'type',
                    title: '动态类型',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(typeDatas, value);
                    }
                },
                {
                    field : 'dynamicCategoryId',
                    title : '动态分类',
                    formatter: function(value, row, index) {
                        let show_value = "";
                        for (let i = 0; i < categoryList.length; i++) {
                            if (categoryList[i].id == value) {
                                show_value = categoryList[i].title;
                                break;
                            }
                        }
                        return show_value;
                    }
                },
                {
                    field: 'source',
                    title: '动态来源',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(sourceDatas, value);
                    }
                },
                {
                    field: 'createDate',
                    title: '发布时间',
                    sortable: true
                },
                {
                    field: 'coverImage',
                    title: '封面图',
                    formatter: function (value, row, index) {
                        // 图片预览（注意：如存储在本地直接获取数据库路径，如有配置context-path需要使用ctx+路径）
                        // 如：/profile/upload/2019/08/08/3b7a839aced67397bac694d77611ce72.png
                        if (value) {
                            return $.table.imageView(value);
                        } else {
                            return $.table.imageView('/jeefast.png');
                        }
                    }
                },
                {
                    field: 'mediaCount',
                    title: '媒体数',
                    sortable: true
                },
                {
                    field: 'weight',
                    title: '权重',
                    sortable: true
                },
                {
                    field: 'isCheck',
                    title: '是否草稿',
                    formatter: function (value, row, index) {
                        if (value == 0) {
                            return '是';
                        } else {
                            return '否';
                        }
                    }
                },
                {
                    field: 'auditStatus',
                    title: '审核状态',
                    formatter: function (value, row, index) {
                        if(row.isCheck == 0){
                            return row.auditStatus == 2 ? '审核失败' : '待提交'
                        }
                        return $.table.selectDictLabel(auditStatusDatas, value);
                    }
                },
                {
                    field: 'isDelete',
                    title: '删除',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(isCheckDatas, value);
                    }
                },
                {
                    field: 'isPinned',
                    title: '是否置顶',
                    formatter: function (value, row, index) {
                        if (value) {
                            return '是';
                        } else {
                            return '否';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    width: '170px',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="auditFn(\'' + row.dynamicId + '\')"><i class="fa fa-edit"></i>查看</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="editNew(\'' + row.dynamicId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.dynamicId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 批量修改分类
        function batchModifyClassification(){
            var rows = $.table.selectFirstColumns();
            if (rows.length == 0) {
       			$.modal.alertWarning("请至少选择一条动态内容");
       			return;
       		}
            $('#dynamicIds').val(rows.join(","));

            // 清空下拉框
            $("#dynamicCategoryId").empty();
            // 填充下拉框选项
            $("#dynamicCategoryId").append('<option value="" >请选择动态分类</option>');
            $.each(categoryList, function(index, item) {
                $("#dynamicCategoryId").append('<option value="' + item.id + '" >' + item.title + '</option>');
            });
            // 打开选择分类的模态框id=myModal
            $('#classificationModal').modal('show');
        };

        // 确定提交分类更改
        function submitCategory(){
            var dynamicIds = $("#dynamicIds").val();
            var categoryId = $("#dynamicCategoryId").val();
            // 初始化表单验证
            var validator = $("#form-classification-edit").validate({
                focusCleanup: true,
                rules: {
                    dynamicCategoryId: {
                        required: true
                    }
                },
                messages: {
                    dynamicCategoryId: {
                        required: "请选择动态分类"
                    }
                }
            });

            // 执行表单验证
            if(validator.form()) {
                let dynamicIdList = dynamicIds.split(',')
                let formdata = {
                    dynamicIdList: dynamicIdList,
                    categoryId: categoryId
                }
                // 提交AJAX请求
                $.ajax({
                    url: ctx + "cat/dynamicBackstage/batch/edit/category",
                    data: JSON.stringify(formdata),
                    type: "post",
                    contentType: "application/json",
                    dataType: "json",
                    success: function (result) {
                        if (result.code == 0) {
                            $.modal.alertSuccess(result.msg);
                            // 刷新表格
                            $.table.refresh();
                            // 关闭模态框
                            $('#classificationModal').modal('hide');
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    }
                })
            }
        };

        // 审核
        function auditFn(dynamicId) {
            var url = prefix + "/audit/audit?dynamicId=" + dynamicId;
            const layerOptions = {
                type: 2,
                area: [800 + 'px', ($(window).height() - 50) + 'px'],
                title: '查看审核',
                content: url,
                btn: ['关闭'],
                shadeClose: true
            };
            layerOptions.yes = function(index) {
                layer.close(index);
            };
            layer.open(layerOptions);
        };

        // 新增
        function openNew() {
            var url = prefix + "/new/addPage";
            $.modal.openTab("修改动态内容", url);
            switchMenu(prefix + "/new/addPage")
        };

        // 编辑- 跳转内容发布页
        function editNew(dynamicId) {
            var url = prefix + "/new/addPage?dynamicId=" + dynamicId + '&from=' + prefix;
            if(!dynamicId){
                url = $.operate.editUrl(dynamicId) + '&from=' + prefix;
            }
            $.modal.openTab("修改动态内容", url);
            switchMenu(prefix + "/new/addPage");
        };

        // 切换菜单选中状态
        function switchMenu(url) {
            var parentDoc = window.parent.document;
            $(parentDoc).find(".menuItem").parent().removeClass("selected");
            $(parentDoc).find(".menuItem").each(function () {
                if ($(this).attr("href") == url) {
                    $(this).parent().addClass("selected");
                }
            });
        };
</script>
</body>
</html>
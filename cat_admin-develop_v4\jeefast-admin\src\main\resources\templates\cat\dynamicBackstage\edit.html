<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改动态内容')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-dynamicBackstage-edit" th:object="${dynamicInfoBackstage}">
            <input name="dynamicId" th:field="*{dynamicId}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label">文章类型：</label>
                <div class="col-sm-8">
                    <select name="articleType" class="form-control m-b" th:with="type=${@dict.getType('article_type')}" required>
                        <option th:each="dict: ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{articleType}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">所属宠物ID：</label>
                <div class="col-sm-8">
                    <input name="petId" th:field="*{petId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">用户ID：</label>
                <div class="col-sm-8">
                    <input name="userId" th:field="*{userId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">内容：</label>
                <div class="row form-body form-horizontal m-t">
                    <div class="col-sm-8">
                        <script  id="editor" name="content" type="text/plain" style="width:750px;height:750px;" th:utext="*{content}"></script>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">所在位置：</label>
                <div class="col-sm-8">
                    <input name="addr" th:field="*{addr}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">经度：</label>
                <div class="col-sm-8">
                    <input name="longitude" th:field="*{longitude}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">纬度：</label>
                <div class="col-sm-8">
                    <input name="latitude" th:field="*{latitude}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">点赞数：</label>
                <div class="col-sm-8">
                    <input name="praiseCount" th:field="*{praiseCount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">评论数：</label>
                <div class="col-sm-8">
                    <input name="commentCount" th:field="*{commentCount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">动态类型：</label>
                <div class="col-sm-8">
                    <select name="type" class="form-control m-b" th:with="type=${@dict.getType('cat_dynamic_type')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{type}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">动态来源：</label>
                <div class="col-sm-8">
                    <input name="source" th:field="*{source}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发布时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" th:value="${#dates.format(dynamicInfoBackstage.createDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">封面图：</label>
                <div class="col-sm-8">
                    <input name="coverImage" th:field="*{coverImage}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">资源高度px：</label>
                <div class="col-sm-8">
                    <input name="height" th:field="*{height}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">资源宽度px：</label>
                <div class="col-sm-8">
                    <input name="width" th:field="*{width}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">媒体数：</label>
                <div class="col-sm-8">
                    <input name="mediaCount" th:field="*{mediaCount}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">权重：</label>
                <div class="col-sm-8">
                    <input name="weight" th:field="*{weight}" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.all.min.js"> </script>
    <script type="text/javascript">
        var ue = UE.getEditor('editor');
        var prefix = ctx + "cat/dynamicBackstage";
        $("#form-dynamicBackstage-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            console.log('------->>>>>')
            if ($.validate.form()) {
                console.log('------->>>>>')
                $.operate.save(prefix + "/edit", $('#form-dynamicBackstage-edit').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
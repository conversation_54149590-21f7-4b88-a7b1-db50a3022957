<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('编辑动态内容')"/>
    <th:block th:include="include :: datetimepicker-css"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<body class="white-bg">

<div class="tabs-container">
    <ul class="nav nav-tabs">
        <li class="active"><a data-toggle="tab" href="#tab-1" aria-expanded="true">基础信息</a>
        </li>
        <li class="" id="media"><a data-toggle="tab" href="#tab-2" aria-expanded="false">媒体信息</a>
        </li>
        <li class=""><a data-toggle="tab" href="#tab-3" aria-expanded="false">驳回操作</a></li>
        <li class=""><a data-toggle="tab" href="#tab-4" aria-expanded="false">圈子信息</a></li>
    </ul>
    <div class="tab-content">
        <div id="tab-1" class="tab-pane active">
            <!--基础信息-->
            <form class="form-horizontal m" id="form-dynamicBackstage-edit" th:object="${dynamicInfoBackstage}">
                <input name="dynamicId" th:field="*{dynamicId}" type="hidden">
                <!--<div class="form-group">
                    <label class="col-sm-3 control-label">所属宠物ID：</label>
                    <div class="col-sm-8">
                        <input name="petId" th:field="*{petId}" class="form-control" type="text">
                    </div>
                </div>-->
                <div class="form-group">
                    <label class="col-sm-3 control-label">文章类型：</label>
                    <div class="col-sm-8">
                        <select name="articleType" class="form-control m-b"
                                th:with="type=${@dict.getType('article_type')}" required>
                            <option th:each="dict: ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                                    th:field="*{articleType}"></option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">动态类型：</label>
                    <div class="col-sm-8">
                        <select id="type" name="type" class="form-control m-b"
                                th:with="type=${@dict.getType('cat_dynamic_type')}" required>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                                    th:field="*{type}"></option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">动态分类：</label>
                    <div class="col-sm-8">
                        <input name="dynamicCategoryIdValue" id="dynamicCategoryIdValue" th:value="*{dynamicCategoryId}" type="hidden">
                        <select name="dynamicCategoryId" th:field="*{dynamicCategoryId}" class="form-control m-b">
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">用户ID：</label>
                    <div class="col-sm-8">
                        <input name="userId" th:field="*{userId}" class="form-control" type="text" required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">标题：</label>
                    <div class="col-sm-8">
                        <input name="title" th:field="*{title}" class="form-control" type="text" required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">内容：</label>
                    <div class="row form-body form-horizontal m-t">
                        <div class="col-sm-8">
                            <script id="editor" name="content" type="text/plain" style="width:750px;height:750px;"
                                    th:utext="*{content}"></script>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">添加链接：</label>
                    <div class="col-sm-8" style="display: flex;">
                        <select name="thirdType" class="form-control" style="width: 100px;" th:with="type=${@dict.getType('cat_link_type')}" required>
                            <option th:each="dict: ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{thirdType}" ></option>
                        </select>
                        <input name="thirdUrl" class="form-control" th:field="*{thirdUrl}" placeholder="输入链接" type="text">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">所在位置：</label>
                    <div class="col-sm-8">
                        <input name="addr" th:field="*{addr}" class="form-control" type="text">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">经度：</label>
                    <div class="col-sm-8">
                        <input name="longitude" th:field="*{longitude}" class="form-control" type="text">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">纬度：</label>
                    <div class="col-sm-8">
                        <input name="latitude" th:field="*{latitude}" class="form-control" type="text">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">点赞数：</label>
                    <div class="col-sm-8">
                        <input name="praiseCount" th:field="*{praiseCount}" class="form-control" type="text">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">评论数：</label>
                    <div class="col-sm-8">
                        <input name="commentCount" th:field="*{commentCount}" class="form-control" type="text">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">动态来源：</label>
                    <div class="col-sm-8">
                        <input name="source" th:field="*{source}" class="form-control" type="text">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">封面图：</label>
                    <div class="col-sm-8">
                        <input name="coverImage" th:field="*{coverImage}" class="form-control" type="text" required>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">资源高度px：</label>
                    <div class="col-sm-8">
                        <input name="height" th:field="*{height}" class="form-control" type="text">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">资源宽度px：</label>
                    <div class="col-sm-8">
                        <input name="width" th:field="*{width}" class="form-control" type="text">
                    </div>
                </div>
                <!--<div class="form-group">
                    <label class="col-sm-3 control-label">媒体数：</label>
                    <div class="col-sm-8">
                        <input name="mediaCount" th:field="*{mediaCount}" class="form-control" type="text" required>
                    </div>
                </div>-->
                <div class="form-group">
                    <label class="col-sm-3 control-label">权重：</label>
                    <div class="col-sm-8">
                        <input name="weight" th:field="*{weight}" class="form-control" type="text">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">权重系数：</label>
                    <div class="col-sm-8">
                        <input name="scale" th:field="*{scale}" class="form-control" type="text">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">直播时间：</label>
                    <div>
                        <div class="col-sm-8">
                            <div class="input-group date">
                                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                <input name="liveTime"   th:value="${#dates.format(dynamicInfoBackstage.liveTime, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss"
                                       type="text">
                            </div>
                        </div>
                    </div>
                </div>
                <!--<div class="form-group">
                    <label class="col-sm-3 control-label">发布时间：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                            <input name="createDate" th:value="${#dates.format(dynamicInfoBackstage.createDate, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text">
                        </div>
                    </div>
                </div>-->
                <div class="form-group">
                    <label class="col-sm-3 control-label">审核状态：</label>
                    <div class="col-sm-8">
                        <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                            <input type="radio" th:id="${'isCheck_' + dict.dictCode}" name="isCheck"
                                   th:value="${dict.dictValue}" th:field="*{isCheck}" required>
                            <label th:for="${'isCheck_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div id="tab-2" class="tab-pane">
            <div class="form-group">
                <div class="file-loading" th:if="${dynamicInfoBackstage.type} eq 2 or ${dynamicInfoBackstage.type} eq 4">
                    <input id="fileinput-demo-1" type="file" multiple>
                </div>
                <video id="video" th:if="${dynamicInfoBackstage.type} eq 3" th:src="${dyMediaStrList}"
                       style="width:60% !important" controls="controls"></video>
            </div>
        </div>
        <div id="tab-3" class="tab-pane">
            <div class="form-group">
                <form class="form-horizontal m" id="form-dynamicBackstage-reasons">
                    <input name="dynamicId" th:field="*{dynamicInfoBackstage.dynamicId}" type="hidden">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">驳回原因：</label>
                        <div class="col-sm-8">
                            <textarea name="reasons" class="form-control"
                                      required>[[*{dynamicInfoBackstage.reasons}]]</textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label"></label>
                        <div class="col-sm-8">
                            <button type="button" class="btn btn-w-m btn-danger" onclick="reasonsSub()">确定驳回
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <!--<div id="tab-4" class="tab-pane">
            <div class="form-group">
                <form class="form-horizontal m" id="form-dynamicBackstage-bindCircle">
                    <input name="dynamicId" th:field="*{dynamicInfoBackstage.dynamicId}" type="hidden">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">已绑圈子名：</label>
                        <div class="col-sm-8">
                            <input id="circleId" name="circleId" th:value="${circleId}" class="form-control" type="hidden">
                            <input id="circleName" name="circleName" th:value="${circleName}" class="form-control" type="text" onclick="selectCircle()" readonly>
                            <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 点击可更换</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label"></label>
                        <div class="col-sm-8">
                            <button type="button" class="btn btn-w-m btn-danger" onclick="bindCircle()">确定绑定</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>-->
    </div>


</div>

<script th:src="@{/ruoyi/js/test.js}" id="testScript" th:data="${dyMediaStrList}"></script>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: datetimepicker-js"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.all.min.js"></script>
<script type="text/javascript">
    var ue = UE.getEditor('editor');

    // 动态分类
    var catApi = ctx + "cat/category/list";
    var categoryList = [];

    // 加载动态分类数据
    function loadCategoryData() {
        $.ajax({
            url: catApi + "?pageNo=1&pageSize=999",
            type: "post",
            dataType: "json",
            success: function(result) {
                if (result.code == 0) {
                    let dynamicCategoryIdValue = $('input[name="dynamicCategoryIdValue"]').val();
                    categoryList = result.rows || [];
                    // 清空下拉框
                    $("#dynamicCategoryId").empty();
                    // 填充下拉框选项
                    // if(!dynamicCategoryIdValue){
                        $("#dynamicCategoryId").append('<option value="" >请选择动态分类</option>');
                    // }
                    $.each(categoryList, function(index, item) {
                        if (item.id == dynamicCategoryIdValue) {
                            $("#dynamicCategoryId").append('<option value="' + item.id + '" selected>' + item.title + '</option>');
                        } else {
                            $("#dynamicCategoryId").append('<option value="' + item.id + '" >' + item.title + '</option>');
                        }
                    });
                } else {
                    $.modal.alertError("获取动态分类失败：" + result.msg);
                }
            },
            error: function() {
                $.modal.alertError("获取动态分类失败，请检查网络连接");
            }
        });
    }

    //动态媒体url列表
    var dynamicMediaList = document.getElementById('testScript').getAttribute('data');
    dynamicMediaList = JSON.parse(dynamicMediaList);
    var prefix = ctx + "cat/dynamicBackstage"
    $("#form-dynamicBackstage-edit").validate({
        focusCleanup: true
    });
    UE.Editor.prototype._bkGetActionUrl = UE.Editor.prototype.getActionUrl;
    UE.Editor.prototype.getActionUrl = function (action) {
        if (action === 'listimage') {
            let protocol = window.location.protocol + '//'
            let hostName = window.location.hostname
            let port = window.location.port ? ':' + window.location.port : ''
            let baseUrl = protocol + hostName + port;
            console.log(baseUrl + "/ueditor/config?action=listimage&start=0&size=20&noCache=" + Date.now() + "&dynamicId=" + document.getElementsByName("dynamicId")[0].value)
            return baseUrl + "/ueditor/config?action=listimage&dynamicId=" + document.getElementsByName("dynamicId")[0].value
        } else {
            return this._bkGetActionUrl.call(this, action);
        }
    }

    var mediaInfoList = [];

    function submitHandler() {
        console.log("=======dynamicMediaList=====", dynamicMediaList)
        if ($.validate.form()) {
            //动态类型（1：文字、2：图文、3：视频）
            var type = $("select[name='type']").val();
            if ((type == 2 || type == 4) && ($.common.isEmpty(dynamicMediaList) || dynamicMediaList.size <= 0)) {
                alert("类型图片 请先上传【图片】");
                return "fail";
            }
            if (type == 3 && ($.common.isEmpty(dynamicMediaList) || dynamicMediaList.size <= 0)) {
                alert("类型视频 请先上传【视频】");
                return "fail";
            } else if (type == 3 && ($.common.isEmpty(dynamicMediaList) || dynamicMediaList.size > 1)) {
                alert("类型视频 只允许上传一个【视频】");
                return "fail";
            }
            var form = $('#form-dynamicBackstage-edit')[0];
            var formdata = new FormData(form);
            formdata.append("dynamicMediaList", dynamicMediaList);
            formdata.append("mediaInfoList", JSON.stringify(mediaInfoList));
            $.ajax({
                url: prefix + "/edit",
                data: formdata,
                type: "post",
                processData: false,
                contentType: false,
                success: function (result) {
                    $.operate.successCallback(result);
                }
            })
            // $.operate.save(prefix + "/add", $('#form-dynamicBackstage-add').serialize());
        } else {
            console.log('校验未通过。。。。')
            alert("请检查必填项！");
            return "fail";
        }
    }

    $("input[name='createDate']").datetimepicker({
        format: "yyyy-mm-dd hh:ii:ss",
        autoclose: true
    });

    $("input[name='liveTime']").datetimepicker({
        format: "yyyy-mm-dd hh:ii:ss",
        autoclose: true
    });

    /*页面初始化*/
    $(document).ready(function () {
        // 加载动态分类数据
        loadCategoryData();

        $("#fileinput-demo-1").fileinput({
            'theme': 'explorer-fas',
            'uploadUrl': '/common/uploadMany',
            dropZoneEnabled: false,
            showUpload: false, //是否显示上传按钮
            uploadAsync: false,//false 同步上传，后台用数组接收，true 异步上传，每次上传一个file,会调用多次接口
            // dropZoneEnabled: false,//是否显示拖拽区域
            showRemove: false, //显示移除按钮
            overwriteInitial: false,
            initialPreviewAsData: true,
            initialPreview: dynamicMediaList,
            language: 'zh', //设置语言
            uploadExtraData: function () {//向后台传递参数
                var data = {
                    fileType: $("select[name='type']").val()
                }
                return data;
            },
        });
        $("#fileinput-demo-1").fileinput('_initFileActions');//这行代码就是调用绑定删除事件
        $("#fileinput-demo-1").on("filebatchselected", function (event, files) {
            console.info("选择文件后处理事件")
        });
        $("#fileinput-demo-1").on("fileuploaded", function (event, files) {
            //debugger
            console.info('文件上传后的files:', files)
            if (dynamicMediaList != undefined) {
                console.log('===========', dynamicMediaList)
                dynamicMediaList.push(files.response.fileUrlList[0].url);
                mediaInfoList.push(files.response.fileUrlList[0]);

                // dynamicMediaList.push("http://36.139.207.87:14081/profile/953596468ca43f0f379e2e1f7c000bb2.png");
                // mediaInfoList.push({height:101,size:42780,small:"http://36.139.207.87:14081/profile/55d153fa3acd4f9ebd1291657c3d78da.png",typ: 1,url:"http://36.139.207.87:14081/profile/953596468ca43f0f379e2e1f7c000bb2.png",width:152});

                console.log('dynamicMediaList:', dynamicMediaList)
                console.log("mediaInfoList", mediaInfoList)
            }
            console.info("文件上传成功结果处理", dynamicMediaList)
        });
        $("#fileinput-demo-1").on("fileremoved", function (event, id, index) {
            //debugger
            console.info("删除成功后处理方法", event, id, index);
            dynamicMediaList.splice(index.split("_")[1], 1);
            console.log("删除成功后处理方法：", dynamicMediaList);
        });
        $('#fileinput-demo-1').on('filebatchuploadsuccess', function (event, data, previewId, index) {
            //debugger
            console.log("批量上传dynamicMediaList", dynamicMediaList, "data:", data)
            if (dynamicMediaList != null && dynamicMediaList.length > 0) {
                dynamicMediaList.push(data.response.fileUrlList);
            } else {
                dynamicMediaList = data.response.fileUrlList;
            }
            console.info("批量上传成功结果处理")
        });
        $('#fileinput-demo-1').on('filebatchuploaderror', function (event, data, msg) {
            //debugger
            console.info("批量上传错误结果处理")
        });
    });


    /**
     * 驳回提交
     */
    function reasonsSub() {
        $.ajax({
            url: prefix + "/reasonsSub",
            data: $('#form-dynamicBackstage-reasons').serialize(),
            type: "post",
            processData: false,
            contentType: false,
            contentType: "application/x-www-form-urlencoded; charset=utf-8",
            success: function (result) {
                $.operate.successCallback(result);
            }
        })
    }

    $(function () {
        $("#video").attr("src", dynamicMediaList[0]);
    })

    function doSelectCircle(index, layero) {
        var table = layero.find("iframe")[0].contentWindow.$.table;
        var id = table.selectColumns("id");
        if (id.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (id.length > 1) {
            $.modal.alertWarning("只能选择一条记录");
            return;
        }
        var circleName = table.selectColumns("circleName");
        //var body = layer.getChildFrame('body', index);
        $("#circleId").val(id.join());
        $("#circleName").val(circleName.join());
        layer.close(index);
    }

    function selectCircle() {
        var url = '/cat/circleInfo';
        var options = {
            title: '选择人员信息',
            width: "1000",
            url: url,
            callBack: doSelectCircle
        };
        $.modal.openOptions(options);
    }

    //绑定圈子
    function bindCircle() {
        var circleId = $("#circleId").val();
        if (!circleId) {
            $.modal.alertWarning("请先选择圈子！");
            return;
        }
        $.ajax({
            url: prefix + "/bindCircle",
            data: $('#form-dynamicBackstage-bindCircle').serialize(),
            type: "post",
            processData: false,
            contentType: false,
            contentType: "application/x-www-form-urlencoded; charset=utf-8",
            success: function (result) {
                $.operate.successCallback(result);
            }
        })
    }

    $('#type').change(function () {
        // 当输入框内容修改时执行这个函数
        if ($('#type').val() === '1') {
            $('#media').hide();
        } else {
            $('#media').show()
        }
    });

</script>
</body>
</html>
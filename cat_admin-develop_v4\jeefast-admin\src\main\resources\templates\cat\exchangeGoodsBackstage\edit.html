<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改可兑换商品')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-exchangeGoodsBackstage-edit" th:object="${exchangeGoodsBackstage}">
            <input name="exchangeGoodsId" th:field="*{exchangeGoodsId}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label">商品图片：</label>
                <div class="col-sm-8">
                    <!--<input name="image" th:field="*{image}" class="form-control" type="text">-->
                    <div class="fileinput fileinput-new" data-provides="fileinput">
                        <div class="fileinput-new thumbnail" style="width: 336px; height: 140px;">
                            <img th:src="*{image}">
                        </div>
                        <div class="fileinput-preview fileinput-exists thumbnail" style="max-width: 200px; max-height: 150px;"></div>
                        <div>
                            <span class="btn btn-white btn-file"><span class="fileinput-new">选择图片</span><span class="fileinput-exists">更改</span>
                                <input id="bannerFile" name="file" class="form-control" type="file">
                            </span>
                            <a href="#" class="btn btn-white fileinput-exists" data-dismiss="fileinput">清除</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品名称：</label>
                <div class="col-sm-8">
                    <input name="goodsName" th:field="*{goodsName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品实际价格：</label>
                <div class="col-sm-8">
                    <input name="goodsMoney" th:field="*{goodsMoney}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品兑换条件：</label>
                <div class="col-sm-8">
                    <input name="canNumber" th:field="*{canNumber}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品描述：</label>
                <div class="col-sm-8">
                    <input name="summary" th:field="*{summary}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品详情：</label>
                <div class="col-sm-8">
                    <input name="content" th:field="*{content}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">已兑换人数：</label>
                <div class="col-sm-8">
                    <input name="exchangeCount" th:field="*{exchangeCount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">最大兑换数：</label>
                <div class="col-sm-8">
                    <input name="maxCount" th:field="*{maxCount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" th:value="${#dates.format(exchangeGoodsBackstage.createDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>

            <div class="form-group">    
                <label class="col-sm-3 control-label">排序值：</label>
                <div class="col-sm-8">
                    <input name="sort" th:field="*{sort}" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: jasny-bootstrap-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/exchangeGoodsBackstage";
        $("#form-exchangeGoodsBackstage-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            /*if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-exchangeGoodsBackstage-edit').serialize());
            }*/
            if ($.validate.form()) {
                var form = $('#form-exchangeGoodsBackstage-edit')[0];
                var formdata = new FormData(form);
                $.ajax({
                    url: prefix + "/edit",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    success: function(result) {
                        $.operate.successCallback(result);
                    }
                })
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
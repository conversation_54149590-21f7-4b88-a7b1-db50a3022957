<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('首页菜单列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>菜单名称：</p>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <p>显示状态：</p>
                                <select name="status" th:with="type=${@dict.getType('cat_app_menu_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>业务类型：</p>
                                <select name="businessType" th:with="type=${@dict.getType('cat_app_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:homeMenuBackstage:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:homeMenuBackstage:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:homeMenuBackstage:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:homeMenuBackstage:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:homeMenuBackstage:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:homeMenuBackstage:remove')}]];
        var hrefTypeDatas = [[${@dict.getType('cat_href_type')}]];
        var statusDatas = [[${@dict.getType('cat_app_menu_status')}]];
        var businessTypeDatas = [[${@dict.getType('cat_app_type')}]];
        var prefix = ctx + "cat/homeMenuBackstage";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                sortName: "sortOn",
                sortOrder: "asc",
                modalName: "首页菜单",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : '菜单Id',
                    visible: false
                },
                {
                    field : 'name', 
                    title : '菜单名称'
                },
                {
                    field : 'imageUrl', 
                    title : '图片地址',
                    formatter: function(value, row, index) {
                        // 图片预览（注意：如存储在本地直接获取数据库路径，如有配置context-path需要使用ctx+路径）
                        // 如：/profile/upload/2019/08/08/3b7a839aced67397bac694d77611ce72.png
                        if(value){
                            return $.table.imageView(value);
                        }else {
                            return $.table.imageView('/jeefast.png');
                        }
                    }
                },
                {
                    field : 'hrefType', 
                    title : '链接类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(hrefTypeDatas, value);
                    }
                },
                {
                    field : 'status', 
                    title : '显示状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field : 'sortOn', 
                    title : '排序值',
                    sortable: true
                },
                {
                    field : 'businessType', 
                    title : '业务类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(businessTypeDatas, value);
                    }
                },
                {
                    field : 'createDate', 
                    title : '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
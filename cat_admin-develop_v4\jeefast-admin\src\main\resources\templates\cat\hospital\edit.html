<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改医院')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-hospital-edit" th:object="${hospital}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label">logo：</label>
                <div class="col-sm-8">
                    <!--<input name="image" class="form-control" type="text">-->
                    <div class="fileinput fileinput-new" data-provides="fileinput">
                        <div class="fileinput-new thumbnail" style="width: 150px; height: 150px;">
                            <img />
                        </div>
                        <div class="fileinput-preview fileinput-exists thumbnail" style="max-width: 200px; max-height: 150px;"></div>
                        <div>
                            <span class="btn btn-white btn-file"><span class="fileinput-new">选择图片</span><span class="fileinput-exists">更改</span>
                                <input id="bannerFile" name="file" class="form-control" type="file">
                            </span>
                            <a href="#" class="btn btn-white fileinput-exists" data-dismiss="fileinput">清除</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">医院编码：</label>
                <div class="col-sm-8">
                    <input name="hospitalCode" th:field="*{hospitalCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">医院名称：</label>
                <div class="col-sm-8">
                    <input name="hospitalName" th:field="*{hospitalName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">医院标签：</label>
                <div class="col-sm-8">
                    <select id="hospital_tags" class="form-control select2-multiple" multiple>
                        <option th:each="tag:${hospital_tags}" th:value="${tag.dictValue}" th:text="${tag.dictLabel}" th:selected="${tag.selected}"  th:disabled="${tag.status == '1'}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">地址：</label>
                <div class="col-sm-8">
                    <input name="address" th:field="*{address}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">医院介绍：</label>
                <div class="col-sm-8">
                    <script id="editor" th:utext="${hospital.description}" name="description" type="text/plain" style="width:470px;height:350px;"></script>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" name="status" value="1" th:checked="${hospital.status == 1 ? true : false}">
                        <label th:field="*{status}" th:text="启用"></label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" name="status" value="2" th:checked="${hospital.status == 2 ? true : false}">
                        <label th:field="*{status}" th:text="停止"></label>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: select2-js" />
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: jasny-bootstrap-js" />
    <script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.all.min.js"> </script>
    <!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
    <!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
    <script type="text/javascript" charset="utf-8" src="/ueditor/lang/zh-cn/zh-cn.js"></script>
    <script type="text/javascript">

        //实例化编辑器
        //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
        var ue = UE.getEditor('editor');

        var prefix = ctx + "cat/hospital";
        $("#form-hospital-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            // if ($.validate.form()) {
            //     var data = $("#form-hospital-edit").serializeArray();
            //     var hospitalTags = $.form.selectSelects("hospital_tags");
            //     data.push({"name": "hospitalTags", "value": hospitalTags});
            //     $.operate.save(prefix + "/edit", data);
            // }
            if ($.validate.form()) {
                var form = $('#form-hospital-edit')[0];
                var formdata = new FormData(form);
                var hospitalTags = $.form.selectSelects("hospital_tags");
                formdata.append("hospitalTags", hospitalTags);
                $.ajax({
                    url: prefix + "/edit",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    success: function (result) {
                        $.operate.successCallback(result);
                    }
                })
            }
        }

        $(function() {
            $('#hospital_tags').select2({
                placeholder:"请选择标签",
                allowClear: true
            });
        })
    </script>
</body>
</html>
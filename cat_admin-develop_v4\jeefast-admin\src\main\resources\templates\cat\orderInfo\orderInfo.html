<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>用户id：</p>
                                <input type="text" name="userId"/>
                            </li>
                            <li>
                                <p>订单编号：</p>
                                <input type="text" name="orderNo"/>
                            </li>
                            <li>
                                <p>支付订单号：</p>
                                <input type="text" name="outTradeNo"/>
                            </li>
                            <!--<li>
                                <p>业务类型：</p>
                                <select name="businessType" th:with="type=${@dict.getType('cat_order_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>-->
                            <li>
                                <p>快递单号：</p>
                                <input type="text" name="trackingNo"/>
                            </li>
                            <li>
                                <p>订单状态：</p>
                                <select name="status" th:with="type=${@dict.getType('cat_order_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li class="select-time">
                                <p>支付时间：</p>
                                <input type="text" class="time-input" id="startPayTime" placeholder="开始时间" name="params[beginPaymentDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endPayTime" placeholder="结束时间" name="params[endPaymentDate]"/>
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endDate]"/>
                            </li>
                            <!--<li>
                                <p>店铺id：</p>
                                <input type="text" name="shopId"/>
                            </li>-->
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <!--<a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:orderInfo:add">
                    <i class="fa fa-plus"></i> 添加
                </a>-->
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:orderInfo:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:orderInfo:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <!--<a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:orderInfo:export">
                    <i class="fa fa-download"></i> 导出
                 </a>-->
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:orderInfo:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:orderInfo:remove')}]];
        var businessTypeDatas = [[${@dict.getType('cat_order_type')}]];
        var statusDatas = [[${@dict.getType('cat_order_status')}]];
        var prefix = ctx + "cat/orderInfo";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "订单",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : '订单id',
                    visible: false
                },
                {
                    field : 'userId', 
                    title : '用户id',
                    visible: false
                },
                {
                    field : 'userName',
                    title : '用户名'
                },
                {
                    field : 'orderNo', 
                    title : '订单编号'
                },
                {
                    field : 'orderSubject', 
                    title : '订单标题',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'businessId', 
                    title : '业务ID',
                    visible: false
                },
                /*{
                    field : 'businessType', 
                    title : '业务类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(businessTypeDatas, value);
                    }
                },*/
                {
                    field : 'payment', 
                    title : '实付款'
                },
                {
                    field : 'totalQuantity',
                    title : '总数量'
                },
                /*{
                    field : 'postage', 
                    title : '运费'
                },*/
                {
                    field : 'trackingNo', 
                    title : '快递单号',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'status', 
                    title : '订单状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field : 'paymentDate', 
                    title : '支付时间'
                },
                /*{
                    field : 'shopId', 
                    title : '店铺id'
                },*/
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-primary btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="on_ship(\'' + row.id + '\')"><i class="fa fa-edit"></i>订单明细</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        //查订单商品列表
        function byOrderIdList(id){
            $.modal.open("商品列表","/cat/orderItem?orderId="+id,1200);
        }
        //一键发货
        function on_ship(id) {
            $.modal.open("发货清单","/cat/orderInfo/shipViwe?id="+id,1200);
        }
    </script>
</body>
</html>
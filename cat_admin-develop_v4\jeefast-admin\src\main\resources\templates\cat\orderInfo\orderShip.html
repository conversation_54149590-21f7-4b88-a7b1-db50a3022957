<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单清单')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse" >
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li style="display: none">
                            <p>订单id：</p>
                            <input type="text" name="orderId" th:value="${orderId}"/>
                        </li>
                    </ul>
                </div>
            </form>
            <div th:object="${shopping}" th:if="${shopping != null}">
                <h3>买家信息</h3>
                <table border="0">
                    <tr style="height: 30px">
                        <td style="text-align:right;">姓名：</td>
                        <td style="min-width:80px"><span th:text="*{userName}"/></td>
                        <td>联系电话：</td>
                        <td><span th:text="*{mobile}"/></td>
                    </tr>
                    <tr style="height: 30px">
                        <td>所在地区：</td>
                        <td colspan="3"><span th:text="*{province + city + district + street}"/></td>
                    </tr>
                    <tr style="height: 30px" >
                        <td>详细地址：</td>
                        <td colspan="3"><span th:text="*{address}"/></td>
                    </tr>
                </table>
            </div>
            <div th:object="${orderInfo}">
                <h3>订单信息</h3>
                <table border="0">
                    <tr style="height: 30px">
                        <td>订单编号：<span th:text="*{orderNo}"/></td>
                        <td>支付编号：<span th:text="*{outTradeNo}"/></td>
                        <td>创建时间：<span th:text="*{#dates.format(createDate, 'yyyy-MM-dd HH:mm:ss')}"/></td>
                    </tr>
                    <tr style="height: 30px">
                        <td>付款时间：<span th:text="*{#dates.format(paymentDate, 'yyyy-MM-dd HH:mm:ss')}"/></td>
                        <td>完成时间：<span th:text="*{#dates.format(endDate, 'yyyy-MM-dd HH:mm:ss')}"/></td>
                    </tr>
                </table>
            </div>
            <form class="form-horizontal m" id="logisticsSub" th:object="${orderInfo}">
                <input style="display: none" type="text" name="id" th:value="${orderId}"/>
                <h3>快递信息</h3>
                <table border="0">
                    <tr>
                        <td>快递名称：</td>
                        <td><input type="text" name="logistics" class="form-control" placeholder="请输入名称" th:field="*{logistics}" required/></td>
                        <td>快递单号：</td>
                        <td><input type="text" name="logisticsNum" class="form-control" placeholder="请输入单号" th:field="*{logisticsNum}" required/></td>
                    </tr>
                </table>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<style>
    td{border:16px solid white;}
</style>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('cat:orderItem:edit')}]];
    var removeFlag = [[${@permission.hasPermi('cat:orderItem:remove')}]];
    var prefix = ctx + "cat/orderItem";

    $(function() {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "订单子",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                {
                    field : 'id',
                    title : '订单子表id',
                    visible: false
                },
                {
                    field : 'userId',
                    title : '用户表id',
                    visible: false
                },
                {
                    field : 'productId',
                    title : '商品id',
                    visible: false
                },
                {
                    field : 'productImage',
                    title : '商品图',
                    formatter: function(value, row, index) {
                        if(value){
                            return $.table.imageView(value);
                        }else {
                            return $.table.imageView('/jeefast.png');
                        }
                    }
                },
                {
                    field : 'productName',
                    title : '商品名称',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'productSkuId',
                    title : '商品sku_id',
                    visible: false
                },
                {
                    field : 'productSkuName',
                    title : '商品规格',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'price',
                    title : '商品单价'
                },
                {
                    field : 'quantity',
                    title : '商品数量'
                },
                {
                    field : 'totalprice',
                    title : '商品总价'
                },
                {
                    field : 'discount',
                    title : '商品折扣'
                },
                {
                    field : 'remark',
                    title : '备注',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                }
            ]
        };
        $.table.init(options);
    });

    $("#logisticsSub").validate({
        focusCleanup: true
    });
    function submitHandler() {
        if ($.validate.form("logisticsSub")) {
            $.operate.save("/cat/orderInfo/ship", $('#logisticsSub').serialize());
        }
    }

</script>
</body>
</html>
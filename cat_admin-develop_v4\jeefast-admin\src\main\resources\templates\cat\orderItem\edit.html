<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改订单子')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-orderItem-edit" th:object="${orderItem}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">订单id：</label>
                <div class="col-sm-8">
                    <input name="orderId" th:field="*{orderId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">用户表id：</label>
                <div class="col-sm-8">
                    <input name="userId" th:field="*{userId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品id：</label>
                <div class="col-sm-8">
                    <input name="productId" th:field="*{productId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品名称：</label>
                <div class="col-sm-8">
                    <input name="productName" th:field="*{productName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">商品图：</label>
                <div class="col-sm-8">
                    <textarea name="productImage" class="form-control">[[*{productImage}]]</textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品sku_id：</label>
                <div class="col-sm-8">
                    <input name="productSkuId" th:field="*{productSkuId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">商品sku_name：</label>
                <div class="col-sm-8">
                    <textarea name="productSkuName" class="form-control">[[*{productSkuName}]]</textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">生成订单时商品单价：</label>
                <div class="col-sm-8">
                    <input name="currentunitprice" th:field="*{currentunitprice}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品数量：</label>
                <div class="col-sm-8">
                    <input name="quantity" th:field="*{quantity}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品单价：</label>
                <div class="col-sm-8">
                    <input name="price" th:field="*{price}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品总价：</label>
                <div class="col-sm-8">
                    <input name="totalprice" th:field="*{totalprice}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">会员总价：</label>
                <div class="col-sm-8">
                    <input name="vipPrice" th:field="*{vipPrice}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品折扣：</label>
                <div class="col-sm-8">
                    <input name="discount" th:field="*{discount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input name="remark" th:field="*{remark}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">物流名称：</label>
                <div class="col-sm-8">
                    <input name="logistics" th:field="*{logistics}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">物流号码：</label>
                <div class="col-sm-8">
                    <input name="logisticsNum" th:field="*{logisticsNum}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" th:value="${#dates.format(orderItem.createDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>
            <!--<div class="form-group">
                <label class="col-sm-3 control-label">店铺id：</label>
                <div class="col-sm-8">
                    <input name="shopId" th:field="*{shopId}" class="form-control" type="text">
                </div>
            </div>-->
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/orderItem";
        $("#form-orderItem-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-orderItem-edit').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
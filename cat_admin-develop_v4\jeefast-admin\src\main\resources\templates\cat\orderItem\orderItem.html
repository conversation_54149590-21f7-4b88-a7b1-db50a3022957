<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('订单子列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>订单id：</p>
                                <input type="text" name="orderId" th:value="${orderId}"/>
                            </li>
                            <li>
                                <p>用户表id：</p>
                                <input type="text" name="userId"/>
                            </li>
                            <li>
                                <p>商品id：</p>
                                <input type="text" name="productId"/>
                            </li>
                            <li>
                                <p>商品名称：</p>
                                <input type="text" name="productName"/>
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <!--<a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:orderItem:add">
                    <i class="fa fa-plus"></i> 添加
                </a>-->
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:orderItem:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:orderItem:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
               <!-- <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:orderItem:export">
                    <i class="fa fa-download"></i> 导出
                 </a>-->
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:orderItem:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:orderItem:remove')}]];
        var prefix = ctx + "cat/orderItem";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "订单子",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : '订单子表id',
                    visible: false
                },
                {
                    field : 'orderId', 
                    title : '订单id'
                },
                {
                    field : 'userId', 
                    title : '用户表id',
                    visible: false
                },
                {
                    field : 'productId', 
                    title : '商品id',
                    visible: false
                },
                {
                    field : 'productName', 
                    title : '商品名称',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    field : 'productImage', 
                    title : '商品图',
                    formatter: function(value, row, index) {
                        if(value){
                            return $.table.imageView(value);
                        }else {
                            return $.table.imageView('/jeefast.png');
                        }
                    }
                },
                {
                    field : 'productSkuId', 
                    title : '商品sku_id',
                    visible: false
                },
                {
                    field : 'productSkuName', 
                    title : '商品规格'
                },
                {
                    field : 'currentunitprice', 
                    title : '当时单价'
                },
                {
                    field : 'price',
                    title : '商品单价'
                },
                {
                    field : 'quantity', 
                    title : '商品数量'
                },
                {
                    field : 'totalprice', 
                    title : '商品总价'
                },
                {
                    field : 'vipPrice', 
                    title : '会员总价'
                },
                {
                    field : 'discount', 
                    title : '商品折扣'
                },
                {
                    field : 'remark', 
                    title : '备注',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
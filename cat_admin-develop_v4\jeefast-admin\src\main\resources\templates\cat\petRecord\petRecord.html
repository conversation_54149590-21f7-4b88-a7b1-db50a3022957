<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('宠物记录列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>宠物id：</p>
                                <input type="text" name="petId"/>
                            </li>
                            <li>
                                <p>用户id：</p>
                                <input type="text" name="userId"/>
                            </li>
                            <li>
                                <p>类型：</p>
                                <select name="type" th:with="type=${@dict.getType('')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>标签：</p>
                                <input type="text" name="tag"/>
                            </li>
                            <li>
                                <p>是否删除：</p>
                                <input type="text" name="isDelete"/>
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:petRecord:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:petRecord:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:petRecord:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:petRecord:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:petRecord:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:petRecord:remove')}]];
        var prefix = ctx + "cat/petRecord";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "宠物记录",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : 'id',
                    visible: false
                },
                {
                    field : 'petId', 
                    title : '宠物id'
                },
                {
                    field : 'userId', 
                    title : '用户id'
                },
                {
                    field : 'content', 
                    title : '内容'
                },
                {
                    field : 'type', 
                    title : '类型'
                },
                {
                    field : 'tag', 
                    title : '标签'
                },
                {
                    field : 'recordDate', 
                    title : '记录时间'
                },
                {
                    field : 'praiseCount', 
                    title : '点赞数'
                },
                {
                    field : 'commentCount', 
                    title : '评论数'
                },
                {
                    field : 'mediaType', 
                    title : '记录类型'
                },
                {
                    field : 'coverImage', 
                    title : '封面图'
                },
                {
                    field : 'height', 
                    title : '资源高度px'
                },
                {
                    field : 'width', 
                    title : '资源宽度px'
                },
                {
                    field : 'mediaCount', 
                    title : '媒体数'
                },
                {
                    field : 'dataExt', 
                    title : '扩展数据'
                },
                {
                    field : 'scope', 
                    title : '可见范围'
                },
                {
                    field : 'isDelete', 
                    title : '是否删除'
                },
                {
                    field : 'createDate', 
                    title : '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('商品sku列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>商品id：</p>
                                <input type="text" name="productId"/>
                            </li>
                            <li>
                                <p>sku_key：</p>
                                <input type="text" name="groupKeyId"/>
                            </li>
                            <li>
                                <p>sku_val：</p>
                                <input type="text" name="groupValId"/>
                            </li>
                            <!--<li>
                                <p>属性搭配方式：</p>
                                <input type="text" name="groupSpace"/>
                            </li>-->
                            <li>
                                <p>价格：</p>
                                <input type="text" name="price"/>
                            </li>
                            <!--<li>
                                <p>库存数量：</p>
                                <input type="text" name="stock"/>
                            </li>
                            <li>
                                <p>总销量：</p>
                                <input type="text" name="totalSales"/>
                            </li>
                            <li>
                                <p>产品组合图：</p>
                                <input type="text" name="mainimage"/>
                            </li>-->
                            <li>
                                <p>sku状态：</p>
                                <select name="status" th:with="type=${@dict.getType('cat_product_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <p>店铺id：</p>
                                <input type="text" name="shopId"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:productSku:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:productSku:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:productSku:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:productSku:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:productSku:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:productSku:remove')}]];
        var statusDatas = [[${@dict.getType('cat_product_status')}]];
        var prefix = ctx + "cat/productSku";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "商品sku",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : 'id',
                    visible: false
                },
                {
                    field : 'productId', 
                    title : '商品id'
                },
                {
                    field : 'groupKeyId', 
                    title : '商品sku_key'
                },
                {
                    field : 'groupValId', 
                    title : '商品sku_val'
                },
                /*{
                    field : 'groupSpace', 
                    title : '属性搭配方式'
                },*/
                {
                    field : 'price', 
                    title : '价格'
                },
                {
                    field : 'stock', 
                    title : '库存数量'
                },
                {
                    field : 'totalSales', 
                    title : '总销量'
                },
                {
                    field : 'mainimage', 
                    title : '产品组合图',
                    formatter: function(value, row, index) {
                        // 图片预览（注意：如存储在本地直接获取数据库路径，如有配置context-path需要使用ctx+路径）
                        // 如：/profile/upload/2019/08/08/3b7a839aced67397bac694d77611ce72.png
                        if(value){
                            return $.table.imageView(value);
                        }else {
                            return $.table.imageView('/jeefast.png');
                        }
                    }
                },
                {
                    field : 'status', 
                    title : 'sku状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field : 'createDate', 
                    title : '创建时间'
                },
                {
                    field : 'updateDate', 
                    title : '更新时间'
                },
                {
                    field : 'shopId', 
                    title : '店铺id'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增商品')" />
    <th:block th:include="include :: datetimepicker-css" />
    <meta name="referrer" content="no-referrer">
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-product-add">
            <input name="categoryId" type="hidden" id="treeId"/>
            <h4 class="form-header h4">销售信息</h4>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品类别：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input name="categoryName" onclick="selectCategoryTree()" id="treeName" type="text" placeholder="请选择分类" class="form-control" readonly="readonly">
                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                    </div>
                    <!--<input name="categoryId" class="form-control" type="text" required>-->
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">商品名称：</label>
                <div class="col-sm-8">
                    <input name="name" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商品副标题：</label>
                <div class="col-sm-8">
                    <input name="subtitle" class="form-control" type="text">
                </div>
            </div>
            <!--<div class="form-group">
                <label class="col-sm-3 control-label">产品主图：</label>
                <div class="col-sm-8">
                    <input name="mainimage" class="form-control" type="text">
                </div>
            </div>-->
            <div class="form-group">
                <label class="col-sm-3 control-label">产品主图：</label>
                <div class="col-sm-8">
                    <div class="fileinput fileinput-new" data-provides="fileinput">
                        <div class="fileinput-new thumbnail" style="width: 336px; height: 140px;">
                            <img />
                        </div>
                        <div class="fileinput-preview fileinput-exists thumbnail" style="max-width: 200px; max-height: 150px;"></div>
                        <div>
                            <span class="btn btn-white btn-file"><span class="fileinput-new">选择图片</span><span class="fileinput-exists">更改</span>
                                <input id="bannerFile" name="file" class="form-control" type="file">
                            </span>
                            <a href="#" class="btn btn-white fileinput-exists" data-dismiss="fileinput">清除</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">价格：</label>
                <div class="col-sm-8">
                    <input name="price" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">原价格：</label>
                <div class="col-sm-8">
                    <input name="originalPrice" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">会员价格：</label>
                <div class="col-sm-8">
                    <input name="vipPrice" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">库存数量：</label>
                <div class="col-sm-8">
                    <input name="stock" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">总销量：</label>
                <div class="col-sm-8">
                    <input name="totalSales" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">推荐：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                        <input type="radio" th:id="${'isRecommend_' + dict.dictCode}" name="isRecommend" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${'isRecommend_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_product_status')}">
                        <input type="radio" th:id="${'status_' + dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <!--<div class="form-group">
                <label class="col-sm-3 control-label">规格项：</label>
                <div class="col-sm-8">
                    <input name="skuName" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">规格值：</label>
                <div class="col-sm-8">
                    <input name="skuVal" class="form-control" type="text">
                </div>
            </div>-->
            <div class="form-group">
                <label class="col-sm-3 control-label">运费：</label>
                <div class="col-sm-8">
                    <input name="postage" class="form-control" type="text">
                </div>
            </div>
            <!--<div class="form-group">
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">更新时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="updateDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>-->
            <!--<div class="form-group">
                <label class="col-sm-3 control-label">店铺id：</label>
                <div class="col-sm-8">
                    <input name="shopId" class="form-control" type="text">
                </div>
            </div>-->
            <div class="form-group">
                <label class="col-sm-3 control-label">商品详情：</label>
                <div class="col-sm-8">
                    <script id="editor" name="detail" type="text/plain" style="width:640px;height:750px;"></script>
                </div>
            </div>
        </form>
    </div>
    <div class="row">
        <div class="col-sm-offset-5 col-sm-10">
            <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
            <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭 </button>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: jasny-bootstrap-js" />
    <script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.all.min.js"> </script>
    <!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
    <!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
    <script type="text/javascript" charset="utf-8" src="/ueditor/lang/zh-cn/zh-cn.js"></script>
    <script type="text/javascript">

        //实例化编辑器
        //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
        var ue = UE.getEditor('editor');

        var prefix = ctx + "cat/product"
        $("#form-product-add").validate({
            focusCleanup: true
        });

        $(function() {

        });


        function submitHandler() {
            if ($.validate.form()) {
                var form = $('#form-product-add')[0];
                var formdata = new FormData(form);
                //formdata.append("detail",UE.getEditor('editor').getContent());
                $.ajax({
                    url: prefix + "/add",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function(result) {
                        $.operate.successTabCallback(result);
                    }
                })
                //var data = $("#form-product-add").serializeArray();
                // data.push({"name": "detail", "value": UE.getEditor('editor').getContent()});
                //$.operate.saveTab(prefix + "/add", formdata);
                //$.operate.save(prefix + "/add", $('#form-product-add').serialize());
            }
        }

        /*用户分类-新增-选择分类树*/
        function selectCategoryTree() {
            var treeId = $("#treeId").val();
            var deptId = $.common.isEmpty(treeId) ? "-1" : $("#treeId").val();
            var url = ctx + "cat/categoryBackstage/selectCategoryTree/" + deptId;
            var options = {
                title: '选择分类',
                width: "380",
                url: url,
                callBack: doSubmit
            };
            $.modal.openOptions(options);
        }
        function doSubmit(index, layero){
            var tree = layero.find("iframe")[0].contentWindow.$._tree;
            if ($.tree.notAllowParents(tree)) {
                var body = layer.getChildFrame('body', index);
                $("#treeId").val(body.find('#treeId').val());
                $("#treeName").val(body.find('#treeName').val());
                layer.close(index);
            }
        }


        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='updateDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
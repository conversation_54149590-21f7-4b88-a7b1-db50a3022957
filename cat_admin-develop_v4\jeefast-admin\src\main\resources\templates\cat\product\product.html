<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('商品列表')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
</head>
<body class="gray-bg">
<div class="ui-layout-center">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>类别id：</p>
                                <input type="text" name="categoryId"/>
                            </li>
                            <li>
                                <p>商品名称：</p>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <p>状态：</p>
                                <select name="status" th:with="type=${@dict.getType('cat_product_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <!--<li>
                                <p>店铺id：</p>
                                <input type="text" name="shopId"/>
                            </li>-->
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" shiro:hasPermission="cat:product:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()" shiro:hasPermission="cat:product:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:product:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:product:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
     </div>
    </div>
    <th:block th:include="include :: footer" />
     <th:block th:include="include :: layout-latest-js" />
     <th:block th:include="include :: ztree-js" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:product:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:product:remove')}]];
        var isRecommendDatas = [[${@dict.getType('cat_recommend')}]];
        var statusDatas = [[${@dict.getType('cat_product_status')}]];
        var prefix = ctx + "cat/product";

        $(function() {
            var panehHidden = false;
            if ($(this).width() < 769) {
                panehHidden = true;
            }
            $('body').layout({ initClosed: panehHidden, west__size: 185 });
            queryList();
        });

        function queryList(){
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "商品",
                columns: [{
                    checkbox: true
                },
                    {
                        field : 'id',
                        title : '商品id',
                        visible: false
                    },
                    {
                        field : 'categoryId',
                        title : '类别id',
                        visible: false
                    },
                    {
                        field : 'categoryName',
                        title : '类别名称'
                    },
                    {
                        field : 'name',
                        title : '商品名称',
                        formatter: function(value, row, index) {
                            return $.table.tooltip(value);
                        }
                    },
                    {
                        field : 'mainimage',
                        title : '产品主图',
                        formatter: function(value, row, index) {
                            // 图片预览（注意：如存储在本地直接获取数据库路径，如有配置context-path需要使用ctx+路径）
                            // 如：/profile/upload/2019/08/08/3b7a839aced67397bac694d77611ce72.png
                            if(value){
                                return $.table.imageView(value);
                            }else {
                                return $.table.imageView('/jeefast.png');
                            }
                        }
                    },
                    {
                        field : 'price',
                        title : '价格'
                    },
                    {
                        field : 'originalPrice',
                        title : '原价格'
                    },
                    {
                        field : 'stock',
                        title : '库存数量'
                    },
                    {
                        field : 'totalSales',
                        title : '总销量'
                    },
                    {
                        field : 'isRecommend',
                        title : '推荐',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabel(isRecommendDatas, value);
                        }
                    },
                    {
                        field : 'status',
                        title : '状态',
                        formatter: function(value, row, index) {
                            return $.table.selectDictLabel(statusDatas, value);
                        }
                    },
                    {
                        field : 'createDate',
                        title : '创建时间'
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="editSku(\'' + row.id + '\')"><i class="fa fa-edit"></i>规格属性</a> ');
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                            return actions.join('');
                        }
                    }]
            };
            $.table.init(options);
        }

        function editSku(productId) {
            $.modal.openTab("修改sku" + $.table._option.modalName, prefix+"/addSku?productId="+productId);
        }
    </script>
</body>
</html>
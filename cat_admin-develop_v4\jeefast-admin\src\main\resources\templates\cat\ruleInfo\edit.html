<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改规则说明')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <!--<div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-ruleInfo-edit" th:object="${ruleInfo}">
            <input name="ruleId" th:field="*{ruleId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">标题：</label>
                <div class="col-sm-8">
                    <input name="title" th:field="*{title}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">内容：</label>
                <div class="col-sm-8">
                    <input name="content" th:field="*{content}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" th:value="${#dates.format(ruleInfo.createDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>
        </form>
    </div>-->

    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="row">
            <div class="col-sm-5" style="padding-left:0px;">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>基础信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="alert alert-info">
                            如有不懂请查看操作文档
                        </div>
                        <form class="form-horizontal m" id="form-ruleInfo-edit" th:object="${ruleInfo}">
                            <input name="ruleId" th:field="*{ruleId}" type="hidden">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">标题：</label>
                                <div class="col-sm-8">
                                    <input name="title" th:field="*{title}" class="form-control" type="text">
                                </div>
                            </div>
                            <!--<div class="form-group">
                                <label class="col-sm-3 control-label">内容：</label>
                                <div class="col-sm-8">
                                    <input name="content" th:field="*{content}" class="form-control" type="text">
                                </div>
                            </div>-->
                            <div class="form-group">
                                <label class="col-sm-3 control-label">创建时间：</label>
                                <div class="col-sm-8">
                                    <div class="input-group date">
                                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                        <input name="createDate" th:value="${#dates.format(ruleInfo.createDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="clearfix"></div>
                    </div>
                </div>
            </div>
            <div class="col-sm-7" style="padding-left:0px;">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>规则内容</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row form-body form-horizontal m-t">
                            <div class="col-sm-8">
                                <script  id="editor" name="content" type="text/plain" style="width:640px;height:750px;" th:utext="${ruleInfo.content}"></script>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>



    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.config.js"></script>
    <script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.all.min.js"> </script>
    <!--建议手动加在语言，避免在ie下有时因为加载语言失败导致编辑器加载失败-->
    <!--这里加载的语言文件会覆盖你在配置项目里添加的语言类型，比如你在配置项目里配置的是英文，这里加载的中文，那最后就是中文-->
    <script type="text/javascript" charset="utf-8" src="/ueditor/lang/zh-cn/zh-cn.js"></script>
    <script type="text/javascript">

        //实例化编辑器
        //建议使用工厂方法getEditor创建和引用编辑器实例，如果在某个闭包下引用该编辑器，直接调用UE.getEditor('editor')就能拿到相关的实例
        var ue = UE.getEditor('editor');

        var prefix = ctx + "cat/ruleInfo";
        $("#form-ruleInfo-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {

                var form = $('#form-ruleInfo-edit')[0];
                var formdata = new FormData(form);
                formdata.append("content",UE.getEditor('editor').getContent());
                $.ajax({
                    url: prefix + "/edit",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    success: function(result) {
                        $.operate.successCallback(result);
                    }
                })

                //$.operate.save(prefix + "/edit", $('#form-ruleInfo-edit').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
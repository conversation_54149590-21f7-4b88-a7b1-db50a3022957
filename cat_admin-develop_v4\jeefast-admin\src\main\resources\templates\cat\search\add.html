<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增热搜词条')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-search-add">
            <div class="form-group">
                <label class="col-sm-3 control-label">标题：</label>
                <div class="col-sm-8">
                    <input name="title" class="form-control" maxlength="60" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">虚拟阅读量：</label>
                <div class="col-sm-8">
                    <input name="inventedReadNo" class="form-control" type="number"  min="1" step="1" max="999999999" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">排序值：</label>
                <div class="col-sm-8">
                    <input name="sortNo" class="form-control" type="number" min="1" step="1" max="10" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">时效开始时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="beginDate" class="form-control" placeholder="yyyy-MM-dd HH:mm" type="text" required>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">时效结束时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="endDate" class="form-control" placeholder="yyyy-MM-dd HH:mm" type="text" required>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">跳转链接：</label>
                <div class="col-sm-8">
                    <input name="routeUrl" class="form-control" required/>
                    <div style="margin-top: 6px;color: indianred;">（文章：/pageSub/note/note?id=?）</div>
                    <div style="margin-top: 6px;color: indianred;">（视频：/find/pages/swiperVideo/swiperVideo?id=?）</div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/search"
        $("#form-search-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-search-add').serialize());
            }
        }
        $("input[name='beginDate']").datetimepicker({
            format: "yyyy-mm-dd hh:ii",
            // minView: 0,
            minuteStep:1,
            autoclose: true
        });

        $("input[name='endDate']").datetimepicker({
            format: "yyyy-mm-dd hh:ii",
            // minView: 0,
            minuteStep:1,
            autoclose: true
        });
    </script>
</body>
</html>
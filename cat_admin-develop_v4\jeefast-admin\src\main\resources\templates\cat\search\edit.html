<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改热搜词条')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-search-edit" th:object="${hotSearch}">
            <input name="id" th:field="*{id}" type="hidden">
            <input name="dynamicId" th:field="*{dynamicId}" class="form-control" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label">标题：</label>
                <div class="col-sm-8">
                    <input name="title" th:field="*{title}" maxlength="60" class="form-control" type="text" required/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">是否广告：</label>
                <div class="col-sm-8">
                    <select name="beAdvertising" class="form-control m-b" th:with="type=${@dict.getType('cat_recommend')}" disabled>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{beAdvertising}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">热版类型：</label>
                <div class="col-sm-8">
                    <select name="hotType" class="form-control m-b" th:with="type=${@dict.getType('cat_hot_type')}" disabled>
                        <option value=""></option>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{hotType}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">最新阅读量：</label>
                <div class="col-sm-8">
                    <input name="totalReadNo" th:field="*{totalReadNo}" class="form-control" type="text" disabled>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">最新阅读量更新时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="totalReadNoUpdateDate" th:value="${#dates.format(hotSearch.totalReadNoUpdateDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text" disabled>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">实际阅读量：</label>
                <div class="col-sm-8">
                    <input name="realReadNo" th:field="*{realReadNo}" class="form-control" type="text" disabled>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">虚拟阅读量：</label>
                <div class="col-sm-8">
                    <input name="inventedReadNo" th:field="*{inventedReadNo}" class="form-control" type="number"  min="1" step="1" max="999999999" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">排序值：</label>
                <div class="col-sm-8">
                    <input name="sortNo" th:field="*{sortNo}" class="form-control" type="number" min="1" step="1" max="10" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">时效开始时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="beginDate" th:value="${#dates.format(hotSearch.beginDate, 'yyyy-MM-dd HH:mm')}" class="form-control" placeholder="yyyy-MM-dd HH:mm" type="text" >
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">时效结束时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="endDate" th:value="${#dates.format(hotSearch.endDate, 'yyyy-MM-dd HH:mm')}" class="form-control" placeholder="yyyy-MM-dd HH:mm" type="text">
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">跳转链接：</label>
                <div class="col-sm-8">
                    <input name="routeUrl" class="form-control" th:field="*{routeUrl}"  required/>
                    <div style="margin-top: 6px;color: indianred;">（文章：/pageSub/note/note?id=?）</div>
                    <div style="margin-top: 6px;color: indianred;">（视频：/find/pages/swiperVideo/swiperVideo?id=?）</div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/search";
        $("#form-search-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                var type = $("select[name='beAdvertising']").val();
                if (type==1 ){
                    let beginDateVal = $("input[name='beginDate']").val();
                    if (!beginDateVal || beginDateVal==""){
                        alert("广告类型 时效开始时间不能为空");
                        return
                    }
                    let endDateVal = $("input[name='endDate']").val();
                    if (!endDateVal || endDateVal==""){
                        alert("广告类型 时效结束时间不能为空");
                        return
                    }
                }
                $.operate.save(prefix + "/edit", $('#form-search-edit').serialize());
            }
        }

        $("input[name='totalReadNoUpdateDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='beginDate']").datetimepicker({
            format: "yyyy-mm-dd hh:ii",
            // minView: 0,
            minuteStep:1,
            autoclose: true
        });

        $("input[name='endDate']").datetimepicker({
            format: "yyyy-mm-dd hh:ii",
            // minView: 0,
            minuteStep:1,
            autoclose: true
        });


    </script>
</body>
</html>
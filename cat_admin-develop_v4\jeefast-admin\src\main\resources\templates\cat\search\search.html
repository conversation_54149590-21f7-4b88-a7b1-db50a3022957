<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('热搜词条列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>标题：</p>
                                <input type="text" name="title"/>
                            </li>
                            <li>
                                <p>是否广告：</p>
                                <select name="beAdvertising" th:with="type=${@dict.getType('cat_recommend')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>热版类型：</p>
                                <select name="hotType" th:with="type=${@dict.getType('cat_hot_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:search:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:search:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:search:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:search:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:search:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:search:remove')}]];
        var beAdvertisingDatas = [[${@dict.getType('cat_recommend')}]];
        var hotTypeDatas = [[${@dict.getType('cat_hot_type')}]];
        var isDeleteDatas = [[${@dict.getType('cat_recommend')}]];
        var prefix = ctx + "cat/search";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "热搜词条",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : '主键',
                    visible: false
                },
                {
                    field : 'dynamicId',
                    title : '动态ID'
                },
                {
                    field : 'title', 
                    title : '标题'
                },
                {
                    field : 'beAdvertising', 
                    title : '是否广告',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(beAdvertisingDatas, value);
                    }
                },
                {
                    field : 'hotType', 
                    title : '热版类型',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(hotTypeDatas, value);
                    }
                },
                {
                    field : 'totalReadNo', 
                    title : '最新阅读量'
                },
                {
                    field : 'totalReadNoUpdateDate', 
                    title : '最新阅读量更新时间'
                },
                {
                    field : 'realReadNo', 
                    title : '实际阅读量'
                },
                {
                    field : 'inventedReadNo', 
                    title : '虚拟阅读量'
                },
                {
                    field : 'sortNo', 
                    title : '排序值'
                },
                {
                    field : 'beginDate', 
                    title : '时效开始时间'
                },
                {
                    field : 'endDate', 
                    title : '时效结束时间'
                },
                {
                    field : 'routeUrl', 
                    title : '跳转链接'
                },
                // {
                //     field : 'isDelete',
                //     title : '是否删除',
                //     formatter: function(value, row, index) {
                //        return $.table.selectDictLabel(isDeleteDatas, value);
                //     }
                // },
                {
                    field : 'createDate', 
                    title : '创建时间'
                },
                // {
                //     field : 'updateDate',
                //     title : '更新时间'
                // },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
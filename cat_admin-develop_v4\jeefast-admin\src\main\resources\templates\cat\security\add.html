<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增动态内容安全审核记录')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-security-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">动态内容id：</label>
                <div class="col-sm-8">
                    <input name="dynamicId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">类型 动态标题、动态文字内容、图、视频：</label>
                <div class="col-sm-8">
                    <select name="type" class="form-control m-b" th:with="type=${@dict.getType('cat_dynamic_security')}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">任务taskId：</label>
                <div class="col-sm-8">
                    <input name="taskId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">动态内容媒体id：</label>
                <div class="col-sm-8">
                    <input name="relateId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">媒体内容url：</label>
                <div class="col-sm-8">
                    <input name="relateUrl" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">文字内容：</label>
                <div class="col-sm-8">
                    <textarea name="content" class="form-control"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">识别策略：</label>
                <div class="col-sm-8">
                    <input name="bizType" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">任务状态：FINISH：</label>
                <div class="col-sm-8">
                    <select name="status" class="form-control m-b" th:with="type=${@dict.getType('cat_dynamic_security_status')}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">后续操作建议：Block：建议屏蔽，Review ：建议人工复审，Pass：建议通过：</label>
                <div class="col-sm-8">
                    <select name="suggestion" class="form-control m-b" th:with="type=${@dict.getType('cat_dynamic_security_suggestion')}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">返回检测结果：Normal：正常，Porn：色情，Abuse：谩骂，Ad：广告；以及其他令人反感、不安全或不适宜的内容类型。：</label>
                <div class="col-sm-8">
                    <select name="labels" class="form-control m-b" th:with="type=${@dict.getType('cat_dynamic_security_label')}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">返回检测结果，命中的关键词。：</label>
                <div class="col-sm-8">
                    <select name="keywords" class="form-control m-b">
                        <option value="">所有</option>
                    </select>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 代码生成请选择字典属性</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">置信度,取值范围：0：</label>
                <div class="col-sm-8">
                    <input name="score" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">内容安全检查返回信息：</label>
                <div class="col-sm-8">
                    <input name="respContent" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">最终审核状态 0待审核 1通过 2不通过：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" name="auditStatus" value="">
                        <label th:for="auditStatus" th:text="未知"></label>
                    </div>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 代码生成请选择字典属性</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">审核原因：</label>
                <div class="col-sm-8">
                    <input name="auditReason" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">null：</label>
                <div class="col-sm-8">
                    <input name="sortNo" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">null：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/security"
        $("#form-security-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-security-add').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
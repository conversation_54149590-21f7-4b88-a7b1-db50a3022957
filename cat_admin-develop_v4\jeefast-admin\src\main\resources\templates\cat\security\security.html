<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('动态内容安全审核记录列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>主键id：</p>
                                <input type="text" name="id"/>
                            </li>
                            <li>
                                <p>动态内容id：</p>
                                <input type="text" name="dynamicId"/>
                            </li>
                            <li>
                                <p>类型 动态标题、动态文字内容、图、视频：</p>
                                <select name="type" th:with="type=${@dict.getType('cat_dynamic_security')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>任务taskId：</p>
                                <input type="text" name="taskId"/>
                            </li>
                            <li>
                                <p>动态内容媒体id：</p>
                                <input type="text" name="relateId"/>
                            </li>
                            <li>
                                <p>媒体内容url：</p>
                                <input type="text" name="relateUrl"/>
                            </li>
                            <li>
                                <p>识别策略：</p>
                                <input type="text" name="bizType"/>
                            </li>
                            <li>
                                <p>任务状态：FINISH：</p>
                                <select name="status" th:with="type=${@dict.getType('cat_dynamic_security_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>后续操作建议：Block：建议屏蔽，Review ：建议人工复审，Pass：建议通过：</p>
                                <select name="suggestion" th:with="type=${@dict.getType('cat_dynamic_security_suggestion')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>返回检测结果：Normal：正常，Porn：色情，Abuse：谩骂，Ad：广告；以及其他令人反感、不安全或不适宜的内容类型。：</p>
                                <select name="labels" th:with="type=${@dict.getType('cat_dynamic_security_label')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>返回检测结果，命中的关键词。：</p>
                                <select name="keywords" th:with="type=${@dict.getType('')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>置信度,取值范围：0：</p>
                                <input type="text" name="score"/>
                            </li>
                            <li>
                                <p>内容安全检查返回信息：</p>
                                <input type="text" name="respContent"/>
                            </li>
                            <li>
                                <p>最终审核状态 0待审核 1通过 2不通过：</p>
                                <select name="auditStatus">
                                    <option value="">所有</option>
                                </select>
                            </li>
                            <li>
                                <p>审核原因：</p>
                                <input type="text" name="auditReason"/>
                            </li>
                            <li>
                                <p>null：</p>
                                <input type="text" name="sortNo"/>
                            </li>
                            <li class="select-time">
                                <p>null：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:security:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:security:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:security:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:security:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:security:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:security:remove')}]];
        var typeDatas = [[${@dict.getType('cat_dynamic_security')}]];
        var statusDatas = [[${@dict.getType('cat_dynamic_security_status')}]];
        var suggestionDatas = [[${@dict.getType('cat_dynamic_security_suggestion')}]];
        var labelsDatas = [[${@dict.getType('cat_dynamic_security_label')}]];
        var prefix = ctx + "cat/security";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "动态内容安全审核记录",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : '主键id',
                    visible: false
                },
                {
                    field : 'dynamicId', 
                    title : '动态内容id'
                },
                {
                    field : 'type', 
                    title : '类型 动态标题、动态文字内容、图、视频',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(typeDatas, value);
                    }
                },
                {
                    field : 'taskId', 
                    title : '任务taskId'
                },
                {
                    field : 'relateId', 
                    title : '动态内容媒体id'
                },
                {
                    field : 'relateUrl', 
                    title : '媒体内容url'
                },
                {
                    field : 'content', 
                    title : '文字内容'
                },
                {
                    field : 'bizType', 
                    title : '识别策略'
                },
                {
                    field : 'status', 
                    title : '任务状态：FINISH',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field : 'suggestion', 
                    title : '后续操作建议：Block：建议屏蔽，Review ：建议人工复审，Pass：建议通过',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(suggestionDatas, value);
                    }
                },
                {
                    field : 'labels', 
                    title : '返回检测结果：Normal：正常，Porn：色情，Abuse：谩骂，Ad：广告；以及其他令人反感、不安全或不适宜的内容类型。',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(labelsDatas, value);
                    }
                },
                {
                    field : 'keywords', 
                    title : '返回检测结果，命中的关键词。'
                },
                {
                    field : 'score', 
                    title : '置信度,取值范围：0'
                },
                {
                    field : 'respContent', 
                    title : '内容安全检查返回信息'
                },
                {
                    field : 'auditStatus', 
                    title : '最终审核状态 0待审核 1通过 2不通过'
                },
                {
                    field : 'auditReason', 
                    title : '审核原因'
                },
                {
                    field : 'sortNo', 
                    title : 'null'
                },
                {
                    field : 'createDate', 
                    title : 'null'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改敏感词管理')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-sensiWords-edit" th:object="${sensiWords}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">敏感词：</label>
                <div class="col-sm-8">
                    <input name="word" th:field="*{word}" class="form-control" type="text" placeholder="请输入敏感词" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">类型：</label>
                <div class="col-sm-8">
                    <!--<input name="classify" th:field="*{classify}" class="form-control" type="text" required>-->
                    <select name="classify" class="form-control m-b">
                        <option value="色情" th:selected="${sensiWords.classify=='色情'}">色情</option>
                        <option value="政治" th:selected="${sensiWords.classify=='政治'}">政治</option>
                        <option value="广告" th:selected="${sensiWords.classify=='广告'}">广告</option>
                        <option value="骂人" th:selected="${sensiWords.classify=='骂人'}">骂人</option>
                        <option value="宗教" th:selected="${sensiWords.classify=='宗教'}">宗教</option>
                        <option value="违禁品" th:selected="${sensiWords.classify=='违禁品'}">违禁品</option>
                        <option value="名人" th:selected="${sensiWords.classify=='名人'}">名人</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">类型：</label>
                <div class="col-sm-8">
                    <select name="type" class="form-control m-b">
                        <option value="0" th:selected="${sensiWords.type=='0'}">屏蔽词</option>
                        <option value="1" th:selected="${sensiWords.type=='1'}">允许词</option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">级别：</label>
                <div class="col-sm-8">
                    <input name="level" th:field="*{level}" class="form-control" type="text" placeholder="敏感词严重程度1-100，允许词可以填0">
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 敏感词严重程度1-100，允许词填0</span>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/sensiWords";
        $("#form-sensiWords-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-sensiWords-edit').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
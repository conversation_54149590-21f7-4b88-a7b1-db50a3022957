<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('敏感词管理列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>敏感词：</p>
                                <input type="text" name="word"/>
                            </li>
                            <li>
                                <p>分类：</p>
                                <select name="classify">
                                    <option value="">所有</option>
                                    <option value="色情">色情</option>
                                    <option value="政治">政治</option>
                                    <option value="广告">广告</option>
                                    <option value="骂人">骂人</option>
                                    <option value="宗教">宗教</option>
                                    <option value="违禁品">违禁品</option>
                                    <option value="名人">名人</option>
                                </select>
                            </li>
                            <li>
                                <p>类型：</p>
                                <select name="type">
                                    <option value="">所有</option>
                                    <option value="0">屏蔽词</option>
                                    <option value="1">允许词</option>
                                </select>
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateDate]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateDate]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:sensiWords:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:sensiWords:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:sensiWords:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:sensiWords:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
                <span style="margin-left: 20px;color:red;"> 内置有6w多敏感词，误判请加允许词!</span>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:sensiWords:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:sensiWords:remove')}]];
        var prefix = ctx + "cat/sensiWords";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                sortName: "createDate",
                sortOrder: "desc",
                modalName: "敏感词管理",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : 'id',
                    visible: false
                },
                {
                    field : 'word', 
                    title : '敏感词'
                },
                {
                    field : 'classify', 
                    title : '分类'
                },
                {
                    field : 'level', 
                    title : '级别',
                    visible: false
                },
                {
                    field : 'type',
                    title : '类型',
                    formatter: function(value, row, index) {
                        if (value == 0) {
                            return '<span class="badge badge-warning">屏蔽词</span>';
                        } else if (value == 1) {
                            return '<span class="badge badge-primary">允许词</span>';
                        }
                    }
                },
                {
                    field : 'createDate', 
                    title : '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
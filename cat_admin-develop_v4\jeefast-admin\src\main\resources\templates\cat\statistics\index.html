<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('智能分诊统计')" />
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/main/animate.min.css" th:href="@{/css/main/animate.min.css}" rel="stylesheet"/>
    <link href="../static/css/main/style.min862f.css" th:href="@{/css/main/style.min862f.css}" rel="stylesheet"/>
</head>
<style>
    .ibox-header{
        display: flex;
    }
    .ibox-header-item{
        display: flex;
        align-items: center;
        padding-left: 30px;
    }
    .item-icon{
        width: 60px;
        height: 60px;
        /*border-radius: 50px;*/
    }
    .item-desc{
        padding-left: 10px;
    }
    .item-desc-title{
        font-size: 14px;
    }
    .item-desc-number{
        font-weight: bolder;
        font-size: 16px;
    }
</style>
<body class="gray-bg">
<div  class="wrapper wrapper-content container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li class="select-time">
                            <label>起止日期范围： </label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="startTime"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="endTime"/>
                        </li>
                        <li>
                            渠道：
                            <select name="channelId">
                                <option value="">所有</option>
                                <option th:each="channel : ${channel_list}" th:text="${channel.channelName}" th:value="${channel.id}"></option>
                            </select>
                        <li>
                            医院：
                            <select name="hospitalId">
                                <option value="">所有</option>
                                <option th:each="hospital : ${hospital_list}" th:text="${hospital.hospitalName}" th:value="${hospital.id}"></option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="userTrendChart()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins" style="height: 650px">
                    <div class="ibox-title ibox-header">
                        <div class="ibox-header-item">
                            <img class="item-icon" th:src="@{/img/fw_count.png}">
                            <div class="item-desc">
                                <div class="item-desc-title">访问量</div>
                                <div class="item-desc-number" id="stat-fw-count">0</div>
                            </div>
                        </div>
                        <div class="ibox-header-item">
                            <img class="item-icon" th:src="@{/img/fw_number.png}">
                            <div class="item-desc">
                                <div class="item-desc-title">访问人数</div>
                                <div class="item-desc-number" id="stat-fw-number">0</div>
                            </div>
                        </div>
                        <div class="ibox-header-item">
                            <img class="item-icon" th:src="@{/img/dz_count.png}">
                            <div class="item-desc">
                                <div class="item-desc-title">导诊量</div>
                                <div class="item-desc-number" id="stat-dz-count">0</div>
                            </div>
                        </div>
                        <div class="ibox-header-item">
                            <img class="item-icon" th:src="@{/img/dz_number.png}">
                            <div class="item-desc">
                                <div class="item-desc-title">导诊人数</div>
                                <div class="item-desc-number" id="stat-dz-number">0</div>
                            </div>
                        </div>
                        <div class="ibox-header-item">
                            <img class="item-icon" th:src="@{/img/gh_count.png}">
                            <div class="item-desc">
                                <div class="item-desc-title">挂号点击量</div>
                                <div class="item-desc-number" id="stat-gh-count">0</div>
                            </div>
                        </div>
                        <div class="ibox-header-item">
                            <img class="item-icon" th:src="@{/img/gh_number.png}">
                            <div class="item-desc">
                                <div class="item-desc-title">点击人数</div>
                                <div class="item-desc-number" id="stat-gh-number">0</div>
                            </div>
                        </div>
                        <div class="ibox-header-item">
                            <img class="item-icon" th:src="@{/img/gh_success.png}">
                            <div class="item-desc">
                                <div class="item-desc-title">挂号量</div>
                                <div class="item-desc-number" id="stat-gh-success">0</div>
                            </div>
                        </div>
                        <div class="ibox-header-item">
                            <img class="item-icon" th:src="@{/img/ai_image.png}">
                            <div class="item-desc">
                                <div class="item-desc-title">图片报告</div>
                                <div class="item-desc-number" id="stat-image">0</div>
                            </div>
                        </div>
                        <div class="ibox-header-item">
                            <img class="item-icon" th:src="@{/img/ai_pdf.png}">
                            <div class="item-desc">
                                <div class="item-desc-title">PDF报告</div>
                                <div class="item-desc-number" id="stat-pdf">0</div>
                            </div>
                        </div>
                    </div>
                    <div class="ibox-title">
                        <h5 class="active-devices">分诊统计</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-12" style="padding-bottom: 26px">
                                <div class="flot-chart">
                                    <div class="echarts" id="userTrendChart" style="height: 400px"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
<!--            <div class="col-sm-12 select-table table-striped">-->
<!--                <table id="bootstrap-table" data-mobile-responsive="true"></table>-->
<!--            </div>-->
        </div>
    </div>
</div>
<script th:src="@{/js/jquery.min.js}"></script>
<script th:src="@{/js/bootstrap.min.js}"></script>
<script th:src="@{/ajax/libs/flot/jquery.flot.js}"></script>
<th:block th:include="include :: footer" />
<th:block th:include="include :: sparkline-js" />
<th:block th:include="include :: echarts-js" />
<script th:inline="javascript">

    var prefix = ctx + "cat/statistics";

    $(document).ready(function () {
        //用户使用趋势图
        initTable()
        userTrendChart();
    });

    function userTrendChart(){
        var form = $('#formId')[0];
        var formdata = new FormData(form);
        var chartDom=null;
        $.ajax({
            url: prefix + "/count",
            data: formdata,
            type: "post",
            processData: false,
            contentType: false,
            success: function(result) {
                console.log('result',result);
                if(0==result.code){
                    chartDom = echarts.init(document.getElementById("userTrendChart"),"macarons");
                    var option;
                    var data = result.data;
                    //
                    loadTableData(data)
                    let seriesOptions=[
                        '访问量', '访问人数','导诊量','导诊人数','挂号点击量','点击人数','挂号量','图片报告','PDF报告'
                    ];
                    let category=data.map(i=>i.count_date);
                    let series=[];
                    let dataList=[
                        data.map(i=>i.fw_count),
                        data.map(i=>i.fw_number),
                        data.map(i=>i.dz_count),
                        data.map(i=>i.dz_number),
                        data.map(i=>i.gh_count),
                        data.map(i=>i.gh_number),
                        data.map(i=>i.success_count),
                        data.map(i=>i.sc_image_count),
                        data.map(i=>i.sc_pdf_count)
                    ];
                    $('#stat-fw-count').html(dataList[0].reduce((acc, curr) => acc + curr, 0));
                    $('#stat-fw-number').html(dataList[1].reduce((acc, curr) => acc + curr, 0));
                    $('#stat-dz-count').html(dataList[2].reduce((acc, curr) => acc + curr, 0));
                    $('#stat-dz-number').html(dataList[3].reduce((acc, curr) => acc + curr, 0));
                    $('#stat-gh-count').html(dataList[4].reduce((acc, curr) => acc + curr, 0));
                    $('#stat-gh-number').html(dataList[5].reduce((acc, curr) => acc + curr, 0));
                    $('#stat-gh-success').html(dataList[6].reduce((acc, curr) => acc + curr, 0));
                    $('#stat-image').html(dataList[7].reduce((acc, curr) => acc + curr, 0));
                    $('#stat-pdf').html(dataList[8].reduce((acc, curr) => acc + curr, 0));
                    for(let i=0;i<seriesOptions.length;i++){
                        let seriesItem={
                            name: seriesOptions[i],
                            symbolSize: 10,//设定实心点的大小
                            cursor:"default",
                            data: dataList[i],
                            type: 'line'
                        };
                        series.push(seriesItem);
                    }
                    option = {
                        legend: {
                            data: seriesOptions,
                            textStyle:{
                                fontSize:14
                            }
                        },
                        grid:{
                            left: 50,  // 左侧留白
                            right: 50,  // 右侧留白
                            bottom: 20, // 下方留白
                            top: 20,   // 上方留白
                        },
                        tooltip: {
                            trigger: 'axis',
                        },
                        xAxis: {
                            type: 'category',
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: '#838383'
                                }
                            },
                            axisLine:{
                                lineStyle:{
                                    color:'#838383',
                                }
                            },
                            data: category
                        },
                        yAxis: {
                            type: 'value',
                            scale: true,
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: '#838383'
                                }
                            },
                            axisLine:{
                                lineStyle:{
                                    color:'#838383',
                                }
                            },
                        },
                        series: series
                    };
                    chartDom.setOption(option);
                    $(window).resize(chartDom.resize);
                }else{
                    alert(result.msg)
                }
            }
        })
    }

    function initTable(){
        var options = {
            data:[],
            pagination:false,
            columns: [
                {
                    checkbox: true
                },
                {
                    field : 'count_date',
                    title : '统计日期'
                },
                {
                    field : 'fw_count',
                    title : '访问量'
                },
                {
                    field : 'fw_number',
                    title : '访问人数'
                },
                {
                    field : 'dz_count',
                    title : '导诊量'
                },
                {
                    field : 'dz_number',
                    title : '导诊人数'
                },
                {
                    field : 'gh_count',
                    title : '挂号点击量'
                },
                {
                    field : 'gh_number',
                    title : '点击人数'
                },
                {
                    field : 'success_count',
                    title : '挂号量'
                },
                {
                    field : 'sc_image_count',
                    title : '图片报告'
                },
                {
                    field : 'sc_pdf_count',
                    title : 'PDF报告'
                }
            ]
        };
        $('#bootstrap-table').bootstrapTable(options);
    }

    function loadTableData(dataList){
        $('#bootstrap-table').bootstrapTable('load',dataList);
    }
</script>
</body>
</html>
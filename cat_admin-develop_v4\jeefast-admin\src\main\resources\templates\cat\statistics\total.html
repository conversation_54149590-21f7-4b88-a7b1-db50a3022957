<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('科室导诊挂号')" />
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/main/animate.min.css" th:href="@{/css/main/animate.min.css}" rel="stylesheet"/>
    <link href="../static/css/main/style.min862f.css" th:href="@{/css/main/style.min862f.css}" rel="stylesheet"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li class="select-time">
                            <label>起止日期范围： </label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="startTime"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="endTime"/>
                        </li>
                        <li>
                            渠道：
                            <select name="channelId">
                                <option value="">所有</option>
                                <option th:each="channel : ${channel_list}" th:text="${channel.channelName}" th:value="${channel.id}"></option>
                            </select>
                        <li>
                            医院：
                            <select name="hospitalId" id="hospitalId" onchange="getDeptList()">
                                <option value="">所有</option>
                                <option th:each="hospital : ${hospital_list}" th:text="${hospital.hospitalName}" th:value="${hospital.id}"></option>
                            </select>
                        </li>
                        <li>
                            科室：
                            <select name="deptCode" id="deptCode">
                                <option value="">所有</option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="getDataList()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<script th:src="@{/js/jquery.min.js}"></script>
<script th:src="@{/js/bootstrap.min.js}"></script>
<script th:src="@{/ajax/libs/flot/jquery.flot.js}"></script>
<th:block th:include="include :: footer" />
<th:block th:include="include :: sparkline-js" />
<th:block th:include="include :: echarts-js" />
<script th:inline="javascript">

    var prefix = ctx + "cat/statistics";

    $(document).ready(function () {
        //用户使用趋势图
        initTable()
        getDataList();
    });

    function getDeptList(){
        let hospitalId=$('#hospitalId').val();
        if(hospitalId>0){
            $('#deptCode').empty(); // 清空所有选项
            $('#deptCode').append('<option value="">所有</option>'); // 添加新选项
            $.ajax({
                url: ctx+"cat/hospital/getDeptInfo?id="+hospitalId,
                type: "get",
                processData: false,
                contentType: false,
                success: function(result) {
                    console.log('result',result);
                    if(0==result.code){
                        let data=result.data;
                        if(data!=null&&data.length>0){
                            data.forEach(itm=>{
                                $('#deptCode').append('<option value="' + itm.value + '">' + itm.label + '</option>'); // 添加新选项
                            });
                        }
                    }else{
                        alert(result.msg)
                    }
                }
            });
        }
    }

    function getDataList(){
        var form = $('#formId')[0];
        var formdata = new FormData(form);
        $.ajax({
            url: prefix + "/dept-total",
            data: formdata,
            type: "post",
            processData: false,
            contentType: false,
            success: function(result) {
                console.log('result',result);
                if(0==result.code){
                    let data=result.data;
                    loadTableData(data)
                }else{
                    alert(result.msg)
                }
            }
        })
    }

    function initTable(){
        var options = {
            // url: prefix + "/dept-count",
            pagination:false,
            data:[],
            sortName:'dz_count',
            sortOrder:'desc',
            columns: [
                {
                    checkbox: true
                },
                {
                    field : 'channel_name',
                    title : '渠道'
                },
                {
                    field : 'hospital_name',
                    title : '医院'
                },
                {
                    field : 'dept_name',
                    title : '科室'
                },
                {
                    field : 'dz_count',
                    title : '导诊量',
                    sortable:true
                },
                {
                    field : 'gh_count',
                    title : '挂号点击量',
                    sortable:true
                },
                {
                    field : 'success_count',
                    title : '挂号量',
                    sortable:true
                }
            ]
        };
        $('#bootstrap-table').bootstrapTable(options);
    }

    function loadTableData(dataList){
        $('#bootstrap-table').bootstrapTable('load',dataList);
    }
</script>
</body>
</html>
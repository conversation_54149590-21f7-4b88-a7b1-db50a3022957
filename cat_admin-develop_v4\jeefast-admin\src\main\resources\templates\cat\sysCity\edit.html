<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改城市')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-sysCity-edit" th:object="${sysCity}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">城市id：</label>
                <div class="col-sm-8">
                    <input name="cityId" th:field="*{cityId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">省市级别：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" name="level" value="" required>
                        <label th:for="level" th:text="未知"></label>
                    </div>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 代码生成请选择字典属性</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">父级id：</label>
                <div class="col-sm-8">
                    <input name="parentId" th:field="*{parentId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">区号：</label>
                <div class="col-sm-8">
                    <input name="areaCode" th:field="*{areaCode}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">名称：</label>
                <div class="col-sm-8">
                    <input name="name" th:field="*{name}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">合并名称：</label>
                <div class="col-sm-8">
                    <input name="mergerName" th:field="*{mergerName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">经度：</label>
                <div class="col-sm-8">
                    <input name="lng" th:field="*{lng}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">纬度：</label>
                <div class="col-sm-8">
                    <input name="lat" th:field="*{lat}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否展示：</label>
                <div class="col-sm-8">
                    <input name="isShow" th:field="*{isShow}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">全拼：</label>
                <div class="col-sm-8">
                    <input name="fullSpell" th:field="*{fullSpell}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">简拼：</label>
                <div class="col-sm-8">
                    <input name="simpleSpell" th:field="*{simpleSpell}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">首字母：</label>
                <div class="col-sm-8">
                    <input name="firstSpell" th:field="*{firstSpell}" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "cat/sysCity";
        $("#form-sysCity-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-sysCity-edit').serialize());
            }
        }
    </script>
</body>
</html>
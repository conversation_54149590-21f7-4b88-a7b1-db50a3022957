<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改话题')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-topicInfoBackstage-edit" th:object="${topicInfoBackstage}">
            <input name="topicId" th:field="*{topicId}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label">封面图：</label>
                <div class="col-sm-8">
<!--                    <input name="coverImage" th:field="*{coverImage}" class="form-control" type="text" required>-->
                    <div class="fileinput fileinput-new" data-provides="fileinput">
                        <div class="fileinput-new thumbnail" style="width: 336px; height: 140px;">
                            <img th:src="*{coverImage}">
                        </div>
                        <div class="fileinput-preview fileinput-exists thumbnail" style="max-width: 200px; max-height: 150px;"></div>
                        <div>
                            <span class="btn btn-white btn-file"><span class="fileinput-new">选择图片</span><span class="fileinput-exists">更改</span>
                                <input id="bannerFile" name="file" class="form-control" type="file">
                            </span>
                            <a href="#" class="btn btn-white fileinput-exists" data-dismiss="fileinput">清除</a>
                        </div>
                    </div>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i>158*98像素尺寸比例</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">话题名称：</label>
                <div class="col-sm-8">
                    <input name="topicName" th:field="*{topicName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">话题分组：</label>
                <div class="col-sm-8">
                    <select id="groupId" name="groupId" class="form-control m-b" required>
                        <option isconf="-1" value="-1">---请选择---</option>
                        <option th:each="item : ${topicGrpupList}" th:text="${item.name}" th:value="${item.id}" th:field="*{groupId}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">话题被引用次数：</label>
                <div class="col-sm-8">
                    <input name="citeCount" th:field="*{citeCount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">话题创建人：</label>
                <div class="col-sm-8">
                    <input name="userId" th:field="*{userId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">简介：</label>
                <div class="col-sm-8">
                    <input name="brief" th:field="*{brief}" class="form-control" type="text">
                </div>
            </div>
            <!--<div class="form-group">
                <label class="col-sm-3 control-label">话题创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" th:value="${#dates.format(topicInfoBackstage.createDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>-->
            <div class="form-group">    
                <label class="col-sm-3 control-label">话题加入人数：</label>
                <div class="col-sm-8">
                    <input name="joinCount" th:field="*{joinCount}" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: jasny-bootstrap-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/topicInfoBackstage";
        $("#form-topicInfoBackstage-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {

                var form = $('#form-topicInfoBackstage-edit')[0];
                var formdata = new FormData(form);
                $.ajax({
                    url: prefix + "/edit",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    success: function(result) {
                        $.operate.successCallback(result);
                    }
                })
                //$.operate.save(prefix + "/edit", $('#form-topicInfoBackstage-edit').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
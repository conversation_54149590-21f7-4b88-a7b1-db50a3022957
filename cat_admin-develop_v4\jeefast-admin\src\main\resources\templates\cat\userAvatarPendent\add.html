<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增头像挂件')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-userAvatarPendent-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">挂件名称：</label>
                <div class="col-sm-8">
                    <input name="name" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">挂件图：</label>
                <div class="col-sm-8">
                    <div class="fileinput fileinput-new" data-provides="fileinput">
                        <div class="fileinput-new thumbnail" style="width: 120px; height: 130px;">
                            <img />
                        </div>
                        <div class="fileinput-preview fileinput-exists thumbnail" style="max-width: 120px; max-height: 130px;"></div>
                        <div>
                            <span class="btn btn-white btn-file"><span class="fileinput-new">选择图片</span><span class="fileinput-exists">更改</span>
                                <input id="iconUrlFile" name="iconUrlFile" class="form-control" type="file" required>
                            </span>
                            <a href="#" class="btn btn-white fileinput-exists" data-dismiss="fileinput">清除</a>
                        </div>
                    </div>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 尺寸：140 x 150</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">效果图：</label>
                <div class="col-sm-8">
                    <!--<input name="thumnail" class="form-control" type="text">-->
                    <div class="fileinput fileinput-new" data-provides="fileinput">
                        <div class="fileinput-new thumbnail" style="width: 120px; height: 130px;">
                            <img />
                        </div>
                        <div class="fileinput-preview fileinput-exists thumbnail" style="max-width: 120px; max-height: 130px;"></div>
                        <div>
                            <span class="btn btn-white btn-file"><span class="fileinput-new">选择图片</span><span class="fileinput-exists">更改</span>
                                <input id="thumnailFile" name="thumnailFile" class="form-control" type="file">
                            </span>
                            <a href="#" class="btn btn-white fileinput-exists" data-dismiss="fileinput">清除</a>
                        </div>
                        <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 尺寸：188 x 188</span>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">分组：</label>
                <div class="col-sm-8">
                    <select name="groupCode" class="form-control m-b" th:with="type=${@dict.getType('cat_avatar_pendent')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">挂件描述：</label>
                <div class="col-sm-8">
                    <input name="describe" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_show_status')}">
                        <input type="radio" th:id="${'status_' + dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">序号：</label>
                <div class="col-sm-8">
                    <input name="sortOn" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: jasny-bootstrap-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/userAvatarPendent"
        $("#form-userAvatarPendent-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                var form = $('#form-userAvatarPendent-add')[0];
                var formdata = new FormData(form);
                $.ajax({
                    url: prefix + "/add",
                    data: formdata,
                    type: "post",
                    processData: false,
                    contentType: false,
                    success: function(result) {
                        $.operate.successCallback(result);
                    }
                })
                //$.operate.save(prefix + "/add", $('#form-userAvatarPendent-add').serialize());
            }
        }
    </script>
</body>
</html>
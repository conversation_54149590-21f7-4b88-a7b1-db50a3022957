<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('头像挂件列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>挂件名称：</p>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <p>分组：</p>
                                <select name="groupCode" th:with="type=${@dict.getType('cat_avatar_pendent')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>状态：</p>
                                <select name="status" th:with="type=${@dict.getType('cat_show_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:userAvatarPendent:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:userAvatarPendent:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:userAvatarPendent:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:userAvatarPendent:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('cat:userAvatarPendent:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:userAvatarPendent:remove')}]];
        var groupCodeDatas = [[${@dict.getType('cat_avatar_pendent')}]];
        var statusDatas = [[${@dict.getType('cat_show_status')}]];
        var prefix = ctx + "cat/userAvatarPendent";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                sortName: "sortOn",
                sortOrder: "asc",
                modalName: "头像挂件",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'id', 
                    title : 'id',
                    visible: false
                },
                {
                    field : 'name', 
                    title : '挂件名称'
                },
                {
                    field : 'iconUrl', 
                    title : '挂件图',
                    formatter: function(value, row, index) {
                        if(value){
                            return $.table.imageView(value);
                        }else {
                            return $.table.imageView('/jeefast.png');
                        }
                    }
                },
                {
                    field : 'thumnail', 
                    title : '效果图',
                    formatter: function(value, row, index) {
                        if(value){
                            return $.table.imageView(value);
                        }else {
                            return $.table.imageView('/jeefast.png');
                        }
                    }
                },
                {
                    field : 'groupCode', 
                    title : '分组',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(groupCodeDatas, value);
                    }
                },
                {
                    field : 'describe', 
                    title : '挂件描述'
                },
                {
                    field : 'status', 
                    title : '状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field : 'sortOn', 
                    title : '序号',
                    sortable: true
                },
                {
                    field : 'createDate', 
                    title : '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增用户信息')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-userInfo-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">用户名：</label>
                <div class="col-sm-8">
                    <input name="userName" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">手机号：</label>
                <div class="col-sm-8">
                    <input name="mobile" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">性别：</label>
                <div class="col-sm-8">
                    <select name="sex" class="form-control m-b" th:with="type=${@dict.getType('cat_user_sex')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">头像：</label>
                <div class="col-sm-8">
                    <input name="avatarUrl" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">等级：</label>
                <div class="col-sm-8">
                    <input name="level" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">年龄：</label>
                <div class="col-sm-8">
                    <input name="age" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">用户简介：</label>
                <div class="col-sm-8">
                    <input name="profiles" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">省：</label>
                <div class="col-sm-8">
                    <input name="province" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">市：</label>
                <div class="col-sm-8">
                    <input name="city" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">经纬度：</label>
                <div class="col-sm-8">
                    <input name="gps" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">魅力值：</label>
                <div class="col-sm-8">
                    <input name="charm" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">罐头克数(g)：</label>
                <div class="col-sm-8">
                    <input name="canNumber" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">微信ID：</label>
                <div class="col-sm-8">
                    <input name="openId" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否官V用户(1：官V ，2：未认证)：</label>
                <div class="col-sm-8">
                    <input name="approve" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">国家：</label>
                <div class="col-sm-8">
                    <input name="country" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">语言：</label>
                <div class="col-sm-8">
                    <input name="language" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">关注人数：</label>
                <div class="col-sm-8">
                    <input name="attentionCount" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">粉丝人数：</label>
                <div class="col-sm-8">
                    <input name="fansCount" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">动态数：</label>
                <div class="col-sm-8">
                    <input name="dynamicCount" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">问答数：</label>
                <div class="col-sm-8">
                    <input name="questionCount" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">获赞数：</label>
                <div class="col-sm-8">
                    <input name="praiseCount" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/userInfo"
        $("#form-userInfo-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-userInfo-add').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
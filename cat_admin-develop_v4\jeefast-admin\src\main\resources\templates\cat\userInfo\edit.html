<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改用户信息')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-userInfo-edit" th:object="${userInfo}">
            <input name="userId" th:field="*{userId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">用户名：</label>
                <div class="col-sm-8">
                    <input name="userName" th:field="*{userName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">手机号：</label>
                <div class="col-sm-8">
                    <input name="mobile" th:field="*{mobile}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">性别：</label>
                <div class="col-sm-8">
                    <select name="sex" class="form-control m-b" th:with="type=${@dict.getType('cat_user_sex')}" required>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{sex}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">头像：</label>
                <div class="col-sm-8">
                    <input name="avatarUrl" th:field="*{avatarUrl}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">等级：</label>
                <div class="col-sm-8">
                    <input name="level" th:field="*{level}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">年龄：</label>
                <div class="col-sm-8">
                    <input name="age" th:field="*{age}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">用户简介：</label>
                <div class="col-sm-8">
                    <input name="profiles" th:field="*{profiles}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">省：</label>
                <div class="col-sm-8">
                    <input name="province" th:field="*{province}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">市：</label>
                <div class="col-sm-8">
                    <input name="city" th:field="*{city}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">经纬度：</label>
                <div class="col-sm-8">
                    <input name="gps" th:field="*{gps}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">魅力值：</label>
                <div class="col-sm-8">
                    <input name="charm" th:field="*{charm}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">罐头克数(g)：</label>
                <div class="col-sm-8">
                    <input name="canNumber" th:field="*{canNumber}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">微信ID：</label>
                <div class="col-sm-8">
                    <input name="openId" th:field="*{openId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">是否官V用户(1：官V ，2：未认证)：</label>
                <div class="col-sm-8">
                    <input name="approve" th:field="*{approve}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">国家：</label>
                <div class="col-sm-8">
                    <input name="country" th:field="*{country}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">语言：</label>
                <div class="col-sm-8">
                    <input name="language" th:field="*{language}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input name="createDate" th:value="${#dates.format(userInfo.createDate, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">关注人数：</label>
                <div class="col-sm-8">
                    <input name="attentionCount" th:field="*{attentionCount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">粉丝人数：</label>
                <div class="col-sm-8">
                    <input name="fansCount" th:field="*{fansCount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">动态数：</label>
                <div class="col-sm-8">
                    <input name="dynamicCount" th:field="*{dynamicCount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">问答数：</label>
                <div class="col-sm-8">
                    <input name="questionCount" th:field="*{questionCount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">获赞数：</label>
                <div class="col-sm-8">
                    <input name="praiseCount" th:field="*{praiseCount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">是否拉黑：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('cat_recommend')}">
                        <input type="radio" th:id="${'isMute_' + dict.dictCode}" name="isMute" th:value="${dict.dictValue}" th:field="*{isMute}" required>
                        <label th:for="${'isMute_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/userInfo";
        $("#form-userInfo-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-userInfo-edit').serialize());
            }
        }

        $("input[name='createDate']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>
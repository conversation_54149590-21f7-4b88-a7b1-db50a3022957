<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('用户充值')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-userInfo-edit" th:object="${userInfo}">
            <input name="userId" th:field="*{userId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">用户名：</label>
                <div class="col-sm-8">
                    <span name="userName" class="toggle-switch" th:text="*{userName}" />
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">罐头g数：</label>
                <div class="col-sm-8">
                    <span class="toggle-switch" th:text="*{canNumber}" />
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">账户余额：</label>
                <div class="col-sm-8">
                    <span class="toggle-switch" th:text="*{catMoney}"  />
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">充值罐头g数：</label>
                <div class="col-sm-8">
                    <input name="canNumber"  class="form-control" type="text" />
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i>不要输入小数点，负数表示扣除，正数增加</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">充值余额：</label>
                <div class="col-sm-8">
                    <input name="catMoney"  class="form-control" type="text" />
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i>可输入2位小数，负数表示扣除，正数增加</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">充值说明：</label>
                <div class="col-sm-8">
                    <input name="remark"  class="form-control" type="text" />
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i>显示在客户端内容</span>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script type="text/javascript">
        var prefix = ctx + "cat/userInfo";
        $("#form-userInfo-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/recharge", $('#form-userInfo-edit').serialize());
            }
        }

    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('用户信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>用户ID：</p>
                                <input type="text" name="userId"/>
                            </li>
                            <li>
                                <p>用户名：</p>
                                <input type="text" name="userName"/>
                            </li>
                            <li>
                                <p>手机号：</p>
                                <input type="text" name="mobile"/>
                            </li>
                            <li>
                                <p>性别：</p>
                                <select name="sex" th:with="type=${@dict.getType('cat_user_sex')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <p>微信ID：</p>
                                <input type="text" name="openId"/>
                            </li>
                            <li>
                                <p>官V用户：</p>
                                <input type="text" name="approve"/>
                            </li>
                            <li class="select-time">
                                <p>创建时间：</p>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="beginCreateDate"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="endCreateDate"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="cat:userInfo:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="cat:userInfo:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="cat:userInfo:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="cat:userInfo:export">
                    <i class="fa fa-download"></i> 导出
                 </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var datas = [[${@dict.getType('cat_user_sex')}]];
        var editFlag = [[${@permission.hasPermi('cat:userInfo:edit')}]];
        var removeFlag = [[${@permission.hasPermi('cat:userInfo:remove')}]];
        var prefix = ctx + "cat/userInfo";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                sortName: "createDate",
                sortOrder: "desc",
                modalName: "用户信息",
                columns: [{
                    checkbox: true
                },
                {
                    field : 'userId', 
                    title : '用户ID',
                    visible: false
                },
                {
                    field : 'userName', 
                    title : '用户名'
                },
                {
                    field : 'avatarUrl',
                    title : '头像',
                    formatter: function(value, row, index) {
                        // 图片预览（注意：如存储在本地直接获取数据库路径，如有配置context-path需要使用ctx+路径）
                        // 如：/profile/upload/2019/08/08/3b7a839aced67397bac694d77611ce72.png
                        if(value){
                            return $.table.imageView(value);
                        }else {
                            return $.table.imageView('/jeefast.png');
                        }
                    }
                },
                {
                    field : 'sex', 
                    title : '性别',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(datas, value);
                    }
                },
                {
                    field : 'level', 
                    title : '等级',
                    sortable: true
                },
                {
                    field : 'age', 
                    title : '年龄',
                    sortable: true
                },
                {
                    field : 'charm', 
                    title : '魅力值',
                    sortable: true
                },
                {
                    field : 'canNumber', 
                    title : '罐头克数(g)',
                    sortable: true
                },
                {
                    field : 'createDate', 
                    title : '创建时间',
                    sortable: true
                },
                {
                    field : 'attentionCount', 
                    title : '关注人数',
                    sortable: true
                },
                {
                    field : 'fansCount', 
                    title : '粉丝人数',
                    sortable: true
                },
                {
                    field : 'dynamicCount', 
                    title : '动态数',
                    sortable: true
                },
                {
                    field : 'questionCount', 
                    title : '问答数',
                    sortable: true
                },
                {
                    field : 'praiseCount', 
                    title : '获赞数',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="rechargeView(\'' + row.userId + '\')"><i class="fa fa-edit"></i>充值</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.userId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.userId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });


        //充值
        function rechargeView(userId){
            $.modal.open("用户充值",prefix+"/rechargeView?id="+userId);
        }



    </script>
</body>
</html>
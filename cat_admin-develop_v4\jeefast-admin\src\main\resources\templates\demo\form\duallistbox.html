<!DOCTYPE html>
<html lang="zh">
<head>
	<th:block th:include="include :: header('左右互选组件')" />
	<th:block th:include="include :: bootstrap-duallistbox-css" />
</head>
<body class="gray-bg">
	<div class="wrapper wrapper-content animated fadeInRight">
        <div class="row">
            <div class="col-lg-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>双重列表框 <small>https://github.com/istvan-ujjmeszaros/bootstrap-duallistbox</small></h5>
                    </div>  
                    <div class="ibox-content">
                        <p>
                            Bootstrap Dual Listbox是针对Twitter Bootstrap进行了优化的响应式双列表框。它适用于所有现代浏览器和触摸设备。
                        </p>

                        <form id="form" action="#" class="wizard-big">
                            <select class="form-control dual_select" multiple>
                                <option value="1">JEEFAST1</option>
                                <option value="2">JEEFAST2</option>
                                <option value="3">JEEFAST3</option>
                                <option selected value="4">JEEFAST4</option>
                                <option selected value="5">JEEFAST5</option>
                                <option value="6">JEEFAST6</option>
                                <option value="7">JEEFAST7</option>
                                <option value="8">JEEFAST8</option>
                                <option value="9">JEEFAST9</option>
                                <option value="10">JEEFAST10</option>
                                <option value="11">JEEFAST11</option>
                                <option value="12">JEEFAST12</option>
                            </select>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-duallistbox-js" />
    <script type="text/javascript">
	    $('.dual_select').bootstrapDualListbox({
	    	nonSelectedListLabel: '未有用户',
            selectedListLabel: '已有用户',
            preserveSelectionOnMove: 'moved',
            moveOnSelect: false,           // 出现一个剪头，表示可以一次选择一个
            filterTextClear: '展示所有',
            moveSelectedLabel: "添加",
            moveAllLabel: '添加所有',
            removeSelectedLabel: "移除",
            removeAllLabel: '移除所有',
            infoText: '共{0}个',
            showFilterInputs: false,       // 是否带搜索
	        selectorMinimalHeight: 160
	    });
    </script>
</body>
</html>

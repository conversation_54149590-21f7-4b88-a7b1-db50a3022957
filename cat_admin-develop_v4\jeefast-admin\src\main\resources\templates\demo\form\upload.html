<!DOCTYPE html>
<html lang="zh">
<head>
	<th:block th:include="include :: header('文件上传')" />
	<th:block th:include="include :: bootstrap-fileinput-css" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>文件上传控件 <small>https://github.com/kartik-v/bootstrap-fileinput</small></h5>
                    </div>
                    <div class="ibox-content">
                    	<div class="form-group">
                            <label class="font-noraml">简单示例</label>
	                        <div class="file-loading">
					            <input class="file" type="file" multiple data-min-file-count="1" data-theme="fas">
					        </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="font-noraml">多文件上传</label>
	                        <div class="file-loading">
					            <input id="fileinput-demo-1" type="file" multiple>
					        </div>
                        </div>
                        <hr>
                        <div class="form-group">
                            <label class="font-noraml">相关参数详细信息</label>
                            <div><a href="http://doc.jeefast.vip/#/standard/zjwd?id=jasny-bootstrap" target="_blank">http://doc.jeefast.vip/#/standard/zjwd?id=jasny-bootstrap</a></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-fileinput-js" />
    <script type="text/javascript">
    $(document).ready(function () {
        $("#fileinput-demo-1").fileinput({
            'theme': 'explorer-fas',
            'uploadUrl': '#',
            dropZoneEnabled:true,
            uploadAsync:false,//false 同步上传，后台用数组接收，true 异步上传，每次上传一个file,会调用多次接口
            /*overwriteInitial: false,
            initialPreviewAsData: true,
            initialPreview: [
                "/img/profile.jpg"
            ]*/
        });

        $("#fileinput-demo-1").on("filebatchselected", function(event, files) {
            console.info("选择文件后处理事件")
        });
        $("#fileinput-demo-1").on("fileuploaded", function(event, files) {
            debugger
            console.info("上传成功后处理方法")
        });
        $('#fileinput-demo-1').on('filebatchuploadsuccess', function(event, data, previewId, index) {
            debugger
            console.info("批量上传成功结果处理")
        });
        $('#fileinput-demo-1').on('filebatchuploaderror', function(event, data, msg) {
            debugger
            console.info("批量上传错误结果处理")
        });
    });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('自定义触发事件')" />
</head>
<body class="gray-bg">
     <div class="container-div">
		<div class="row">
			<div class="col-sm-12 select-table table-striped">
			    <p class="select-title">自定义触发事件（点击某行/双击某行/单击某格/双击某格/服务器发送数据前触发/数据被加载时触发）</p>
				<table id="bootstrap-table" data-mobile-responsive="true"></table>
			</div>
		</div>
	</div>
    <div th:include="include :: footer"></div>
    <script th:inline="javascript">
        var prefix = ctx + "demo/table";
        var datas = [[${@dict.getType('sys_normal_disable')}]];

        $(function() {
            var options = {
                url: prefix + "/list",
		        showSearch: false,
		        showRefresh: false,
		        showToggle: false,
		        showColumns: false,
		        onClickRow: onClickRow,
		        onDblClickRow: onDblClickRow,
		        onClickCell: onClickCell,
		        onDblClickCell: onDblClickCell,
		        responseHandler: responseHandler,
		        onLoadSuccess: onLoadSuccess,
                columns: [{
		            checkbox: true
		        },
				{
					field : 'userId', 
					title : '用户ID'
				},
				{
					field : 'userCode', 
					title : '用户编号'
				},
				{
					field : 'userName', 
					title : '用户姓名'
				},
				{
					field : 'userPhone', 
					title : '用户手机'
				},
				{
					field : 'userEmail', 
					title : '用户邮箱'
				},
				{
				    field : 'userBalance',
				    title : '用户余额'
				},
				{
                    field: 'status',
                    title: '用户状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                    	return $.table.selectDictLabel(datas, value);
                    }
                },
		        {
		            title: '操作',
		            align: 'center',
		            formatter: function(value, row, index) {
		            	var actions = [];
		            	actions.push('<a class="btn btn-success btn-xs" href="#"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="#"><i class="fa fa-remove"></i>删除</a>');
						return actions.join('');
		            }
		        }]
            };
            $.table.init(options);
        });
        
        function onClickRow(row, $element){
        	alert("单击行userId：" + row.userId + " userName：" + row.userName);
        }
        
        function onDblClickRow(row, $element){
        	alert("双击行userId：" + row.userId + " userName：" + row.userName);
        }
        
        function onClickCell(field, value, row, $element){
        	alert("单击格name：" + field + " value：" + value);
        }
        
        function onDblClickCell(field, value, row, $element){
        	alert("双击格name：" + field + " value：" + value);
        }
        
        function responseHandler(res){
        	alert("请求获取数据后处理回调函数");
        }
        
        function onLoadSuccess(data){
        	alert("当所有数据被加载时触发");
        }
    </script>
</body>
</html>
<!-- 通用CSS -->
<head th:fragment=header(title)>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="keywords" content="">
	<meta name="description" content="">
	<title th:text="${title}"></title>
	<link rel="shortcut icon" href="favicon.ico">
	<link th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
	<link th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
	<!-- bootstrap-table 表格插件样式 -->
	<link th:href="@{/ajax/libs/bootstrap-table/bootstrap-table.min.css}" rel="stylesheet"/>
	<link th:href="@{/css/animate.css}" rel="stylesheet"/>
	<link th:href="@{/css/style.css}" rel="stylesheet"/>
	<link th:href="@{/my/css/my-ui.css}" rel="stylesheet"/>
</head>

<!-- 通用JS -->
<div th:fragment="footer">
	<script th:src="@{/js/jquery.min.js}"></script>
	<script th:src="@{/js/bootstrap.min.js}"></script>
	<!-- bootstrap-table 表格插件 -->
	<script th:src="@{/ajax/libs/bootstrap-table/bootstrap-table.min.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/locale/bootstrap-table-zh-CN.min.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/mobile/bootstrap-table-mobile.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/toolbar/bootstrap-table-toolbar.min.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/columns/bootstrap-table-fixed-columns.js}"></script>
	<!-- jquery-validate 表单验证插件 -->
	<script th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
	<script th:src="@{/ajax/libs/validate/messages_zh.min.js}"></script>
	<script th:src="@{/ajax/libs/validate/jquery.validate.extend.js}"></script>
	<!-- jquery-validate 表单树插件 -->
	<script th:src="@{/ajax/libs/bootstrap-treetable/bootstrap-treetable.js}"></script>
	<!-- jquery-export 表格导出插件 -->
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/export/bootstrap-table-export.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/export/tableExport.js}"></script>
	<!-- 遮罩层 -->
	<script th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
    <script th:src="@{/ajax/libs/iCheck/icheck.min.js}"></script>
	<script th:src="@{/ajax/libs/layer/layer.min.js}"></script>
	<script th:src="@{/ajax/libs/layui/layui.js}"></script>
	<script th:src="@{/my/js/common.js?v=2.0}"></script>
	<script th:src="@{/my/js/my-ui.js?v=2.0}"></script>
	<script th:inline="javascript"> var ctx = [[@{/}]]; </script>
</div>

<!-- ztree树插件 -->
<div th:fragment="ztree-css">
    <link th:href="@{/ajax/libs/jquery-ztree/3.5/css/metro/zTreeStyle.css}" rel="stylesheet"/>
</div>
<div th:fragment="ztree-js">
    <script th:src="@{/ajax/libs/jquery-ztree/3.5/js/jquery.ztree.all-3.5.js}"></script>
</div>

<!-- select2下拉框插件 -->
<div th:fragment="select2-css">
    <link th:href="@{/ajax/libs/select2/select2.min.css}" rel="stylesheet"/>
    <link th:href="@{/ajax/libs/select2/select2-bootstrap.css}" rel="stylesheet"/>
</div>
<div th:fragment="select2-js">
    <script th:src="@{/ajax/libs/select2/select2.min.js}"></script>
</div>

<!-- bootstrap-select下拉框插件 -->
<div th:fragment="bootstrap-select-css">
    <link th:href="@{/ajax/libs/bootstrap-select/bootstrap-select.css}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-select-js">
    <script th:src="@{/ajax/libs/bootstrap-select/bootstrap-select.js}"></script>
</div>

<!-- datetimepicker日期和时间插件 -->
<div th:fragment="datetimepicker-css">
    <link th:href="@{/ajax/libs/datapicker/bootstrap-datetimepicker.min.css}" rel="stylesheet"/>
</div>
<div th:fragment="datetimepicker-js">
    <script th:src="@{/ajax/libs//datapicker/bootstrap-datetimepicker.min.js}"></script>
</div>

<!-- ui布局插件 -->
<div th:fragment="layout-latest-css">
    <link th:href="@{/ajax/libs/jquery-layout/jquery.layout-latest.css}" rel="stylesheet"/>
</div>
<div th:fragment="layout-latest-js">
    <script th:src="@{/ajax/libs/jquery-layout/jquery.layout-latest.js}"></script>
</div>

<!-- summernote富文本编辑器插件 -->
<div th:fragment="summernote-css">
    <link th:href="@{/ajax/libs/summernote/summernote.css}" rel="stylesheet"/>
	<link th:href="@{/ajax/libs/summernote/summernote-bs3.css}" rel="stylesheet"/>
</div>
<div th:fragment="summernote-js">
    <script th:src="@{/ajax/libs/summernote/summernote.min.js}"></script>
	<script th:src="@{/ajax/libs/summernote/summernote-zh-CN.js}"></script>
</div>

<!-- cropbox图像裁剪插件 -->
<div th:fragment="cropbox-css">
    <link th:href="@{/ajax/libs/cropbox/cropbox.css}" rel="stylesheet"/>
</div>
<div th:fragment="cropbox-js">
    <script th:src="@{/ajax/libs/cropbox/cropbox.js}"></script>
</div>

<!-- jasny功能扩展插件 -->
<div th:fragment="jasny-bootstrap-css">
    <link th:href="@{/ajax/libs/jasny/jasny-bootstrap.min.css}" rel="stylesheet"/>
</div>
<div th:fragment="jasny-bootstrap-js">
    <script th:src="@{/ajax/libs/jasny/jasny-bootstrap.min.js}"></script>
</div>

<!-- fileinput文件上传插件 -->
<div th:fragment="bootstrap-fileinput-css">
    <link th:href="@{/ajax/libs/bootstrap-fileinput/fileinput.min.css}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-fileinput-js">
    <script th:src="@{/ajax/libs/bootstrap-fileinput/fileinput.min.js}"></script>
</div>

<!-- duallistbox双列表框插件 -->
<div th:fragment="bootstrap-duallistbox-css">
    <link th:href="@{/ajax/libs/duallistbox/bootstrap-duallistbox.min.css}" rel="stylesheet"/>
</div>
<div th:fragment="bootstrap-duallistbox-js">
    <script th:src="@{/ajax/libs/duallistbox/bootstrap-duallistbox.min.js}"></script>
</div>

<!-- suggest搜索自动补全 -->
<div th:fragment="bootstrap-suggest-js">
    <script th:src="@{/ajax/libs/suggest/bootstrap-suggest.min.js}"></script>
</div>

<!-- typeahead搜索自动补全 -->
<div th:fragment="bootstrap-typeahead-js">
    <script th:src="@{/ajax/libs/typeahead/bootstrap3-typeahead.min.js}"></script>
</div>

<!-- jsonview格式化和语法高亮JSON格式数据查看插件 -->
<div th:fragment="jsonview-css">
    <link th:href="@{/ajax/libs/jsonview/jquery.jsonview.css}" rel="stylesheet"/>
</div>
<div th:fragment="jsonview-js">
    <script th:src="@{/ajax/libs/jsonview/jquery.jsonview.js}"></script>
</div>

<!-- jquery.steps表单向导插件 -->
<div th:fragment="jquery-steps-css">
    <link th:href="@{/ajax/libs/staps/jquery.steps.css}" rel="stylesheet"/>
</div>
<div th:fragment="jquery-steps-js">
    <script th:src="@{/ajax/libs/staps/jquery.steps.min.js}"></script>
</div>

<!-- ECharts百度统计图表插件 -->
<div th:fragment="echarts-js">
    <script th:src="@{/ajax/libs/report/echarts/echarts-all.js}"></script>
	<script src="https://cdn.jsdelivr.net/gh/apache/echarts-website@asf-site/v4/zh/asset/theme/macarons.js?_v_=20200710_1"></script>
</div>

<!-- peity图表组合插件 -->
<div th:fragment="peity-js">
    <script th:src="@{/ajax/libs/report/peity/jquery.peity.min.js}"></script>
</div>

<!-- sparkline线状图插件 -->
<div th:fragment="sparkline-js">
    <script th:src="@{/ajax/libs/report/sparkline/jquery.sparkline.min.js}"></script>
</div>

<!-- 表格拖拽插件 -->
<div th:fragment="bootstrap-table-reorder-js">
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder/bootstrap-table-reorder.js}"></script>
	<script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder/jquery.tablednd.js}"></script>
</div>
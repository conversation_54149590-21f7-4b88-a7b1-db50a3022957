<!DOCTYPE html>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
    
    <title>悦尔.管理平台</title>
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/style.css" th:href="@{/css/style.css}" rel="stylesheet"/>
    <link href="../static/css/login.min.css" th:href="@{/css/login.min.css}" rel="stylesheet"/>
    <link href="../static/my/css/my-ui.css" th:href="@{/my/css/my-ui.css?v=2.0}" rel="stylesheet"/>
    <!--[if lt IE 9]>
    <meta http-equiv="refresh" content="0;ie.html" />
    <![endif]-->
    <link rel="shortcut icon" href="../static/favicon.ico" th:href="@{favicon.ico}"/>
    <style type="text/css">label.error { position:inherit;  }</style>
    <script>
        if(window.top!==window.self){window.top.location=window.location};
    </script>
</head>

<body class="signin">

    <div class="signinpanel">
        <div class="row">
            <div class="col-sm-6">
                <!-- <div class="signin-info">
                    <div class="logopanel m-b">
                        <h1><img alt="[ JeeFast ]" src="../static/jeefast.png" th:src="@{/jeefast.png}"></h1>
                        <h1>JeeFast</h1>
                    </div>
                    <div class="m-b"></div>
                    <h4>欢迎使用 <strong>JeeFast 管理平台</strong></h4>
                    <ul class="m-b">
                        <li><i class="fa fa-arrow-circle-o-right m-r-xs"></i> 可以跨库构建任意数据结构的数据模型</li>
                        <li><i class="fa fa-arrow-circle-o-right m-r-xs"></i> 轻松的生成数据模型的容器-表单，并进行一个业务流转</li>
                        <li><i class="fa fa-arrow-circle-o-right m-r-xs"></i> 高效的流程任务处理能力</li>
                        <li><i class="fa fa-arrow-circle-o-right m-r-xs"></i> 稳健低耦合的流程引擎核心</li>
                        <li><i class="fa fa-arrow-circle-o-right m-r-xs"></i> 强大丰富的表单组件支持，快速构建所需的业务应用</li>
                        <li><i class="fa fa-arrow-circle-o-right m-r-xs"></i> 多环境、多数据源真实复杂场景的支持</li>
                        <li><i class="fa fa-arrow-circle-o-right m-r-xs"></i> 基于基础组件,未来更多应用的可贡献性、可共享性</li>
                    </ul>
                    <strong>还没有账号？ <a href="#">立即注册&raquo;</a></strong>
                </div> -->
<!--                <img alt="[ JeeFast ]" src="../static/jeefast.png" th:src="@{/jeefast.png}">-->
            </div>
            <div class="col-sm-6">
                <form id="signupForm">
                    <h1 class="no-margins">悦尔.管理平台</h1>
                    <p class="m-t-md">快速开发.让研发自由简单</p>
                    <input type="text"     name="username" class="form-control uname"     placeholder="用户名" value=""    />
                    <input type="password" name="password" class="form-control pword"     placeholder="密码"   value="" />
					<div class="row m-t" th:if="${captchaEnabled==true}">
						<div class="col-xs-7">
						    <input type="text" name="validateCode" class="form-control code" placeholder="验证码" maxlength="5" autocomplete="off">
						</div>
						<div class="col-xs-5">
							<a href="javascript:void(0);" title="点击更换验证码">
								<img th:src="@{captcha/captchaImage(type=${captchaType})}" class="imgcode" width="85%"/>
							</a>
						</div>
					</div>
                    <div class="checkbox-custom" th:classappend="${captchaEnabled==false} ? 'm-t'">
				        <input type="checkbox" id="rememberme" name="rememberme"> <label for="rememberme">记住我</label>
				    </div>
                    <button class="btn btn-success btn-block" id="btnSubmit" data-loading="正在验证登录，请稍后...">登录</button>
                </form>
            </div>
        </div>
        <div class="signup-footer">
            <div class="pull-left">
                &copy; 2019 All Rights Reserved. youmao <br>
            </div>
        </div>
    </div>
<script th:inline="javascript"> var ctx = [[@{/}]]; var captchaType = [[${captchaType}]]; </script>
<!-- 全局js -->
<script src="../static/js/jquery.min.js" th:src="@{/js/jquery.min.js}"></script>
<script src="../static/js/bootstrap.min.js" th:src="@{/js/bootstrap.min.js}"></script>
<!-- 验证插件 -->
<script src="../static/ajax/libs/validate/jquery.validate.min.js" th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
<script src="../static/ajax/libs/validate/messages_zh.min.js" th:src="@{/ajax/libs/validate/messages_zh.min.js}"></script>
<script src="../static/ajax/libs/layer/layer.min.js" th:src="@{/ajax/libs/layer/layer.min.js}"></script>
<script src="../static/ajax/libs/blockUI/jquery.blockUI.js" th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
<script src="../static/my/js/my-ui.js" th:src="@{/my/js/my-ui.js?v=2.0}"></script>
<script src="../static/my/login.js" th:src="@{/my/login.js}"></script>
</body>
</html>

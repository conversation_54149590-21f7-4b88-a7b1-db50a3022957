<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计</title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/main/animate.min.css" th:href="@{/css/main/animate.min.css}" rel="stylesheet"/>
    <link href="../static/css/main/style.min862f.css" th:href="@{/css/main/style.min862f.css}" rel="stylesheet"/>
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content">

        <div class="row">
            <!--<div class="col-sm-3">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <span class="label label-success pull-right">月</span>
                        <h5>收入</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 class="no-margins">40 886,200</h1>
                        <div class="stat-percent font-bold text-success">98% <i class="fa fa-bolt"></i>
                        </div>
                        <small>总收入</small>
                    </div>
                </div>
            </div>-->
            <div class="col-sm-3">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <span class="label label-info pull-right">全年</span>
                        <h5>新增内容</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 class="no-margins" id="yearDynamicCut">正在处理中</h1>
                        <!--<div class="stat-percent font-bold text-info">20% <i class="fa fa-level-up"></i>
                        </div>-->
                        <small>新内容：<small id="nowDynamicCut">正在处理中</small></small>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <span class="label label-primary pull-right" id="nowPvLabel">今天</span>
                        <h5>启动次数</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 class="no-margins" id="nowPv">正在处理中</h1>
                        <div class="stat-percent font-bold text-navy" id="nowPvColor"><i id="nowPvTxt">44%</i> <i id="nowPvLevel" class="fa fa-level-up"></i>
                        </div>
                        <small>昨日：<small id="yesterdayPv">正在处理中</small></small>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <span class="label label-primary pull-right" id="nowUvLabel">今天</span>
                        <h5>活跃设备</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 class="no-margins" id="nowUv">正在处理中</h1>
                        <div class="stat-percent font-bold text-danger" id="nowUvColor"><i id="nowUvTxt">44%</i> <i id="nowUvLevel" class="fa fa-level-up"></i>
                        </div>
                        <small>昨日：<small id="yesterdayUv">正在处理中</small></small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5 class="active-devices">每日活跃设备与注册数量</h5>
                        <!--<div class="pull-right">
                            <div class="btn-group">
                                <button type="button" class="btn btn-xs btn-white active">天</button>
                                <button type="button" class="btn btn-xs btn-white">月</button>
                                <button type="button" class="btn btn-xs btn-white">年</button>
                            </div>
                        </div>-->
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-9" style="padding-bottom: 26px">
                                <div class="flot-chart">
                                    <div class="echarts" id="userTrendChart"></div>
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="row" style="margin-top: 26px">
                                    <div class="col-sm-5">
                                        <h1 class="no-margins" id="userSum" >0</h1>
                                        <h4 style="margin-top: 10px">用户总数</h4>
                                    </div>
                                    <div class="col-sm-5">
                                        <h1 class="no-margins " id="monthUserCount">0</h1>
                                        <h4 style="margin-top: 10px">本月注册用户</h4>
                                    </div>
                                </div>
                                <div class="row" style="margin-top: 40px">
                                    <div class="col-sm-5">
                                        <h1 class="no-margins " id="yesterdayUserCount">0</h1>
                                        <h4 style="margin-top: 10px">昨日注册用户</h4>
                                    </div>
                                    <div class="col-sm-5">
                                        <h1 class="no-margins " id="dayUserCount">0</h1>
                                        <h4 style="margin-top: 10px">今日注册用户</h4>
                                    </div>
                                </div>
                                <!--<ul class="stat-list">
                                    <li>
                                        <h1 class="no-margins" id="userSum" >8,346</h1>
                                        <h3>用户总数</h3>
                                        &lt;!&ndash;<div class="stat-percent">48% <i class="fa fa-level-up text-navy"></i>
                                        </div>&ndash;&gt;
                                        &lt;!&ndash;<div class="progress progress-mini">
                                            <div style="width: 48%;" class="progress-bar"></div>
                                        </div>&ndash;&gt;
                                    </li>
                                    <li>
                                        <h1 class="no-margins " id="monthUserCount">422</h1>
                                        <h3>本月注册用户</h3>
                                        &lt;!&ndash;<div class="stat-percent">60% <i class="fa fa-level-down text-navy"></i>
                                        </div>&ndash;&gt;
                                        &lt;!&ndash;<div class="progress progress-mini">
                                            <div style="width: 60%;" class="progress-bar"></div>
                                        </div>&ndash;&gt;
                                    </li>
                                    <li>
                                        <h1 class="no-margins " id="dayUserCount">2,180</h1>
                                        <h3>今日注册用户</h3>
                                        &lt;!&ndash;<div class="stat-percent">22% <i class="fa fa-bolt text-navy"></i>
                                        </div>&ndash;&gt;
                                        &lt;!&ndash;<div class="progress progress-mini">
                                            <div style="width: 22%;" class="progress-bar"></div>
                                        </div>&ndash;&gt;
                                    </li>
                                 </ul>-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>设备平台统计</h5>
                        <div class="pull-right">
                            <div class="btn-group">
                                <button id="selPieChartDay" type="button" class="btn btn-xs btn-white" onclick="dataPieChart('day')">天</button>
                                <button id="selPieChartMoon" type="button" class="btn btn-xs btn-white" onclick="dataPieChart('moon')">月</button>
                                <button id="selPieChartYear" type="button" class="btn btn-xs btn-white" onclick="dataPieChart('year')">年</button>
                            </div>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div  class="echarts col-xs-6" id="platformPieChart"></div>
                            <div  class="echarts col-xs-6" id="versionPieChart"></div>
                        </div>
                        <div class="row">
                            <div  class="echarts col-xs-6" id="brandPieChart"></div>
                            <div  class="echarts col-xs-6" id="channelPieChart"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



    </div>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <script th:src="@{/ajax/libs/flot/jquery.flot.js}"></script>
    
    <th:block th:include="include :: sparkline-js" />
    <th:block th:include="include :: echarts-js" />
    <script type="text/javascript">
	    $(document).ready(function () {
	        /*var data2 = [
	            [gd(2012, 1, 1), 7], [gd(2012, 1, 2), 6], [gd(2012, 1, 3), 4], [gd(2012, 1, 4), 8],
	            [gd(2012, 1, 5), 9], [gd(2012, 1, 6), 7], [gd(2012, 1, 7), 5], [gd(2012, 1, 8), 4],
	            [gd(2012, 1, 9), 7], [gd(2012, 1, 10), 8], [gd(2012, 1, 11), 9], [gd(2012, 1, 12), 6],
	            [gd(2012, 1, 13), 4], [gd(2012, 1, 14), 5], [gd(2012, 1, 15), 11], [gd(2012, 1, 16), 8],
	            [gd(2012, 1, 17), 8], [gd(2012, 1, 18), 11], [gd(2012, 1, 19), 11], [gd(2012, 1, 20), 6],
	            [gd(2012, 1, 21), 6], [gd(2012, 1, 22), 8], [gd(2012, 1, 23), 11], [gd(2012, 1, 24), 13],
	            [gd(2012, 1, 25), 7], [gd(2012, 1, 26), 9], [gd(2012, 1, 27), 9], [gd(2012, 1, 28), 8],
	            [gd(2012, 1, 29), 5], [gd(2012, 1, 30), 8], [gd(2012, 1, 31), 25]
	        ];
	
	        var data3 = [
	            [gd(2012, 1, 1), 800], [gd(2012, 1, 2), 500], [gd(2012, 1, 3), 600], [gd(2012, 1, 4), 700],
	            [gd(2012, 1, 5), 500], [gd(2012, 1, 6), 456], [gd(2012, 1, 7), 800], [gd(2012, 1, 8), 589],
	            [gd(2012, 1, 9), 467], [gd(2012, 1, 10), 876], [gd(2012, 1, 11), 689], [gd(2012, 1, 12), 700],
	            [gd(2012, 1, 13), 500], [gd(2012, 1, 14), 600], [gd(2012, 1, 15), 700], [gd(2012, 1, 16), 786],
	            [gd(2012, 1, 17), 345], [gd(2012, 1, 18), 888], [gd(2012, 1, 19), 888], [gd(2012, 1, 20), 888],
	            [gd(2012, 1, 21), 987], [gd(2012, 1, 22), 444], [gd(2012, 1, 23), 999], [gd(2012, 1, 24), 567],
	            [gd(2012, 1, 25), 786], [gd(2012, 1, 26), 666], [gd(2012, 1, 27), 888], [gd(2012, 1, 28), 900],
	            [gd(2012, 1, 29), 178], [gd(2012, 1, 30), 555], [gd(2012, 1, 31), 993]
	        ];
	
	
	        var dataset = [
	            {
	                label: "用户数",
	                data: data3,
	                color: "#1ab394",
	                bars: {
	                    show: true,
	                    align: "center",
	                    barWidth: 24 * 60 * 60 * 600,
	                    lineWidth: 0
	                }
	
	            }, {
	                label: "昨日数",
	                data: data2,
	                yaxis: 2,
	                color: "#464f88",
	                lines: {
	                    lineWidth: 1,
	                    show: true,
	                    fill: true,
	                    fillColor: {
	                        colors: [{
	                            opacity: 0.2
	                        }, {
	                            opacity: 0.2
	                        }]
	                    }
	                },
	                splines: {
	                    show: false,
	                    tension: 0.6,
	                    lineWidth: 1,
	                    fill: 0.1
	                },
	            }
	        ];
	
	
	        var options = {
	            xaxis: {
	                mode: "time",
	                tickSize: [3, "day"],
	                tickLength: 0,
	                axisLabel: "Date",
	                axisLabelUseCanvas: true,
	                axisLabelFontSizePixels: 12,
	                axisLabelFontFamily: 'Arial',
	                axisLabelPadding: 10,
	                color: "#838383"
	            },
	            yaxes: [{
	                    position: "left",
	                    max: 1070,
	                    color: "#838383",
	                    axisLabelUseCanvas: true,
	                    axisLabelFontSizePixels: 12,
	                    axisLabelFontFamily: 'Arial',
	                    axisLabelPadding: 3
	            }, {
	                    position: "right",
	                    clolor: "#838383",
	                    axisLabelUseCanvas: true,
	                    axisLabelFontSizePixels: 12,
	                    axisLabelFontFamily: ' Arial',
	                    axisLabelPadding: 67
	            }
	            ],
	            legend: {
	                noColumns: 1,
	                labelBoxBorderColor: "#000000",
	                position: "nw"
	            },
	            grid: {
	                hoverable: false,
	                borderWidth: 0,
	                color: '#838383'
	            }
	        };
	
	        function gd(year, month, day) {
	            return new Date(year, month - 1, day).getTime();
	        }
	
	        var previousPoint = null,
	            previousLabel = null;
	
	        $.plot($("#flot-dashboard-chart"), dataset, options);*/
            //头部数据统计
            topData();
            //用户设备饼图
            dataPieChart("moon");
            //用户使用趋势图
            userTrendChart("moon");
            //用户总数
            userSum();
	    });

        function topData(){
            //头部统计数
            var config = {
                url: "/stats/dayVisit",
                type: "post",
                dataType: "json",
                data: "",
                beforeSend: function () {

                },
                success: function(result) {
                    if(0==result.code){
                        var data = result.data;
                        var nowUv = data.nowData.uv;
                        var nowpv = data.nowData.pv;
                        var yesterdayUv = data.yesterdayData.uv;
                        var yesterdayPv = data.yesterdayData.pv;
                        if (nowpv<yesterdayPv){
                            $("#nowPvLabel").removeClass("label-primary");
                            $("#nowPvLabel").addClass("label-danger");
                            $("#nowPvColor").removeClass("text-navy");
                            $("#nowPvColor").addClass("text-danger");
                            $("#nowPvLevel").removeClass("fa-level-up");
                            $("#nowPvLevel").addClass("fa-level-down");
                        }
                        if (nowUv<yesterdayUv){
                            $("#nowUvLabel").removeClass("label-primary");
                            $("#nowUvLabel").addClass("label-danger");
                            $("#nowUvColor").removeClass("text-navy");
                            $("#nowUvColor").addClass("text-danger");
                            $("#nowUvLevel").removeClass("fa-level-up");
                            $("#nowUvLevel").addClass("fa-level-down");
                        }
                        $("#nowUv").html(nowUv);
                        $("#nowPv").html(nowpv);
                        $("#yesterdayUv").html(yesterdayUv);
                        $("#yesterdayPv").html(yesterdayPv);

                        //全年新增内容
                        $("#yearDynamicCut").html(data.nowData.yearDynamicCut);
                        $("#nowDynamicCut").html(data.nowData.nowDynamicCut);

                        $("#nowPvTxt").html((Math.abs(nowpv-yesterdayPv)/yesterdayPv*100).toFixed(2)+"%");
                        $("#nowUvTxt").html((Math.abs(nowUv-yesterdayUv)/yesterdayUv*100).toFixed(2)+"%");
                    }else{
                        alert("请求统计错误")
                    }
                }
            };
            $.ajax(config)
        }

        function dataPieChart(scope){
            if ("day"==scope){
                $("#selPieChartMoon").removeClass("active");
                $("#selPieChartYear").removeClass("active");
                $("#selPieChartDay").addClass("active");
            }
            if ("moon"==scope){
                $("#selPieChartDay").removeClass("active");
                $("#selPieChartYear").removeClass("active");
                $("#selPieChartMoon").addClass("active");
            }
            if ("year"==scope){
                $("#selPieChartDay").removeClass("active");
                $("#selPieChartMoon").removeClass("active");
                $("#selPieChartYear").addClass("active");
            }
            platformPieChart(scope)
            versionPieChart(scope)
            brandPieChart(scope)
            channelPieChart(scope)
        }


        //设备活跃度统计
        function platformPieChart(scope){
            var config = {
                url: "/stats/platformPieChart",
                type: "post",
                dataType: "json",
                data: "scope="+scope,
                beforeSend: function () {

                },
                success: function(result) {
                    if(0==result.code){
                        var lineChart = echarts.init(document.getElementById("platformPieChart"),"macarons");
                        var option = {
                            title: {
                                text: '设备平台',
                                //subtext: '纯属虚构',
                                left: 'center'
                            },
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b} : {c} ({d}%)'
                            },
                            legend: {
                                orient: 'vertical',
                                left: 'left',
                            },
                            series: [
                                {
                                    name: '设备平台',
                                    type: 'pie',
                                    radius: '55%',
                                    /*center: ['50%', '60%'],*/
                                    radius: ['40%', '60%'],
                                    data: result.data,
                                    emphasis: {
                                        itemStyle: {
                                            shadowBlur: 10,
                                            shadowOffsetX: 0,
                                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                                        }
                                    }
                                }
                            ]
                        };
                        lineChart.setOption(option);
                        $(window).resize(lineChart.resize);
                    }else{
                        alert("请求统计错误")
                    }
                }
            };
            $.ajax(config)
        }

        //版本号活跃度统计
        function versionPieChart(scope){
            var config = {
                url: "/stats/versionPieChart",
                type: "post",
                dataType: "json",
                data: "scope="+scope,
                beforeSend: function () {

                },
                success: function(result) {
                    if(0==result.code){
                        var versionPieChart = echarts.init(document.getElementById("versionPieChart"),"macarons");
                        var option = {
                            title: {
                                text: '应用版本号',
                                //subtext: '纯属虚构',
                                left: 'center'
                            },
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b} : {c} ({d}%)'
                            },
                            legend: {
                                orient: 'vertical',
                                left: 'left',
                            },
                            series: [
                                {
                                    name: '应用版本号',
                                    type: 'pie',
                                    radius: '55%',
                                    // center: ['50%', '60%'],
                                    radius: ['40%', '60%'],
                                    data: result.data,
                                }
                            ]
                        };
                        versionPieChart.setOption(option);
                        $(window).resize(versionPieChart.resize);
                    }else{
                        alert("请求统计错误")
                    }
                }
            };
            $.ajax(config)
        }

        //手机品牌活跃度统计
        function brandPieChart(scope){
            var config = {
                url: "/stats/brandPieChart",
                type: "post",
                dataType: "json",
                data: "scope="+scope,
                beforeSend: function () {

                },
                success: function(result) {
                    if(0==result.code){
                        var lineChart = echarts.init(document.getElementById("brandPieChart"),"macarons");
                        var option = {
                            title: {
                                text: '手机品牌',
                                //subtext: '纯属虚构',
                                left: 'center'
                            },
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b} : {c} ({d}%)'
                            },
                            legend: {
                                // top: 'bottom',
                                // textStyle: { //图例文字的样式
                                //     fontSize: 10
                                // },
                                orient: 'vertical',
                                left: 'left',
                                textStyle: { //图例文字的样式
                                    fontSize: 8
                                },
                            },
                            series: [
                                {
                                    name: '手机品牌',
                                    type: 'pie',
                                    radius: ['40%', '70%'],
                                    avoidLabelOverlap: false,
                                    label: {
                                        show: false,
                                        position: 'center'
                                    },
                                    emphasis: {
                                        label: {
                                            show: true,
                                            fontSize: '40',
                                            fontWeight: 'bold'
                                        }
                                    },
                                    labelLine: {
                                        show: false
                                    },
                                    data: result.data,
                                }
                            ]
                        };
                        lineChart.setOption(option);
                        $(window).resize(lineChart.resize);
                    }else{
                        alert("请求统计错误")
                    }
                }
            };
            $.ajax(config)
        }

        //来源渠道活跃度统计
        function channelPieChart(scope){
            var config = {
                url: "/stats/channelPieChart",
                type: "post",
                dataType: "json",
                data: "scope="+scope,
                beforeSend: function () {

                },
                success: function(result) {
                    if(0==result.code){
                        var lineChart = echarts.init(document.getElementById("channelPieChart"),"macarons");
                        var option = {
                            title: {
                                text: '来源渠道',
                                left: 'center'
                            },
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b} : {c} ({d}%)'
                            },
                            legend: {
                                orient: 'vertical',
                                left: 'left',
                                textStyle: { //图例文字的样式
                                    fontSize: 10
                                },
                            },
                            series: [
                                {
                                    name: '手机品牌',
                                    type: 'pie',
                                    radius: ['40%', '70%'],
                                    data: result.data,
                                }
                            ]
                        };
                        lineChart.setOption(option);
                        $(window).resize(lineChart.resize);
                    }else{
                        alert("请求统计错误")
                    }
                }
            };
            $.ajax(config)
        }

        function userTrendChart(scope){
            var config = {
                url: "/stats/userTrendChart",
                type: "post",
                dataType: "json",
                data: "scope="+scope,
                beforeSend: function () {

                },
                success: function(result) {
                    if(0==result.code){
                        var chartDom = echarts.init(document.getElementById("userTrendChart"),"macarons");
                        var option;
                        var data = result.data;
                        option = {
                            legend: {
                                data: ['每日活跃设备数', '每日注册用户数'],
                                textStyle:{
                                    fontSize:14
                                }
                            },
                            grid:{
                                left: 50,  // 左侧留白
                                right: 50,  // 右侧留白
                                bottom: 20, // 下方留白
                                top: 20,   // 上方留白
                            },
                            tooltip: {
                                trigger: 'axis',
                            },
                            xAxis: {
                                type: 'category',
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: '#838383'
                                    }
                                },
                                axisLine:{
                                    lineStyle:{
                                        color:'#838383',
                                    }
                                },
                                data: data.xAxis
                            },
                            yAxis: {
                                type: 'value',
                                scale: true,
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: '#838383'
                                    }
                                },
                                axisLine:{
                                    lineStyle:{
                                        color:'#838383',
                                    }
                                },
                            },
                            series: [
                                {
                                    name: '每日活跃设备数',
                                    symbolSize: 10,//设定实心点的大小
                                    cursor:"default",
                                    data: data.series,
                                    type: 'line',
                                    areaStyle: {
                                        normal: {
                                            color: new echarts.graphic.LinearGradient(
                                                0,
                                                0,
                                                0,
                                                1,
                                                [
                                                    {
                                                        offset: 0,
                                                        color: 'rgb(237,109,52)',
                                                    },
                                                    {
                                                        offset: 1,
                                                        color: 'rgba(237,109,52,0.19)',
                                                    },
                                                ],
                                                false
                                            ),
                                        },
                                    },
                                    itemStyle:{
                                        normal: {
                                            color: 'rgb(199,97,51)',
                                        },
                                    }
                                },
                                {
                                    name: '每日注册用户数',
                                    symbolSize: 10,//设定实心点的大小
                                    cursor:"default",
                                    data: data.register,
                                    type: 'line',
                                    areaStyle: {
                                        normal: {
                                            color: new echarts.graphic.LinearGradient(
                                                0,
                                                0,
                                                0,
                                                1,
                                                [
                                                    {
                                                        offset: 0,
                                                        color: 'rgb(69,189,207)',
                                                    },
                                                    {
                                                        offset: 1,
                                                        color: 'rgba(69,189,207,0.24)',
                                                    },
                                                ],
                                                false
                                            ),
                                        },
                                    },
                                    itemStyle:{
                                        normal: {
                                            color: 'rgb(62,169,185)',
                                        },
                                    }
                                }
                            ]
                        };
                        chartDom.setOption(option);
                        $(window).resize(chartDom.resize);
                    }else{
                        alert("请求用户趋势图错误")
                    }
                }
            };
            $.ajax(config)
        }



        //用户总数
        function userSum(){
            var config = {
                url: "/stats/userSum",
                type: "post",
                dataType: "json",
                data: "scope=",
                beforeSend: function () {

                },
                success: function(result) {
                    if(0==result.code){
                        var data = result.data;
                        $("#userSum").html(data.userSum);
                        $("#monthUserCount").html(data.monthUserCount);
                        $("#dayUserCount").html(data.dayUserCount);
                        $("#yesterdayUserCount").html(data.yesterdayCount);

                    }else{
                        alert("请求用户统计错误")
                    }
                }
            };
            $.ajax(config)
        }

    </script>
</body>
</html>

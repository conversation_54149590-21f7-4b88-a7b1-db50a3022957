<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增指标')"/>
    <th:block th:include="include :: datetimepicker-css"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: select2-css" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="white-bg">

<div class="tabs-container">
    <ul class="nav nav-tabs">
        <li class="active"><a data-toggle="tab" href="#tab-1" aria-expanded="true">基础信息</a></li>
        <li id="alias" class=""><a data-toggle="tab" href="#tab-2" aria-expanded="true">别名信息</a></li>
    </ul>
    <div class="tab-content">
        <div id="tab-1" class="tab-pane active">
            <!--基础信息-->
            <form class="form-horizontal m" id="form-ele-add">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">项目名称：</label>
                        <div class="col-sm-8">
                            <select name="projectLabel" class="form-control m-b"
                                    th:with="type=${@dict.getType('project_label')}" required>
                                <option th:each="dict: ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">指标显示名称：</label>
                        <div class="col-sm-9">
                            <input type="text" name="eleMaster" class="form-control" placeholder="请输入文本">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">指标编码：</label>
                        <div class="col-sm-9">
                            <input type="text" name="eleName" class="form-control" placeholder="请输入文本">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">指标类型：</label>
                        <div class="col-sm-8">
                            <div class="col-sm-8">
                                <select name="eleType" class="form-control m-b"
                                        th:with="type=${@dict.getType('ele_type')}" required>
                                    <option th:each="dict: ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">指标备注：</label>
                        <div class="col-sm-9">
                            <input type="text" name="eleRemarks" class="form-control" placeholder="请输入文本">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">是否启用：</label>
                        <div class="col-sm-8">
                            <div class="radio-box" th:each="dict : ${@dict.getType('is_start')}">
                                <input type="radio" th:id="${dict.dictCode}" name="isStart" th:value="${dict.dictValue}"
                                       th:checked="${dict.default}">
                                <label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">标准单位：</label>
                        <div class="col-sm-9">
                            <input type="text" name="baseUnit" class="form-control" placeholder="请输入文本">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">变体单位列表：</label>
                        <div class="col-sm-9">
                            <textarea type="text" name="variantUnitList" class="form-control"
                                      placeholder="请输入文本"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">参考值：</label>
                        <div class="col-sm-9">
                            <input type="text" name="baseReference" class="form-control" placeholder="请输入文本">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">参考值-展示：</label>
                        <div class="col-sm-9">
                            <input type="text" name="baseReferenceShow" class="form-control" placeholder="请输入文本">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">图标url：</label>
                        <div class="col-sm-9">
                            <input type="text" name="imageUrl" class="form-control" placeholder="请输入文本" value="https://api.weviva.com.cn/profile/default_nurse.png">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">审核状态：</label>
                        <div class="col-sm-8">
                            <div class="radio-box" th:each="dict : ${@dict.getType('is_check')}">
                                <input type="radio" th:id="${dict.dictCode}" name="isCheck" th:value="${dict.dictValue}"
                                       th:checked="${dict.default}">
                                <label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">分类：</label>
                        <div class="col-sm-8">
                            <label>
                                <select name="reportType" class="form-control m-b"
                                        th:with="type=${@dict.getType('ele_report_type')}" required>
                                    <option th:each="dict: ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"></option>
                                </select>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">是否重点指标：</label>
                        <div class="col-sm-8">
                            <div class="radio-box" th:each="dict : ${@dict.getType('sys_yes_no')}">
                                <input type="radio" th:id="${dict.dictCode}" name="isImportance" th:value="${dict.dictValue}"
                                       th:checked="${dict.default}">
                                <label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">关联异常描述：</label>
                        <div class="col-sm-9">
                            <textarea type="text" name="exceptionDesc" class="form-control"
                                      placeholder="请输入文本"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">偏离程度-偏低：</label>
                        <div class="col-sm-9">
                            <textarea type="text" name="lowSide" class="form-control"
                                      placeholder="请输入文本"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">偏离程度-偏高：</label>
                        <div class="col-sm-9">
                            <textarea type="text" name="highSide" class="form-control"
                                      placeholder="请输入文本"></textarea>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">潜在健康风险(严重程度)：</label>
                        <div class="col-sm-9">
                            <div class="col-sm-9">
                                <input type="text" name="healthRisk" class="form-control" placeholder="请输入文本">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">复查期限：</label>
                        <div class="col-sm-9">
                            <input type="text" name="reviewDate" class="form-control" placeholder="请输入文本">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">系统名称：</label>
                        <div class="col-sm-9" >
                            <label style="width: 100%">
                                <select name="systemNameList" data-width="100%" id="businessTypes" th:with="type=${@dict.getType('system_of_systems_name')}" class="selectpicker" data-none-selected-text="请选择" multiple>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </label>
                        </div>
                    </div>

                </div>
            </form>
        </div>
        <div id="tab-2" class="tab-pane">
            <div class="col-md-12 form-group">
                <div style="color: #761c19; font-size: 20px; text-align: center">多个别名,套餐名按照英文;进行隔开(A;B;C)</div>
            </div>
            <form class="form-horizontal m" id="form-alias-add">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">别名名称：</label>
                        <div class="col-sm-9">
                            <input type="text" name="aliasName" class="form-control" placeholder="请输入文本">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">套餐名称：</label>
                        <div class="col-sm-9">
                            <input type="text" name="projectLabel" class="form-control" placeholder="请输入文本">
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: datetimepicker-js"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: select2-js" />
<th:block th:include="include :: bootstrap-select-js" />
<script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.config.js"></script>
<script type="text/javascript" charset="utf-8" src="/ueditor/ueditor.all.min.js"></script>
<script type="text/javascript">
    var ue = UE.getEditor('editor');
    //动态媒体url列表
    var prefix = ctx + "report/nurseBill"
    $("#form-ele-add").validate({
        focusCleanup: true
    });
    var ele_alise_list = []

    function submitHandler() {
        if ($.validate.form()) {
            var obj = $('#form-ele-add').serializeObject();
            var alias = $('#form-alias-add').serializeObject();
            var add_alias_list = []
            var aliasName = alias.aliasName
            var projectLabel = alias.projectLabel
            // 获取表单数据并转换为对象
            if ('string' == typeof obj.systemNameList) {
                obj.systemNameList = [obj.systemNameList];
            }
            if (aliasName !== '' && projectLabel !== ''){
                var alias_list = aliasName.split(';');
                var project_list = projectLabel.split(';');
                for (var i=0; i< alias_list.length; i++){
                    for (var j=0; j<project_list.length; j++){
                        add_alias_list.push({aliasName: alias_list[i], projectLabel: project_list[j]})
                    }
                }
            }
            obj.check = obj.check == "1"? true: false
            obj.eleAliasInfoList = add_alias_list
            console.log(obj)
            //formdata.append("dynamicMediaList[]",dynamicMediaList);
            $.ajax({
                url: prefix + "/add",
                data: JSON.stringify(obj),
                type: "post",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                success: function (result) {
                    $.operate.successCallback(result);
                }
            })
            //$.operate.save(prefix + "/add", obj);
        }
    }

    $("input[name='createDate']").datetimepicker({
        format: "yyyy-mm-dd hh:mm:ss",
        // minView: "month",
        autoclose: true
    });


    /**
     * 将Form的数据转化成Javascript的Json对象
     */
    $.fn.serializeObject = function () {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function () {
            if (o[this.name] !== undefined) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    }
    $('#type').change(function () {
        // 当输入框内容修改时执行这个函数
        if ($('#type').val() === '1') {
            $('#media').hide();
        } else {
            $('#media').show()
        }
    });


</script>
</body>
</html>
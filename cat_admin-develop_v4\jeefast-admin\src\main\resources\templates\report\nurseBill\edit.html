<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改指标')"/>
    <th:block th:include="include :: select2-css" />
    <th:block th:include="include :: bootstrap-select-css" />
    <title>修改指标</title>
</head>
<body class="white-bg">

<div class="tabs-container">
    <ul class="nav nav-tabs">
        <li class="active"><a data-toggle="tab" href="#tab-1" aria-expanded="true">基础信息</a></li>
        <li id="alias" class=""><a data-toggle="tab" href="#tab-2" aria-expanded="true">别名信息</a></li>
    </ul>
    <div class="tab-content">
        <div id="tab-1" class="tab-pane active">
            <!--基础信息-->
            <form class="form-horizontal m" id="form-ele-edit" th:object="${nurseBill}">
                <input name="eleId" class="form-control" th:value="*{eleId}" type="hidden">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-sm-3 control-label"> 项目名称：</label>
                        <div class="col-sm-8">
                            <label>
                                <select name="projectLabel" class="form-control m-b"
                                        th:with="type=${@dict.getType('project_label')}" required>
                                    <option th:each="dict : ${type}"
                                            th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"
                                            th:selected="${dict.dictValue == nurseBill.projectLabel}"/>
                                </select>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">指标显示名称：</label>
                        <div class="col-sm-9">
                            <label>
                                <input type="text" name="eleMaster" class="form-control" placeholder="请输入文本"
                                       th:value="*{eleMaster}">
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">指标编码：</label>
                        <div class="col-sm-9">
                            <label>
                                <input type="text" name="eleName" class="form-control" placeholder="请输入文本"
                                       th:value="*{eleName}">
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">指标类型：</label>
                        <div class="col-sm-8">
                            <label>
                                <select name="eleType" class="form-control m-b"
                                        th:with="type=${@dict.getType('ele_type')}" required>
                                    <option th:each="dict: ${type}" th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"
                                            th:selected="${dict.dictValue == nurseBill.eleType}"/>
                                </select>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">指标备注：</label>
                        <div class="col-sm-9">
                            <label>
                                <input type="text" name="eleRemarks" class="form-control" placeholder="请输入文本"
                                       th:value="*{eleRemarks}">
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">是否启用：</label>
                        <div class="col-sm-8">
                            <div class="radio-box" th:each="dict : ${@dict.getType('is_start')}">
                                <label>
                                    <input type="radio" th:id="${dict.dictCode}" name="isStart"
                                           th:value="${dict.dictValue}"
                                           th:checked="${nurseBill.isStart == dict.dictValue}">
                                </label>
                                <label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">标准单位：</label>
                        <div class="col-sm-9">
                            <label>
                                <input type="text" name="baseUnit" class="form-control" placeholder="请输入文本"
                                       th:value="*{baseUnit}">
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">变体单位列表：</label>
                        <div class="col-sm-9">
                            <label>
                                <textarea name="variantUnitList" class="form-control"
                                          placeholder="请输入文本" th:text="*{variantUnitList}"></textarea>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">参考值：</label>
                        <div class="col-sm-9">
                            <label>
                                <input type="text" name="baseReference" class="form-control" placeholder="请输入文本"
                                       th:value="*{baseReference}">
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">参考值-展示：</label>
                        <div class="col-sm-9">
                            <label>
                                <input type="text" name="baseReferenceShow" class="form-control"
                                       placeholder="请输入文本"
                                       th:value="*{baseReferenceShow}">
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">图标url：</label>
                        <div class="col-sm-9">
                            <label>
                                <input type="text" name="imageUrl" class="form-control" placeholder="请输入文本"
                                       th:value="*{imageUrl}">
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">分类：</label>
                        <div class="col-sm-8">
                            <label>
                                <select name="reportType" class="form-control m-b"
                                        th:with="type=${@dict.getType('ele_report_type')}" required>
                                    <option th:each="dict : ${type}"
                                            th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"
                                            th:selected="${dict.dictValue == nurseBill.reportType}"/>
                                </select>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">是否重点指标：</label>
                        <div class="col-sm-8">
                            <div class="radio-box" th:each="dict : ${@dict.getType('sys_yes_no')}">
                                <label>
                                    <input type="radio" th:id="${dict.dictCode}" name="isImportance"
                                           th:value="${dict.dictValue}"
                                           th:checked="${nurseBill.isImportance == dict.dictValue}">
                                </label>
                                <label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">关联异常描述：</label>
                        <div class="col-sm-9">
                            <label>
                                <textarea name="exceptionDesc" class="form-control"
                                          placeholder="请输入格式文本" th:text="*{exceptionDesc}"></textarea>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">偏离程度-偏低：</label>
                        <div class="col-sm-9">
                            <label>
                                    <textarea name="lowSide" class="form-control"
                                              placeholder="请输入文本" th:text="*{lowSide}"></textarea>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">偏离程度-偏高：</label>
                        <div class="col-sm-9">
                            <label>
                                <textarea name="highSide" class="form-control"
                                          placeholder="请输入文本" th:text="*{highSide}"></textarea>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">潜在健康风险(严重程度)：</label>
                        <div class="col-sm-9">
                            <label>
                                <input type="text" name="healthRisk" class="form-control" placeholder="请输入文本"
                                       th:value="*{healthRisk}">
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">复查期限：</label>
                        <div class="col-sm-9">
                            <label>
                                <input type="text" name="reviewDate" class="form-control" placeholder="请输入文本"
                                       th:value="*{reviewDate}">
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label">系统名称：</label>
                        <div class="col-sm-9" >
                            <label style="width: 100%">
                                <select name="systemNameList" class="form-control"
                                        th:with="type=${@dict.getType('system_of_systems_name')}" multiple>
                                    <option th:each="dict : ${type}"
                                            th:text="${dict.dictLabel}"
                                            th:value="${dict.dictValue}"
                                            th:selected="${#strings.contains(nurseBill.systemNames,dict.dictValue)}"/>
                                </select>
                            </label>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div id="tab-2" class="tab-pane">
            <div class="col-md-12 form-group">
                <div style="color: #761c19; font-size: 20px; text-align: center">
                    多个别名,套餐名按照英文;进行隔开(A;B;C)
                </div>
            </div>
            <form class="form-horizontal m" id="form-alias-edit" th:object="${nurseBill}">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">别名名称：</label>
                        <div class="col-sm-9">
                            <label>
                                <input type="text" name="aliasNameList" class="form-control" placeholder="请输入文本"
                                       th:value="*{aliasNameList}">
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">套餐名称：</label>
                        <div class="col-sm-9">
                            <label>
                                <input type="text" name="projectAlias" class="form-control" placeholder="请输入文本"
                                       th:value="*{projectAlias}">
                            </label>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>


<th:block th:include="include :: footer"/>
<th:block th:include="include :: select2-js" />
<th:block th:include="include :: bootstrap-select-js" />
<script type="text/javascript">
    //动态媒体url列表
    var prefix = ctx + "report/nurseBill"
    $("#form-ele-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            // 获取表单数据并转换为对象
            var formData = serializeFormToObject($('#form-ele-edit'));
            if ('string' == typeof formData.systemNameList) {
                formData.systemNameList = [formData.systemNameList];
            }
            // console.log('formData:{}',typeof formData.systemNameList)
            var aliasData = serializeFormToObject($('#form-alias-edit'));
            // 合并两个对象
            var obj = {
                ...formData,
                aliasNameList: aliasData.aliasNameList,
                projectAlias: aliasData.projectAlias,
                check: formData.check === "1"
            };
            console.log(obj);
            // 使用 AJAX 提交数据
            $.ajax({
                url: prefix + "/edit",
                data: JSON.stringify(obj),
                type: "POST",
                dataType: "json",
                contentType: "application/json;charset=utf-8",
                success: function (result) {
                    $.operate.successCallback(result);
                },
                error: function (xhr, status, error) {
                    console.error('Error:', error);
                }
            });
        }
    }

    // 序列化表单数据为对象
    function serializeFormToObject($form) {
        var o = {};
        var a = $form.serializeArray();
        $.each(a, function () {
            if (o[this.name] !== undefined) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    }

</script>
</body>
</html>
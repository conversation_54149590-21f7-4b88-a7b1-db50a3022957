<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指标迁移')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <table id="bootstrap-table2" data-mobile-responsive="true" class="table table-hover">
            <thead>
            <tr>
                <th style="" data-field="eleMaster" tabindex="0">
                    <div class="th-inner ">指标显示名称</div>
                    <div class="fht-cell"></div>
                </th>
<!--                <th style="" data-field="eleName" tabindex="0">-->
<!--                    <div class="th-inner ">元素名称</div>-->
<!--                    <div class="fht-cell"></div>-->
<!--                </th>-->
<!--                <th style="" data-field="eleType" tabindex="0">-->
<!--                    <div class="th-inner ">元素类型</div>-->
<!--                    <div class="fht-cell"></div>-->
<!--                </th>-->
                <th style="" data-field="eleRemarks" tabindex="0">
                    <div class="th-inner ">指标名称</div>
                    <div class="fht-cell"></div>
                </th>
                <th style="" data-field="projectLabel" tabindex="0">
                    <div class="th-inner ">项目名称</div>
                    <div class="fht-cell"></div>
                </th>
                <th style="" data-field="check" tabindex="0">
                    <div class="th-inner ">人工审核</div>
                    <div class="fht-cell"></div>
                </th>
            </tr>
            </thead>
            <tbody>
            </tbody>
        </table>
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <!-- 主元素 -->
                        <li>
                            <p>指标显示名称：</p>
                            <input type="text" name="eleMaster"/>
                        </li>
                        <!-- 元素名称 -->
                        <li>
                            <p>指标名称：</p>
                            <input type="text" name="eleRemarks"/>
                        </li>

                        <!-- 是否启用 -->
                        <li>
                            <p>是否启用：</p>
                            <input type="number" name="isStart"/>
                        </li>
                        <!-- 人工审核 -->
                        <!--                        <li>-->
                        <!--                            <p>人工审核：</p>-->
                        <!--                            <input type="checkbox" name="check" />-->
                        <!--                        </li>-->
                        <!-- 报告名称 -->
                        <li>
                            <p>项目名称 ：</p>
                            <input type="text" name="projectLabel"/>
                        </li>
                        <li class="select-time">
                            <p>发布时间：</p>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                   name="params[beginCreateDate]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                   name="params[endCreateDate]"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>

<script th:inline="javascript">

    var searchParams = window.location.search;
    var urlSearchParams = new URLSearchParams(searchParams);
    var idList = urlSearchParams.get('idList');
    var editFlag = [[${@permission.hasPermi('cat:dynamicBackstage:edit')}]];
    var removeFlag = [[${@permission.hasPermi('cat:dynamicBackstage:remove')}]];
    var moveFlag = [[${@permission.hasPermi('report:dynamicBackstage:move')}]];
    var typeDatas = [[${@dict.getType('cat_dynamic_type')}]];
    var sourceDatas = [[${@dict.getType('cat_dynamic_source')}]];
    var isCheckDatas = [[${@dict.getType('cat_recommend')}]];
    var prefix = ctx + "report/nurseBill";
    $(function () {
        var options = {
            url: prefix + "/list?notIdList=" + idList,
            sortName: "createDate",
            sortOrder: "desc",
            modalName: "动态内容",
            columns: [{
                checkbox: true
            },
                {
                    field: 'eleId',
                    title: '元素ID',
                    visible: false
                },
                {
                    field: 'eleMaster',
                    title: '指标显示名称',
                    visible: true
                },
                {
                    field: 'eleName',
                    title: '指标编码',
                    visible: true
                },
                {
                    field: 'eleType',
                    title: '指标类型',
                    visible: true
                },
                {
                    field: 'eleRemarks',
                    title: '指标名称',
                    visible: true
                },
                {
                    field: 'aliasNameList',
                    title: '指标别名',
                    visible: true,
                    formatter: function(value, row, index) {
                        if (value == undefined){
                            return value
                        }
                        var value_list = value.split(',')
                        value = Array.from(new Set(value_list)).join(',')
                        return value
                        if (value.length >= 20){
                            return value.substring(0, 20);
                        }else {
                            return value
                        }
                    },
                },
                {
                    field: 'projectLabel',
                    title: '项目名称',
                    visible: true
                },
                {
                    field: 'projectAlias',
                    title: '项目别名',
                    visible: true,
                    formatter: function(value, row, index) {
                        if (value == undefined){
                            return value
                        }
                        var value_list = value.split(',')
                        value = Array.from(new Set(value_list)).join(',')
                        return value
                        if (value.length >= 20){
                            return value.substring(0, 20);
                        }else {
                            return value
                        }
                    }
                },
                {
                    field: 'check',
                    title: '人工审核',
                    formatter: function (value) {
                        return value ? '是' : '否';
                    }
                },
                {
                    field: 'isStart',
                    title: '是否启用',
                    formatter: function (value) {
                        return value ? '是' : '否';
                    }
                },
                {
                    field: 'isDelete',
                    title: '删除',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(isCheckDatas, value);
                    }
                },

            ]
        };
        $.table.init(options);
    });
    function submitHandler() {
        var searchParams = window.location.search;
        var urlSearchParams = new URLSearchParams(searchParams);
        var idList = urlSearchParams.get('idList');
        var rows = $.common.isEmpty($.table._option.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns($.table._option.uniqueId);
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        if (rows.length > 1) {
            $.modal.alertWarning("请至多选择一条记录");
            return;
        }
        var eleId = rows[0]
        var moveUrl = prefix + "/move/list/";
        var jsonData = {
            eleId: eleId,
            idList: idList
        }
        $.ajax({
            url: moveUrl, // 替换为实际的数据来源URL
            type: 'POST', // 请求类型，可以根据实际情况是GET或POST等
            dataType: 'json', // 期望返回的数据类型，这里是JSON
            contentType: 'application/json', // 设置请求头，表明发送的是JSON数据
            data: JSON.stringify(jsonData), // 将JSON数据转换为字符串
            success: function (data) {
                $.operate.successCallback(data)
                return true;
            }
        })

    }
</script>
<script>
    $(document).ready(function () {
        var searchParams = window.location.search;
        var urlSearchParams = new URLSearchParams(searchParams);
        var idList = urlSearchParams.get('idList');
        var prefix = ctx + "report/nurseBill";
        var moveUrl = prefix + "/move/list/?idList=" + idList;
        const tbody = $('#bootstrap-table2').find('tbody');
        $.ajax({
            url: moveUrl, // 替换为实际的数据来源URL
            type: 'GET', // 请求类型，可以根据实际情况是GET或POST等
            dataType: 'json', // 期望返回的数据类型，这里是JSON
            success: function (data) {
                // 在这里处理成功获取到的数据
                console.log('成功获取到数据:', data);
                $.each(data.rows, function (index, row) {
                    // 创建新的tr元素
                    const tr = $('<tr>').attr('data-index', index);

                    const eleMasterTd = $('<td>').text(row.eleMaster);
                    tr.append(eleMasterTd);

                    // const eleNameTd = $('<td>').text(row.eleName);
                    // tr.append(eleNameTd);

                    // const eleTypeTd = $('<td>').text('-'); // 数据中未提供eleType，暂时设为-
                    // tr.append(eleTypeTd);

                    const eleRemarksTd = $('<td>').text(row.eleRemarks);
                    tr.append(eleRemarksTd);

                    const projectLabelTd = $('<td>').text(row.projectLabel);
                    tr.append(projectLabelTd);

                    const checkTd = $('<td>').text(row.check ? '是' : '否');
                    tr.append(checkTd);

                    // 将创建好的tr元素添加到tbody中
                    tbody.append(tr);
                });
                // 可以进行数据的展示、进一步处理等操作
            },
            error: function (xhr, status, error) {
                // 在这里处理请求失败的情况
                console.log('请求失败:', error);
            }
        });
    });
</script>
</body>
</html>
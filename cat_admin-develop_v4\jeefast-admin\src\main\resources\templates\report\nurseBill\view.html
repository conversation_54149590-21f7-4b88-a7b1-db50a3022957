<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('指标数据')"/>
    <title>报告指标管理</title>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <p>指标显示名称：</p>
                            <label>
                                <input type="text" name="eleMaster"/>
                            </label>
                        </li>
                        <li>
                            <p>指标名称：</p>
                            <label>
                                <input type="text" name="eleRemarks"/>
                            </label>
                        </li>
                        <li>
                            <p>项目名称 ：</p>
                            <label>
                                <input type="text" name="projectLabel"/>
                            </label>
                        </li>
                        <li>
                            <p>分类：</p>
                            <select name="reportType" th:with="type=${@dict.getType('ele_report_type')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <p>是否重点指标：</p>
                            <select name="isImportance" th:with="type=${@dict.getType('sys_yes_no')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li class="select-time">
                            <p>发布时间：</p>
                            <label for="startTime">
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                       name="params[beginCreateDate]"/>
                            </label>
                            <span>-</span>
                            <label for="endTime">
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                       name="params[endCreateDate]"/>
                            </label>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()"
               shiro:hasPermission="cat:nurseBill:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()"
               shiro:hasPermission="cat:nurseBill:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-primary multiple disabled" onclick="$.operate.moveAll()"
               shiro:hasPermission="cat:nurseBill:edit">
                <i class="fa fa-edit"></i> 移动
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="cat:nurseBill:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    var isCheckDatas = [[${@dict.getType('cat_recommend')}]];
    var reportTypeDatas = [[${@dict.getType('ele_report_type')}]];
    var isImportanceDatas = [[${@dict.getType('sys_yes_no')}]];
    var prefix = ctx + "report/nurseBill";
    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            moveUrl: prefix + "/move/?idList={id}",
            sortName: "createDate",
            sortOrder: "desc",
            modalName: "报告指标",
            columns: [{
                checkbox: true
            },
                {
                    field: 'eleId',
                    title: '元素ID',
                    visible: false
                },
                {
                    field: 'eleMaster',
                    title: '指标显示名称',
                    visible: true
                },
                {
                    field: 'eleName',
                    title: '指标编码',
                    visible: true
                },
                // {
                //     field: 'eleType',
                //     title: '指标类型',
                //     visible: true
                // },
                {
                    field: 'eleRemarks',
                    title: '指标名称',
                    visible: true
                },
                {
                    field: 'aliasNameList',
                    title: '指标别名',
                    visible: true,
                    formatter: function (value) {
                        if (value === undefined) {
                            return value
                        }
                        var value_list = value.split(',')
                        value = Array.from(new Set(value_list)).join(',')
                        return value
                    },
                },
                {
                    field: 'projectLabel',
                    title: '项目名称',
                    visible: true
                },
                {
                    field: 'projectAlias',
                    title: '项目别名',
                    visible: true,
                    formatter: function (value) {
                        if (value === undefined) {
                            return value
                        }
                        var value_list = value.split(',')
                        value = Array.from(new Set(value_list)).join(',')
                        return value
                    },
                },
                // {
                //     field: 'variantUnitList',
                //     title: '变体单位列表',
                //     visible: true
                // },
                // {
                //     field: 'baseReference',
                //     title: '参考值',
                //     visible: true
                // },
                // {
                //     field: 'baseReferenceShow',
                //     title: '参考值 - 展示',
                //     visible: true
                // },
                {
                    field: 'check',
                    title: '人工审核',
                    formatter: function (value) {
                        return value ? '是' : '否';
                    },
                    visible: false
                },
                {
                    field: 'isStart',
                    title: '是否启用',
                    formatter: function (value) {
                        return value ? '是' : '否';
                    },
                    visible: false
                },
                {
                    field: 'isDelete',
                    title: '删除',
                    formatter: function (value) {
                        return $.table.selectDictLabel(isCheckDatas, value);
                    },
                    visible: false
                },
                {
                    field: 'reportType',
                    title: '分类',
                    formatter: function (value) {
                        return $.table.selectDictLabel(reportTypeDatas, value);
                    },
                    visible: true
                },
                {
                    field: 'isImportance',
                    title: '是否重点',
                    formatter: function (value) {
                        return $.table.selectDictLabel(isImportanceDatas, value);
                    },
                    visible: true
                }
            ]
        };
        $.table.init(options);
    });
</script>
</body>
</html>
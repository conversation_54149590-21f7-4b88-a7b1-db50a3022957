<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('AI生成内容')"/>
    <th:block th:include="include :: layout-latest-css"/>
</head>
<body class="gray-bg">
<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive="true"></table>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: layout-latest-js"/>
<script th:inline="javascript">
    var prefix = ctx + "system/algorithm";

    $(function () {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({initClosed: panehHidden, west__size: 185});
        queryUserList();
    });

    function queryUserList() {
        var options = {
            url: prefix + "/list",
            updateUrl: prefix + "/edit/{id}",
            columns: [
                {
                    field: 'id',
                    title: '记录'
                },
                {
                    field: 'content',
                    title: '内容'
                },
                {
                    field: 'status',
                    title: '审核状态'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.id + '\')"><i class="fa fa-edit"></i>通过</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.id + '\')"><i class="fa fa-remove"></i>拒绝</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    }
</script>
</body>
</html>
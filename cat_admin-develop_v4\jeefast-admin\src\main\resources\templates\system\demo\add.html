<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增用户演示')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-demo-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">登录账号：</label>
                <div class="col-sm-8">
                    <input name="userName" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">用户昵称：</label>
                <div class="col-sm-8">
                    <input name="userName" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">删除标志：</label>
                <div class="col-sm-8">
                    <input name="delFlag" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "system/demo"
        $("#form-demo-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-demo-add').serialize());
            }
        }
    </script>
</body>
</html>
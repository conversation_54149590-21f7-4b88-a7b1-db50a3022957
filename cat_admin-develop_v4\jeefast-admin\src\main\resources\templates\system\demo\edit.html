<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改用户演示')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-demo-edit" th:object="${sysDemo}">
            <input name="userId" th:field="*{userId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">登录账号：</label>
                <div class="col-sm-8">
                    <input name="userName" th:field="*{userName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">用户昵称：</label>
                <div class="col-sm-8">
                    <input name="nickName" th:field="*{nickName}" class="form-control" type="text" required>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "system/demo";
        $("#form-demo-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-demo-edit').serialize());
            }
        }
    </script>
</body>
</html>
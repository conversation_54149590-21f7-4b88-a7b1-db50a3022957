<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<meta charset="utf-8">
	<th:block th:include="include :: header('新增通知公告')" />
	<th:block th:include="include :: summernote-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-notice-add">
			<div class="form-group">	
				<label class="col-sm-2 control-label">公告标题：</label>
				<div class="col-sm-10">
					<input id="noticeTitle" name="noticeTitle" class="form-control" type="text" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-2 control-label">公告类型：</label>
				<div class="col-sm-10">
					<select name="noticeType" class="form-control m-b" th:with="type=${@dict.getType('sys_notice_type')}">
	                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
	                </select>
				</div>
			</div>
			<div class="form-group">	
				<label class="col-sm-2 control-label">公告内容：</label>
				<div class="col-sm-10">
				    <input id="noticeContent" name="noticeContent" type="hidden">
				    <div class="summernote"></div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-2 control-label">链接地址：</label>
				<div class="col-sm-10">
					<input id="href" name="href" class="form-control" type="text" >
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-2 control-label">链接类型：</label>
				<div class="col-sm-10">
					<div class="radio-box" th:each="dict : ${@dict.getType('cat_href_type')}">
						<input type="radio" th:id="${dict.dictCode}" name="hrefType" th:value="${dict.dictValue}" th:checked="${dict.default}">
						<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-2 control-label">条件语句：</label>
				<div class="col-sm-10">
					<input id="condition" name="condition" class="form-control" type="text" >
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-2 control-label">公告状态：</label>
				<div class="col-sm-10">
				    <div class="radio-box" th:each="dict : ${@dict.getType('sys_notice_status')}">
						<input type="radio" th:id="${dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
						<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
					</div>
				</div>
			</div>
		</form>
	</div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: summernote-js" />
    <script type="text/javascript">
        var prefix = ctx + "system/notice";

	    $('.summernote').summernote({
	    	placeholder: '请输入公告内容',
			height : 192,
			lang : 'zh-CN',
			followingToolbar: false,
			callbacks: {
                onImageUpload: function (files) {
                    sendFile(files[0], this);
                }
            }
		});
	    
	    // 上传文件
	    function sendFile(file, obj) {
	        var data = new FormData();
	        data.append("file", file);
	        $.ajax({
	            type: "POST",
	            url: ctx + "common/upload",
	            data: data,
	            cache: false,
	            contentType: false,
	            processData: false,
	            dataType: 'json',
	            success: function(result) {
	                if (result.code == web_status.SUCCESS) {
	                	$(obj).summernote('editor.insertImage', result.url, result.fileName);
					} else {
						$.modal.alertError(result.msg);
					}
	            },
	            error: function(error) {
	                $.modal.alertWarning("图片上传失败。");
	            }
	        });
	    }
		
		$("#form-notice-add").validate({
			focusCleanup: true
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	        	var sHTML = $('.summernote').summernote('code');
				$("#noticeContent").val(sHTML);
				$.operate.save(prefix + "/add", $('#form-notice-add').serialize());
	        }
	    }
	</script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('新增岗位')" />
</head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-post-add">
			<div class="form-group">
				<label class="col-sm-3 control-label">岗位名称：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="postName" id="postName" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label ">岗位编码：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="postCode" id="postCode" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">显示顺序：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="postSort" id="postSort" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">岗位状态：</label>
				<div class="col-sm-8">
				    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
						<input type="radio" th:id="${dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
						<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">备注：</label>
				<div class="col-sm-8">
					<textarea id="remark" name="remark" class="form-control"></textarea>
				</div>
			</div>
		</form>
	</div>
	<th:block th:include="include :: footer" />
	<script type="text/javascript">
		var prefix = ctx + "system/post";
	    
		$("#form-post-add").validate({
			onkeyup: false,
			rules:{
				postName:{
					remote: {
		                url: ctx + "system/post/checkPostNameUnique",
		                type: "post",
		                dataType: "json",
		                data: {
		                	"postName" : function() {
		                        return $.common.trim($("#postName").val());
		                    }
		                },
		                dataFilter: function(data, type) {
		                	return $.validate.unique(data);
		                }
		            }
				},
				postCode:{
					remote: {
		                url: ctx + "system/post/checkPostCodeUnique",
		                type: "post",
		                dataType: "json",
		                data: {
		                	"postCode" : function() {
		                        return $.common.trim($("#postCode").val());
		                    }
		                },
		                dataFilter: function(data, type) {
		                	return $.validate.unique(data);
		                }
		            }
				},
				postSort:{
					digits:true
				},
			},
			messages: {
		        "postCode": {
		            remote: "岗位编码已经存在"
		        },
		        "postName": {
		            remote: "岗位名称已经存在"
		        }
		    },
		    focusCleanup: true
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	        	$.operate.save(prefix + "/add", $('#form-post-add').serialize());
	        }
	    }
	</script>
</body>
</html>

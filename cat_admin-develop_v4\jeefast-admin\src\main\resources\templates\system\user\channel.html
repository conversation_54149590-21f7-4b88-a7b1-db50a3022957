<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('渠道绑定')" />
	<th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-user-bind">
			<input name="userId"  type="hidden"  th:value="${user.userId}" />
			<div class="form-group">
				<label class="col-sm-3 control-label ">登录名称：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" readonly="true" name="userName" th:value="${user.userName}"/>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">渠道：</label>
				<div class="col-sm-8">
					<select name="channelIds" class="form-control select2-multiple">
						<option th:each="channel:${channel_list}" th:value="${channel.id}" th:text="${channel.channelName}"  th:field="${user.channelIds}"></option>
					</select>
				</div>
			</div>
		</form>
	</div>
	<th:block th:include="include :: footer" />
	<th:block th:include="include :: select2-js" />
	<script type="text/javascript">
		function submitHandler() {
			$.operate.save(ctx + "system/user/bindChannel", $('#form-user-bind').serialize());
	    }

		$(function() {
			$('#channelIds').select2({
				placeholder:"请选择渠道",
				allowClear: true
			});
		})
	</script>
</body>

</html>

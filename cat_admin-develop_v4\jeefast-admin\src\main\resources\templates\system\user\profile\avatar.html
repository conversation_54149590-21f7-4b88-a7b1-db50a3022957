<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('修改用户头像')" />
	<th:block th:include="include :: cropbox-css" />
</head>
<body class="white-bg">
    <div class="container"> 
        <div class="imageBox"> 
	        <div class="thumbBox"></div> 
	        <div class="spinner" style="display: none">Loading...</div> 
	   </div> 
	   <div class="action"> 
	       <div class="new-contentarea tc">
	           <a href="javascript:void(0)" class="upload-img"> <label for="avatar">上传图像</label> </a> 
	           <input type="file" class="" name="avatar" id="avatar" accept="image/*"/>
	       </div> 
	   <input type="button" id="btnCrop" class="Btnsty_peyton" value="裁切" /> 
	   <input type="button" id="btnZoomIn" class="Btnsty_peyton" value="+" /> 
	   <input type="button" id="btnZoomOut" class="Btnsty_peyton" value="-" /> 
	   </div> 
	   <div class="cropped"></div> 
    </div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: cropbox-js" />
<script type="text/javascript">
var cropper;
$(window).load(function() {
	var avatar = '[[${user.avatar}]]';
    var options = {
        thumbBox: '.thumbBox',
        spinner: '.spinner',
        imgSrc: $.common.isEmpty(avatar) ? ctx + 'img/profile.jpg' : ctx + avatar.substr(1)
    }
    cropper = $('.imageBox').cropbox(options);
    $('#avatar').on('change', function() {
        var reader = new FileReader();
        reader.onload = function(e) {
            options.imgSrc = e.target.result;
            //根据MIME判断上传的文件是不是图片类型
            if((options.imgSrc).indexOf("image/")==-1){
                $.modal.alertWarning("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");
            } else {
                cropper = $('.imageBox').cropbox(options);
            }
        }
        reader.readAsDataURL(this.files[0]);
    })
    
	$('#btnCrop').on('click', function(){
		var img = cropper.getDataURL();
		$('.cropped').html('');
		$('.cropped').append('<img src="'+img+'" align="absmiddle" style="width:64px;margin-top:4px;border-radius:64px;box-shadow:0px 0px 12px #7E7E7E;" ><p>64px*64px</p>');
		$('.cropped').append('<img src="'+img+'" align="absmiddle" style="width:128px;margin-top:4px;border-radius:128px;box-shadow:0px 0px 12px #7E7E7E;"><p>128px*128px</p>');
		$('.cropped').append('<img src="'+img+'" align="absmiddle" style="width:180px;margin-top:4px;border-radius:180px;box-shadow:0px 0px 12px #7E7E7E;"><p>180px*180px</p>');
	})
	
	$('#btnZoomIn').on('click', function(){
		cropper.zoomIn();
	})
	
	$('#btnZoomOut').on('click', function(){
		cropper.zoomOut();
	})
});

function submitHandler() {
    var img = cropper.getBlob();
    var formdata = new FormData();
    formdata.append("avatarfile", img);
    $.ajax({
        url: ctx + "system/user/profile/updateAvatar",
        data: formdata,
        type: "post",
        processData: false,
        contentType: false,
        success: function(result) {
        	$.operate.saveSuccess(result);
        }
    })
}
</script>
</body>
</html>

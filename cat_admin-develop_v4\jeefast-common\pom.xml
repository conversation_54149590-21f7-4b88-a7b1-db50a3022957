<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jeefast</artifactId>
        <groupId>com.jeefast</groupId>
        <version>2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
	
    <artifactId>jeefast-common</artifactId>
	
	<description>
	    common通用工具
	</description>

    <dependencies>
		
		<!-- Spring框架基本的核心工具 -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
		</dependency>
		
		<!-- SpringWeb模块 -->
		<dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        
        <!--<PERSON><PERSON>核心框架 -->
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-core</artifactId>
		</dependency>
        
		<!-- pagehelper 分页插件 -->
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>mybatis-spring</artifactId>
					<groupId>org.mybatis</groupId>
				</exclusion>
				<exclusion>
					<artifactId>mybatis</artifactId>
					<groupId>org.mybatis</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
			<version>2.2.3</version>
		</dependency>
		
		<!-- 自定义验证注解 -->
        <dependency>
			<groupId>javax.validation</groupId>
			<artifactId>validation-api</artifactId>
        </dependency>
    
        <!--常用工具类 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		
		<!-- JSON工具类 -->
		<dependency>
		    <groupId>com.fasterxml.jackson.core</groupId>
		    <artifactId>jackson-databind</artifactId>
		</dependency>

		<!-- 阿里JSON解析器 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>

		<!-- io常用工具类 -->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
		</dependency>

		<!-- 文件上传工具类 -->
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
		</dependency>
		
		<!-- HTML解析器 -->
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
		</dependency>
		
		<!-- excel工具 -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
		</dependency>
		
		<!-- yml解析器 -->
		<dependency>
		    <groupId>org.yaml</groupId>
		    <artifactId>snakeyaml</artifactId>
		</dependency>
		
		<!-- servlet包 -->
        <dependency>
		    <groupId>javax.servlet</groupId>
		    <artifactId>javax.servlet-api</artifactId>
		</dependency>

		<!--工具类-->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-core</artifactId>
			<version>5.7.17</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-http</artifactId>
			<version>5.7.17</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-crypto</artifactId>
			<version>5.7.17</version>
		</dependency>

		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>2.9.0</version>
		</dependency>

		<!-- 腾讯云cos存储 -->
		<dependency>
			<groupId>com.tencent.cloud</groupId>
			<artifactId>cos-sts-java</artifactId>
			<version>3.0.5</version>
		</dependency>
		<dependency>
			<groupId>com.qcloud</groupId>
			<artifactId>cos_api</artifactId>
			<version>5.6.24</version>
		</dependency>
		<!-- 腾讯云内容安全 -->
		<dependency>
			<groupId>com.tencentcloudapi</groupId>
			<artifactId>tencentcloud-sdk-java</artifactId>
			<version>3.1.1217</version>
		</dependency>
		<dependency>
			<groupId>com.tencentcloudapi</groupId>
			<artifactId>tencentcloud-sdk-java-tms</artifactId>
			<version>3.1.1131</version>
		</dependency>
		<dependency>
			<groupId>com.tencentcloudapi</groupId>
			<artifactId>tencentcloud-sdk-java-ims</artifactId>
			<version>3.1.1214</version>
		</dependency>
		<dependency>
			<groupId>com.tencentcloudapi</groupId>
			<artifactId>tencentcloud-sdk-java-vm</artifactId>
			<version>3.1.1209</version>
		</dependency>
		<dependency>
			<groupId>com.qcloud</groupId>
			<artifactId>vod_api</artifactId>
			<version>2.1.5</version>
		</dependency>
    </dependencies>

	<repositories>
		<repository>
			<id>bintray-qcloud-maven-repo</id>
			<name>qcloud-maven-repo</name>
			<url>https://dl.bintray.com/qcloud/maven-repo/</url>
			<layout>default</layout>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>
  
</project>
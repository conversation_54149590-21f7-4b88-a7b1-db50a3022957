package com.jeefast.common.config;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jeefast.common.utils.file.UploadCloudFileUtil;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;



@Slf4j
@Configuration
@ConfigurationProperties(prefix = "dcloud-aliyun")
@ToString
public class DcloudAliyunConfig {

    private static String spaceName;
    private static String spaceId;
    private static String clientSecret;


    public static String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        DcloudAliyunConfig.spaceName = spaceName;
    }

    public static String getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(String spaceId) {
        DcloudAliyunConfig.spaceId = spaceId;
    }

    public static String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        DcloudAliyunConfig.clientSecret = clientSecret;
    }

    public static String getToken(){
        String url = "https://api.bspapp.com/client";
        long time = new Date().getTime();
        Map<String,Object> params = new LinkedHashMap<>();
        params.put("method","serverless.auth.user.anonymousAuthorize");
        params.put("params","{}");
        params.put("spaceId", getSpaceId());
        params.put("timestamp",time);
        String json = JSON.toJSONString(params);
        String signSrt = UploadCloudFileUtil.toParams(params);
        String sign = SecureUtil.hmacMd5(getClientSecret()).digestHex(signSrt);
        
        String signResult = HttpUtil.createPost(url).header("x-serverless-sign",sign).body(json).execute().body();
        JSONObject signJson = JSONObject.parseObject(signResult);
        if(signJson.getBoolean("success")){
            return signJson.getJSONObject("data").getString("accessToken");
        }
        log.error("获取上传token失败[{}]",signResult);
        return null;
    }


}

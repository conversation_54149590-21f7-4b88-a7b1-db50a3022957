package com.jeefast.common.config;

import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


@Configuration
@ConfigurationProperties(prefix = "qcloud")
@ToString
public class QcloudConfig {

    private static String appid;
    private static String secretId;
    private static String secretKey;
    private static String region;
    private static String bucketName;

    public static String getAppid() {
        return appid;
    }

    public  void setAppid(String appid) {
        QcloudConfig.appid = appid;
    }

    public static String getSecretId() {
        return secretId;
    }

    public  void setSecretId(String secretId) {
        QcloudConfig.secretId = secretId;
    }

    public static String getSecretKey() {
        return secretKey;
    }

    public  void setSecretKey(String secretKey) {
        QcloudConfig.secretKey = secretKey;
    }

    public static String getRegion() {
        return region;
    }

    public  void setRegion(String region) {
        QcloudConfig.region = region;
    }

    public static String getBucketName() {
        return bucketName;
    }

    public  void setBucketName(String bucketName) {
        QcloudConfig.bucketName = bucketName;
    }
}

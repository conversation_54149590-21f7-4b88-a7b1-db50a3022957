package com.jeefast.common.core.controller;

import java.beans.PropertyEditorSupport;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.core.domain.AjaxResult.Type;
import com.jeefast.common.core.page.PageDomain;
import com.jeefast.common.core.page.TableDataInfo;
import com.jeefast.common.core.page.TableSupport;
import com.jeefast.common.utils.DateUtils;
import com.jeefast.common.utils.ServletUtils;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.sql.SqlUtil;


public class BaseController {
    protected final Logger logger = LoggerFactory.getLogger(BaseController.class);


    @InitBinder
    public void initBinder(WebDataBinder binder) {

        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }


    protected void startPage() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.startPage(pageNum, pageSize, orderBy);
        }
    }


    public HttpServletRequest getRequest() {
        return ServletUtils.getRequest();
    }


    public HttpServletResponse getResponse() {
        return ServletUtils.getResponse();
    }


    public HttpSession getSession() {
        return getRequest().getSession();
    }


    @SuppressWarnings({"rawtypes", "unchecked"})
    protected TableDataInfo getDataTable(List<?> list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    protected TableDataInfo getDataTable(List<?> list, long total) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(list);
        rspData.setTotal(total);
        return rspData;
    }


    protected AjaxResult toAjax(int rows) {
        return rows > 0 ? success() : error();
    }


    protected AjaxResult toAjax(boolean result) {
        return result ? success() : error();
    }


    public AjaxResult success() {
        return AjaxResult.success();
    }


    public AjaxResult error() {
        return AjaxResult.error();
    }


    public AjaxResult success(String message) {
        return AjaxResult.success(message);
    }


    public AjaxResult error(String message) {
        return AjaxResult.error(message);
    }


    public AjaxResult error(Type type, String message) {
        return new AjaxResult(type, message);
    }


    public String redirect(String url) {
        return StringUtils.format("redirect:{}", url);
    }
}

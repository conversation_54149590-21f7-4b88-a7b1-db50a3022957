package com.jeefast.common.enums;


/**
 * 0待审核 1通过 2不通过
 */
public enum AuditEnum {
    // 0待审核 1通过 2不通过 3待复审（动态主数据）
    WAIT_AUDIT("0", "待审核"), PASS("1", "通过"), REJECT("2", "不通过"), TO_RE_AUDIT("3", "待复审");

    private final String code;
    private final String info;

    AuditEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}

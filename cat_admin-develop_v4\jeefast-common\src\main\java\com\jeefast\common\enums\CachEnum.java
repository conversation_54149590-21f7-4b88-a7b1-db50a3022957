package com.jeefast.common.enums;


public enum CachEnum {

    SYS_CONFIG("sys_config:"),
    SYS_DICT_TYPE("sys_dict_type:"),
    WE_CHAT_MINI_ACCESSTOKEN("we_chat:mini:accessToken"),
    REDIS_VIP_GROUP_RULE("vip_group_rule"),
    SQL_VIP_CODE("cat_vip_level_code"),
    ORDER_DELAY_QUEUE("order_delay_queue"),
    WE_CHAT_MINI_CONFIG("we_chat:sys_config"),
    ADVERT_START("advert:start"),
    AGENT_CONFIG("agent"),
    B2B_SIGN("sys:config:b2b_sign"),
    ;


    private String code;

    CachEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    private void setCode(String code) {
        this.code = code;
    }

}

package com.jeefast.common.enums;


public enum HotTypeEnum
{
    BOT("1", "爆"), HOT("2", "热"), NEW("3", "新");

    private final String code;
    private final String info;

    HotTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}

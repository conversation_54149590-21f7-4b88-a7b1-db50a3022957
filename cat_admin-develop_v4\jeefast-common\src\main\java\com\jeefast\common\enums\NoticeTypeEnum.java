package com.jeefast.common.enums;


public enum NoticeTypeEnum
{
    NOTIFY("notify", "推荐通知"), DYNAMIC_FAIL("dynamic_fail", "审核失败通知");

    private final String code;
    private final String info;

    NoticeTypeEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}

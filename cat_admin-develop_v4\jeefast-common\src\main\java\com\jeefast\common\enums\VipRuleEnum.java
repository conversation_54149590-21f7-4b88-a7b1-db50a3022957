package com.jeefast.common.enums;


import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


public enum VipRuleEnum {

    SHOPPING_SALE("商品折扣","shopping_sale",1),
    DYNAMIC_COUNT("动态发布","dynamic_count",1),
    DYNAMIC_TOP("动态置顶","dynamic_top",0),
    DYNAMIC_EXPOSURE("动态超级曝光","dynamic_exposure",1),
    SELF_DYNAMIC_TOP("个人动态置顶","self_dynamic_top",0),
    HEAD_TAG("头像标识","head_tag",1),
    ACCESS_RECORD("访问记录","access_record",0),
    SUPER_PRAISE("超级点赞","super_praise",1),
    REMOVE_ADVERTISE("去除广告","remove_advertise",0),
    ATTENTION_COUNT("关注上限","attention_count",1),
    DIRECTED_LOOK("定向查看","directed_look",0),
    VIP_FIRST_OPEN_CAN("首充送罐头","vip_first_open_can",1);


    private final String name;
    private final String code;
    
    private final Integer isConfig;

    VipRuleEnum(String name, String code, Integer isConfig) {
        this.name = name;
        this.code = code;
        this.isConfig = isConfig;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public Integer getIsConfig() {
        return isConfig;
    }


    public static List<Map<String, String>> toList() {
        List<Map<String, String>> list = Lists.newArrayList();
        for (VipRuleEnum item : VipRuleEnum.values()) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("code", item.getCode());
            map.put("name", item.getName());
            map.put("isConfig", item.getIsConfig().toString());
            list.add(map);
        }
        return list;
    }
}

package com.jeefast.common.utils.file;

import java.io.File;
import java.io.IOException;

import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.SpringContextHolder;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import com.jeefast.common.config.Global;
import com.jeefast.common.constant.Constants;
import com.jeefast.common.exception.file.FileNameLengthLimitExceededException;
import com.jeefast.common.exception.file.FileSizeLimitExceededException;
import com.jeefast.common.exception.file.InvalidExtensionException;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.security.Md5Utils;


public class FileUploadUtils
{
    
    public static final long DEFAULT_MAX_SIZE = 300 * 1024 * 1024;

    
    public static final int DEFAULT_FILE_NAME_LENGTH = 100;

    
    private static String defaultBaseDir = Global.getProfile();

    private static int counter = 0;

    public static void setDefaultBaseDir(String defaultBaseDir)
    {
        FileUploadUtils.defaultBaseDir = defaultBaseDir;
    }

    public static String getDefaultBaseDir()
    {
        return defaultBaseDir;
    }

    
    public static final String upload(MultipartFile file) throws IOException
    {
        try
        {
            return upload(getDefaultBaseDir(), file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        }
        catch (Exception e)
        {
            throw new IOException(e.getMessage(), e);
        }
    }

    
    public static final String upload(String baseDir, MultipartFile file) throws IOException
    {
        try
        {
            return upload(baseDir, file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        }
        catch (Exception e)
        {
            throw new IOException(e.getMessage(), e);
        }
    }

    
    public static final String upload(String baseDir, MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException, IOException, FileNameLengthLimitExceededException,
            InvalidExtensionException
    {
        int fileNamelength = file.getOriginalFilename().length();
        if (fileNamelength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH)
        {
            throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
        }

        assertAllowed(file, allowedExtension);

        String fileName = extractFilename(file);

        RedisUtil redisUtil  = SpringContextHolder.getBean(RedisUtil.class);
        baseDir = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.upload.path");

        File desc = getAbsoluteFile(baseDir, fileName);
        file.transferTo(desc);


        String address = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.cat.ipaddress");
        String pathFileName = address+"/profile" + File.separator + fileName;
        return pathFileName;
    }

    
    public static final String extractFilename(MultipartFile file)
    {
        String fileName = file.getOriginalFilename();
        String extension = getExtension(file);

        
        fileName = encodingFilename(fileName) + "." + extension;
        return fileName;
    }

    private static final File getAbsoluteFile(String uploadDir, String fileName) throws IOException
    {
        File desc = new File(uploadDir + File.separator + fileName);

        if (!desc.getParentFile().exists())
        {
            desc.getParentFile().mkdirs();
        }
        if (!desc.exists())
        {
            desc.createNewFile();
        }
        return desc;
    }

    private static final String getPathFileName(String uploadDir, String fileName) throws IOException
    {
        int dirLastIndex = uploadDir.lastIndexOf("/") + 1;
        String currentDir = StringUtils.substring(uploadDir, dirLastIndex);
        String pathFileName = Constants.RESOURCE_PREFIX + "/" + currentDir + "/" + fileName;
        return pathFileName;
    }

    
    private static final String encodingFilename(String fileName)
    {
        fileName = fileName.replace("_", " ");
        fileName = Md5Utils.hash(fileName + System.nanoTime() + counter++);
        return fileName;
    }

    
    public static final void assertAllowed(MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException, InvalidExtensionException
    {
        long size = file.getSize();
        if (DEFAULT_MAX_SIZE != -1 && size > DEFAULT_MAX_SIZE)
        {
            throw new FileSizeLimitExceededException(DEFAULT_MAX_SIZE / 1024 / 1024);
        }

        String fileName = file.getOriginalFilename();
        String extension = getExtension(file);
        if (allowedExtension != null && !isAllowedExtension(extension, allowedExtension))
        {
            if (allowedExtension == MimeTypeUtils.IMAGE_EXTENSION)
            {
                throw new InvalidExtensionException.InvalidImageExtensionException(allowedExtension, extension,
                        fileName);
            }
            else if (allowedExtension == MimeTypeUtils.FLASH_EXTENSION)
            {
                throw new InvalidExtensionException.InvalidFlashExtensionException(allowedExtension, extension,
                        fileName);
            }
            else if (allowedExtension == MimeTypeUtils.MEDIA_EXTENSION)
            {
                throw new InvalidExtensionException.InvalidMediaExtensionException(allowedExtension, extension,
                        fileName);
            }
            else
            {
                throw new InvalidExtensionException(allowedExtension, extension, fileName);
            }
        }

    }

    
    public static final boolean isAllowedExtension(String extension, String[] allowedExtension)
    {
        for (String str : allowedExtension)
        {
            if (str.equalsIgnoreCase(extension))
            {
                return true;
            }
        }
        return false;
    }

    
    public static final String getExtension(MultipartFile file)
    {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (StringUtils.isEmpty(extension))
        {
            extension = MimeTypeUtils.getExtension(file.getContentType());
        }
        return extension;
    }

    
    public static final String uploadExt(MultipartFile file) throws IOException
    {
        try
        {
            String downloadUrl = null;
            RedisUtil redisUtil  = SpringContextHolder.getBean(RedisUtil.class);
            UploadCloudFileUtil uploadCloudFileUtil = SpringContextHolder.getBean(UploadCloudFileUtil.class);
            VodUploadCloudFileUtil vodUploadCloudFileUtil = SpringContextHolder.getBean(VodUploadCloudFileUtil.class);

            String uploadType = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.cat.upload.type");
            if("2".equals(uploadType)){
                downloadUrl = uploadCloudFileUtil.uploadTxCloud(file);
            } else if("4".equals(uploadType)){
                File saveFile = uploadCloudFileUtil.multipartToFile(file);
                downloadUrl = uploadCloudFileUtil.dCloudAliYunUp(saveFile);
            }else if("5".equals(uploadType)){
                // 通过 file.getContentType() 判断文件是否是视频
                downloadUrl = vodUploadCloudFileUtil.uploadTxCloud(file,file.getContentType().startsWith("video"));
            } else{
                
                downloadUrl = upload(null, file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
            }
            return downloadUrl;
        }
        catch (Exception e)
        {
            throw new IOException(e.getMessage(), e);
        }
    }


    
    public static final String uploadExt(File file) throws IOException
    {
        try
        {
            String downloadUrl = null;
            RedisUtil redisUtil  = SpringContextHolder.getBean(RedisUtil.class);
            UploadCloudFileUtil uploadCloudFileUtil = SpringContextHolder.getBean(UploadCloudFileUtil.class);
            VodUploadCloudFileUtil vodUploadCloudFileUtil = SpringContextHolder.getBean(VodUploadCloudFileUtil.class);
            
            String uploadType = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.cat.upload.type");
            if("2".equals(uploadType)){
                downloadUrl = uploadCloudFileUtil.uploadFile(file,"/"+file.getName());
            } else if("4".equals(uploadType)){
                downloadUrl = uploadCloudFileUtil.dCloudAliYunUp(file);
            }else if("5".equals(uploadType)){
                downloadUrl = vodUploadCloudFileUtil.uploadTxCloud(file);
            }else{
                
                String address = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.cat.ipaddress");
                downloadUrl = address+"/profile" + File.separator + file.getName();
            }
            return downloadUrl;
        }
        catch (Exception e)
        {
            throw new IOException(e.getMessage(), e);
        }
    }
}
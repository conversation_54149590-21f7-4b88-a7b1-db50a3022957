package com.jeefast.common.utils.file;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jeefast.common.config.DcloudAliyunConfig;
import com.jeefast.common.config.QcloudConfig;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.SpringContextHolder;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.DeleteObjectRequest;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;

@Slf4j
@Component
public class UploadCloudFileUtil {




    
    public static String uploadFile(File file, String uploadPath){
        PutObjectRequest putObjectRequest = new PutObjectRequest(QcloudConfig.getBucketName(), uploadPath, file);
        COSClient cosClient = getCosClient();
        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
        cosClient.shutdown();
        return String.format("https://%s.cos.%s.myqcloud.com%s",QcloudConfig.getBucketName(),QcloudConfig.getRegion(),uploadPath);
    }

    private static COSClient getCosClient(){
        
        COSCredentials cred = new BasicCOSCredentials(QcloudConfig.getSecretId(), QcloudConfig.getSecretKey());
        
        
        ClientConfig clientConfig = new ClientConfig(new Region(QcloudConfig.getRegion()));
        
        COSClient cosClient = new COSClient(cred, clientConfig);
        
        
        return cosClient;
    }


    
    public String uploadTxCloud(MultipartFile multipartFile) throws IOException {
        File file = multipartToFile(multipartFile);
        return uploadFile(file,"/"+file.getName());
    }


    
    public boolean delFile(String filePath){
        String servicePath = String.format("https://%s.cos.%s.myqcloud.com/",QcloudConfig.getBucketName(),QcloudConfig.getRegion());
        
        String fileKey = filePath.replace(servicePath,"");
        COSClient cosClient = getCosClient();
        cosClient.deleteObject(QcloudConfig.getBucketName(),fileKey);
        cosClient.shutdown();
        return true;
    }


    
    public File multipartToFile(MultipartFile multipartFile) throws IOException {
        String fileName = IdUtil.fastSimpleUUID() +"."+ FileUtil.extName(multipartFile.getOriginalFilename());
        RedisUtil redisUtil  = SpringContextHolder.getBean(RedisUtil.class);
        String fileTempPath = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.upload.path");
        File file = new File(fileTempPath + fileName);
        FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), file);
        return file;
    }


    
    public static String dCloudAliYunUp(File file) {
        String token = DcloudAliyunConfig.getToken();
        String url = "https://api.bspapp.com/client";
        long time = new Date().getTime();
        Map<String,Object> params = new LinkedHashMap<>();
        params.put("method","serverless.file.resource.generateProximalSign");
        params.put("params","{\"env\":\"public\",\"filename\":\""+file.getName()+"\"}");
        params.put("spaceId",DcloudAliyunConfig.getSpaceId());
        params.put("timestamp",time);
        params.put("token",token);
        String json = JSON.toJSONString(params);
        String signSrt = toParams(params);
        String sign = SecureUtil.hmacMd5(DcloudAliyunConfig.getClientSecret()).digestHex(signSrt);
        String signResult = HttpUtil.createPost(url)
                .header("x-basement-token",token)
                .header("x-serverless-sign",sign)
                .body(json).execute().body();
        JSONObject signJson = JSONObject.parseObject(signResult);
        if(signJson.getBoolean("success")){
            
            JSONObject dataJson = signJson.getJSONObject("data");
            String accessKeyId = dataJson.getString("accessKeyId");
            String cdnDomain = dataJson.getString("cdnDomain");
            String signature = dataJson.getString("signature");
            String host = dataJson.getString("host");
            String id = dataJson.getString("id");
            String policy = dataJson.getString("policy");
            String ossPath = dataJson.getString("ossPath");
            params.clear();
            params.put("Content-Disposition","attachment");
            params.put("OSSAccessKeyId",accessKeyId);
            params.put("Signature",signature);
            params.put("host",host);
            params.put("id",id);
            params.put("key",ossPath);
            params.put("policy",policy);
            params.put("success_action_status",200);
            params.put("file",file);
            HttpResponse httpResponse = HttpUtil.createPost("https://bsppub.oss-cn-shanghai.aliyuncs.com")
                    .header("X-OSS-server-side-encrpytion", "AES256")
                    .form(params).execute();
            
            if(200==httpResponse.getStatus()){
                boolean flag = dCloudAliYunReport(token,id);
                if(flag){
                    return String.format("https://%s/%s",cdnDomain,ossPath);
                }
                log.error("DCloud阿里云上传报告失败");
                return null;
            }
        }
        return null;
    }

    
    public static boolean dCloudAliYunReport(String token,String fileId){
        String url = "https://api.bspapp.com/client";
        long time = new Date().getTime();
        Map<String,Object> params = new LinkedHashMap<>();
        params.put("method","serverless.file.resource.report");
        params.put("params","{\"id\":\""+fileId+"\"}");
        params.put("spaceId",DcloudAliyunConfig.getSpaceId());
        params.put("timestamp",time);
        params.put("token",token);
        String json = JSON.toJSONString(params);
        String signSrt = toParams(params);
        String sign = SecureUtil.hmacMd5(DcloudAliyunConfig.getClientSecret()).digestHex(signSrt);
        String signResult = HttpUtil.createPost(url)
                .header("x-basement-token",token)
                .header("x-serverless-sign",sign)
                .body(json).execute().body();
        JSONObject signJson = JSONObject.parseObject(signResult);
        if(signJson.getBoolean("success")){
            return true;
        }
        return false;
    }

    
    public static String toParams(Map<String, ?> paramMap) {
        if (CollectionUtil.isEmpty(paramMap)) {
            return StrUtil.EMPTY;
        }

        final StringBuilder sb = new StringBuilder();
        boolean isFirst = true;
        String key;
        Object value;
        String valueStr;
        for (Map.Entry<String, ?> item : paramMap.entrySet()) {
            if (isFirst) {
                isFirst = false;
            } else {
                sb.append("&");
            }
            key = item.getKey();
            value = item.getValue();
            if (value instanceof Iterable) {
                value = CollectionUtil.join((Iterable<?>) value, ",");
            } else if (value instanceof Iterator) {
                value = CollectionUtil.join((Iterator<?>) value, ",");
            }
            valueStr = Convert.toStr(value);
            if (StrUtil.isNotEmpty(key)) {
                sb.append(key).append("=");
                if (StrUtil.isNotEmpty(valueStr)) {
                    sb.append(valueStr);
                }
            }
        }
        return sb.toString();
    }



}

package com.jeefast.common.utils.file;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import com.jeefast.common.config.QcloudConfig;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.exception.BusinessException;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.SpringContextHolder;
import com.qcloud.vod.VodUploadClient;
import com.qcloud.vod.model.VodUploadRequest;
import com.qcloud.vod.model.VodUploadResponse;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.DeleteMediaRequest;
import com.tencentcloudapi.vod.v20180717.models.DescribeMediaInfosRequest;
import com.tencentcloudapi.vod.v20180717.models.DescribeMediaInfosResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

/**
 * 云点播上传
 * https://cloud.tencent.com/document/product/266/10276
 */
@Slf4j
@Component
public class VodUploadCloudFileUtil {


    public static String uploadFile(String filePath,Boolean isVideo) {
        VodUploadClient client = new VodUploadClient(QcloudConfig.getSecretId(), QcloudConfig.getSecretKey());
        VodUploadRequest request = new VodUploadRequest();
        request.setMediaFilePath(filePath);
        if (isVideo){
            request.setProcedure("LongVideoPreset");
        }
        try {
            VodUploadResponse response = client.upload(QcloudConfig.getRegion(), request);
            log.info("Upload FileId = {}", response.getFileId());
            log.info(response.toString());
            return response.getMediaUrl()+"?fileId="+response.getFileId();
        } catch (Exception e) {
            // 业务方进行异常处理
            log.error("封面文件错误", e);
            throw new BusinessException("文件上传失败");
        }
    }

    public DescribeMediaInfosResponse queryDescribeMediaInfos(String fileId) {
        Credential cred = new Credential(QcloudConfig.getSecretId(), QcloudConfig.getSecretKey());
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("vod.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        VodClient client = new VodClient(cred, "ap-guangzhou", clientProfile);
        // 实例化一个请求对象,每个接口都会对应一个request对象
        DescribeMediaInfosRequest req = new DescribeMediaInfosRequest();
        String[] fileIds = {fileId};
        req.setFileIds(fileIds);
        String[] filters = {
                "basicInfo",
                "metaData",
                "adaptiveDynamicStreamingInfo"};
        req.setFilters(filters);
        try {

            // 返回的resp是一个DescribeMediaInfosResponse的实例，与请求对象对应
            DescribeMediaInfosResponse resp = client.DescribeMediaInfos(req);
            // 输出json格式的字符串回包
            log.info(AbstractModel.toJsonString(resp));
            return resp;
        } catch (Exception e) {
            // 业务方进行异常处理
            log.error("查询文件信息错误", e);
            throw new BusinessException("查询文件信息错误");
        }
    }
    public String uploadTxCloud(MultipartFile multipartFile,Boolean isVideo) throws IOException {
        File file = multipartToFile(multipartFile);
        return uploadFile(file.getAbsolutePath(),isVideo);
    }
    public String uploadTxCloud(File file){
        return uploadFile(file.getAbsolutePath(),false);
    }


    public File multipartToFile(MultipartFile multipartFile) throws IOException {
        String fileName = IdUtil.fastSimpleUUID() + "." + FileUtil.extName(multipartFile.getOriginalFilename());
        RedisUtil redisUtil = SpringContextHolder.getBean(RedisUtil.class);
        String fileTempPath = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + "sys.upload.path");
        File file = new File(fileTempPath + fileName);
        FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), file);
        return file;
    }



    public boolean delFile(String fileId) {
        Credential cred = new Credential(QcloudConfig.getSecretId(), QcloudConfig.getSecretKey());
        VodClient client = new VodClient(cred, QcloudConfig.getRegion());
        DeleteMediaRequest deleteMediaRequest = new DeleteMediaRequest();
        deleteMediaRequest.setFileId(fileId);
        try {
            client.DeleteMedia(deleteMediaRequest);
        } catch (Exception e) {
            // 业务方进行异常处理
            log.error("删除文件错误", e);
            throw new BusinessException("删除上传失败");
        }
        return true;
    }

}

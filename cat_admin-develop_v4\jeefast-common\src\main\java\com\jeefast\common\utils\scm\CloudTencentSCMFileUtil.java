package com.jeefast.common.utils.scm;


import com.jeefast.common.config.QcloudConfig;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ims.v20201229.ImsClient;
import com.tencentcloudapi.ims.v20201229.models.ImageModerationRequest;
import com.tencentcloudapi.ims.v20201229.models.ImageModerationResponse;
import com.tencentcloudapi.tms.v20201229.TmsClient;
import com.tencentcloudapi.tms.v20201229.models.TextModerationRequest;
import com.tencentcloudapi.tms.v20201229.models.TextModerationResponse;
import com.tencentcloudapi.vm.v20210922.VmClient;
import com.tencentcloudapi.vm.v20210922.models.CreateVideoModerationTaskRequest;
import com.tencentcloudapi.vm.v20210922.models.CreateVideoModerationTaskResponse;
import com.tencentcloudapi.vm.v20210922.models.DescribeTaskDetailRequest;
import com.tencentcloudapi.vm.v20210922.models.DescribeTaskDetailResponse;
import com.tencentcloudapi.vm.v20210922.models.StorageInfo;
import com.tencentcloudapi.vm.v20210922.models.TaskInput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Base64;

/**
 * 腾讯内容安全工具类
 * 文本： https://cloud.tencent.com/document/product/1124
 * 图片：https://cloud.tencent.com/document/product/1125
 * 视频：https://cloud.tencent.com/document/product/1265/50673
 */
@Slf4j
@Component
public class CloudTencentSCMFileUtil {

    @Autowired
    private QcloudConfig qcloudConfig;

    /**
     * 文字识别
     * <p>
     * 文本： https://cloud.tencent.com/document/product/1124
     *
     * @param content 该字段表示待检测对象的文本内容，文本需要按utf-8格式编码，长度不能超过10000个字符（按unicode编码计算），并进行 Base64加密
     * @param bizType
     * @return
     */
    public TextModerationResponse textModeration(String content, String dataId, String bizType) {
        try {
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            log.info("====qcloudConfig.getSecretId()====");
            log.info(qcloudConfig.getSecretId());
            Credential cred = new Credential(qcloudConfig.getSecretId(), qcloudConfig.getSecretKey());
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("tms.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            TmsClient client = new TmsClient(cred, "ap-guangzhou", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            TextModerationRequest req = new TextModerationRequest();
            // 对 content 进行 base64 加密
            req.setDataId(dataId);
            req.setContent(Base64.getEncoder().encodeToString(content.getBytes()));
            req.setBizType(bizType);
            // 返回的resp是一个TextModerationResponse的实例，与请求对象对应
            return client.TextModeration(req);
            // 输出json格式的字符串回包
//            System.out.println(TextModerationResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            log.error("腾讯云文本安全检查异常：{}", e.toString(), e);
            return null;
        }
    }

    /**
     * 图片识别
     * <p>
     * 图片：https://cloud.tencent.com/document/product/1125
     *
     * @param imgUrl  文件大小的最小限制为16字节；
     *                Base64编码后的内容（仅限FileContent）应小于10MB；
     *                通过FileURL可访问的源图大小应小于30MB；
     *                文件格式支持：
     *                静态图：BMP、ICO、JPEG、JNG、PNG、TIFF、RAW、SVG、GIF、WEBP、HEIC
     *                动态图：GIF、WEBP、HEIC（默认最多抽取5帧图像，每隔5帧进行一次采样）
     * @param bizType
     * @return
     */
    public ImageModerationResponse imgModeration(String imgUrl, String dataId, String bizType) {
        try {
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(qcloudConfig.getSecretId(), qcloudConfig.getSecretKey());
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("ims.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            ImsClient client = new ImsClient(cred, "ap-guangzhou", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            ImageModerationRequest req = new ImageModerationRequest();
            req.setDataId(dataId);
            req.setBizType(bizType);
            req.setFileUrl(imgUrl);
            // 返回的resp是一个ImageModerationResponse的实例，与请求对象对应
            return client.ImageModeration(req);
            // 输出json格式的字符串回包
//            System.out.println(ImageModerationResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            log.error("腾讯云图片安全检查异常：{}", e.toString(), e);
            return null;
        }
    }

    /**
     * 创建视频识别任务
     * <p>
     * 视频：https://cloud.tencent.com/document/product/1265/50673
     *
     * @param videoUrl 视频文件大小支持：4K视频文件 < 10GB；低于4K视频文件 < 5GB
     *                 视频文件分辨率支持：最佳分辨率为1920x1080 (1080p)，如果视频文件小于300MB，则分辨率可以大于1080p，分辨率最大支持4K
     *                 视频文件支持格式：flv、mkv 、mp4 、rmvb 、avi 、wmv、3gp、ts、mov、rm、mpeg、wmf等。
     *                 视频文件支持的访问方式：链接地址（支持HTTP/HTTPS）、腾讯云COS存储；
     * @param bizType
     * @return
     */
    public CreateVideoModerationTaskResponse videoModerationCreate(String videoUrl, String dataId, String bizType) {
        try {
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(qcloudConfig.getSecretId(), qcloudConfig.getSecretKey());
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("vm.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            VmClient client = new VmClient(cred, "ap-guangzhou", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            CreateVideoModerationTaskRequest req = new CreateVideoModerationTaskRequest();
            req.setBizType(bizType);
            req.setType("VIDEO");
            TaskInput[] taskInputs1 = new TaskInput[1];
            TaskInput taskInput1 = new TaskInput();
            taskInput1.setDataId(dataId);
            taskInput1.setName(dataId);
            StorageInfo storageInfo1 = new StorageInfo();
            storageInfo1.setType("URL");
            storageInfo1.setUrl(videoUrl);
            taskInput1.setInput(storageInfo1);
            taskInputs1[0] = taskInput1;
            req.setTasks(taskInputs1);
            // 返回的resp是一个CreateVideoModerationTaskResponse的实例，与请求对象对应
            return client.CreateVideoModerationTask(req);
            // 输出json格式的字符串回包
//            System.out.println(CreateVideoModerationTaskResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            log.error("腾讯云创建视频安全检查任务异常：{}", e.toString(), e);
            return null;
        }
    }

    /**
     * 查询视频识别任务
     * <p>
     * 视频：https://cloud.tencent.com/document/product/1265/50673
     *
     * @param taskId 创建任务后返回的TaskId字段
     * @return
     */
    public DescribeTaskDetailResponse videoModerationQuery(String taskId) {
        try {
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(qcloudConfig.getSecretId(), qcloudConfig.getSecretKey());
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("vm.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            VmClient client = new VmClient(cred, "ap-guangzhou", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            DescribeTaskDetailRequest req = new DescribeTaskDetailRequest();
            req.setTaskId(taskId);
            // 返回的resp是一个DescribeTaskDetailResponse的实例，与请求对象对应
            return client.DescribeTaskDetail(req);
            // 输出json格式的字符串回包
//            System.out.println(DescribeTaskDetailResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            log.error("腾讯云查询视频安全检查任务异常：{}", e.toString(), e);
            return null;
        }
    }



}

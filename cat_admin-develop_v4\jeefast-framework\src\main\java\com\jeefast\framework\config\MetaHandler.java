package com.jeefast.framework.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jeefast.framework.util.ShiroUtils;
import com.jeefast.system.domain.SysUser;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;


@Component
public class MetaHandler implements MetaObjectHandler {

    private static final Logger logger = LoggerFactory.getLogger(MetaHandler.class);

    
    @Override
    public void insertFill(MetaObject metaObject) {
        
        String fieldName = "createDate";
        if (metaObject.hasSetter(fieldName) && metaObject.hasGetter(fieldName)) {
            
            if(metaObject.getValue(fieldName)==null){
                this.setFieldValByName(fieldName, new Date(), metaObject);
            }
        } else if (metaObject.hasGetter(Constants.ENTITY)) {
            Object et = metaObject.getValue(Constants.ENTITY);
            if (et != null) {
                MetaObject etMeta = SystemMetaObject.forObject(et);
                if (etMeta.hasSetter(fieldName)) {
                    
                    if(metaObject.getValue(fieldName)==null){
                        this.setFieldValByName(fieldName, new Date(), metaObject);
                    }
                }
            }
        } else{
            
            SysUser userEntity = ShiroUtils.getSysUser();
            this.setFieldValByName("createTime", new Date(), metaObject);
            this.setFieldValByName("createBy", userEntity.getUserName(), metaObject);
            this.setFieldValByName("updateTime", new Date(), metaObject);
            this.setFieldValByName("updateBy", userEntity.getUserName(), metaObject);
        }
    }

    
    @Override
    public void updateFill(MetaObject metaObject) {
        
        String fieldName = "createDate";
        if (metaObject.hasSetter(fieldName)) {
            return;
        }
        SysUser userEntity = ShiroUtils.getSysUser();
        this.setFieldValByName("updateTime", new Date(), metaObject);
        this.setFieldValByName("updateBy", userEntity.getUserName(), metaObject);
    }

}

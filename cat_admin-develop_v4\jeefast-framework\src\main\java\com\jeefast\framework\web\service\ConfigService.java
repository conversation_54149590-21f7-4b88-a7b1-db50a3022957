package com.jeefast.framework.web.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeefast.system.service.ISysConfigService;


@Service("config")
public class ConfigService
{
    @Autowired
    private ISysConfigService configService;

    
    public String getKey(String configKey)
    {
        return configService.selectConfigByKey(configKey);
    }
}

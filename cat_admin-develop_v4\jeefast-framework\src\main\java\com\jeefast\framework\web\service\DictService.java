package com.jeefast.framework.web.service;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeefast.system.domain.SysDictData;
import com.jeefast.system.service.ISysDictDataService;


@Service("dict")
public class DictService
{
    @Autowired
    private ISysDictDataService dictDataService;

    
    public List<SysDictData> getType(String dictType)
    {
        return dictDataService.selectDictDataByType(dictType);
    }

    
    public String getLabel(String dictType, String dictValue)
    {
        return dictDataService.selectDictLabel(dictType, dictValue);
    }
}

package com.jeefast.generator.mapper;

import java.util.List;

import com.jeefast.generator.domain.GenTableColumn;


public interface GenTableColumnMapper
{
    
    public List<GenTableColumn> selectDbTableColumnsByName(String tableName);
    
    
    public List<GenTableColumn> selectGenTableColumnListByTableId(GenTableColumn genTableColumn);

    
    public int insertGenTableColumn(GenTableColumn genTableColumn);

    
    public int updateGenTableColumn(GenTableColumn genTableColumn);

    
    public int deleteGenTableColumnByIds(Long[] ids);
}
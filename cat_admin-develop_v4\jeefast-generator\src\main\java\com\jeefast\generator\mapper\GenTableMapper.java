package com.jeefast.generator.mapper;

import java.util.List;

import com.jeefast.generator.domain.GenTable;


public interface GenTableMapper
{
    
    public List<GenTable> selectGenTableList(GenTable genTable);

    
    public List<GenTable> selectDbTableList(GenTable genTable);

    
    public List<GenTable> selectDbTableListByNames(String[] tableNames);

    
    public GenTable selectGenTableById(Long id);

    
    public GenTable selectGenTableByName(String tableName);

    
    public int insertGenTable(GenTable genTable);

    
    public int updateGenTable(GenTable genTable);

    
    public int deleteGenTableByIds(Long[] ids);
}
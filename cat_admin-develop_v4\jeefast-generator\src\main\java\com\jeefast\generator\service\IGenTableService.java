package com.jeefast.generator.service;

import java.util.List;
import java.util.Map;

import com.jeefast.generator.domain.GenTable;


public interface IGenTableService
{
    
    public List<GenTable> selectGenTableList(GenTable genTable);

    
    public List<GenTable> selectDbTableList(GenTable genTable);

    
    public List<GenTable> selectDbTableListByNames(String[] tableNames);

    
    public GenTable selectGenTableById(Long id);

    
    public void updateGenTable(GenTable genTable);

    
    public void deleteGenTableByIds(String ids);

    
    public void importGenTable(List<GenTable> tableList, String operName);

    
    public Map<String, String> previewCode(Long tableId);

    
    public byte[] generatorCode(String tableName);

    
    public byte[] generatorCode(String[] tableNames);

    
    public void validateEdit(GenTable genTable);
}

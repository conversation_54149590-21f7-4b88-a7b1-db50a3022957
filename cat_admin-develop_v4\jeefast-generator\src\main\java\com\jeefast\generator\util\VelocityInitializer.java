package com.jeefast.generator.util;

import java.util.Properties;
import org.apache.velocity.app.Velocity;
import com.jeefast.common.constant.Constants;


public class VelocityInitializer
{
    
    public static void initVelocity()
    {
        Properties p = new Properties();
        try
        {
            
            p.setProperty("file.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
            
            p.setProperty(Velocity.ENCODING_DEFAULT, Constants.UTF8);
            p.setProperty(Velocity.OUTPUT_ENCODING, Constants.UTF8);
            
            Velocity.init(p);
        }
        catch (Exception e)
        {
            throw new RuntimeException(e);
        }
    }
}

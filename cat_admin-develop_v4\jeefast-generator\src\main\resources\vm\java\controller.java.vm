package ${basePackage}.web.controller.${moduleName};

import java.util.Arrays;
import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
#if($table.crud)
import com.jeefast.common.core.page.TableDataInfo;
#elseif($table.tree)
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.core.domain.Ztree;
#end

/**
 * ${functionName}Controller
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Controller
@RequestMapping("/${moduleName}/${businessName}")
public class ${ClassName}Controller extends BaseController {
    private String prefix = "${moduleName}/${businessName}";

    @Autowired
    private I${ClassName}Service ${className}Service;

    @RequiresPermissions("${permissionPrefix}:view")
    @GetMapping()
    public String ${businessName}() {
        return prefix + "/${businessName}";
    }

#if($table.tree)
    /**
     * 查询${functionName}树列表
     */
    @RequiresPermissions("${permissionPrefix}:list")
    @PostMapping("/list")
    @ResponseBody
    public List<${ClassName}> list(${ClassName} ${className}) {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return list;
    }
#elseif($table.crud)
    /**
     * 查询${functionName}列表
     */
    @RequiresPermissions("${permissionPrefix}:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(${ClassName} ${className}) {
        QueryWrapper<${ClassName}> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	/*if(StringUtils.isNotEmpty(sysDemo.getLoginName())) {
    		queryWrapper.like("login_name", sysDemo.getLoginName());
    	} 
    	if(StringUtils.isNotEmpty(sysDemo.getUserName())) {
    		queryWrapper.like("user_name", sysDemo.getUserName());
    	}*/
		// 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        return getDataTable(${className}Service.list(queryWrapper));
    }
#end

    /**
     * 导出${functionName}列表
     */
    @RequiresPermissions("${permissionPrefix}:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(${ClassName} ${className}) {
        List<${ClassName}> list = ${className}Service.list(new QueryWrapper<>());
        ExcelUtil<${ClassName}> util = new ExcelUtil<${ClassName}>(${ClassName}.class);
        return util.exportExcel(list, "${businessName}");
    }

#if($table.crud)
    /**
     * 新增${functionName}
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }
#elseif($table.tree)
    /**
     * 新增${functionName}
     */
    @GetMapping(value = { "/add/{${pkColumn.javaField}}", "/add/" })
    public String add(@PathVariable(value = "${pkColumn.javaField}", required = false) Long ${pkColumn.javaField}, ModelMap mmap) {
        if (StringUtils.isNotNull(${pkColumn.javaField})) {
            mmap.put("${className}", ${className}Service.select${ClassName}ById(${pkColumn.javaField}));
        }
        return prefix + "/add";
    }
#end

    /**
     * 新增保存${functionName}
     */
    @RequiresPermissions("${permissionPrefix}:add")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(${ClassName} ${className}) {
        return toAjax(${className}Service.save(${className}));
    }

    /**
     * 修改${functionName}
     */
    @GetMapping("/edit/{${pkColumn.javaField}}")
    public String edit(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}, ModelMap mmap) {
        ${ClassName} ${className} = ${className}Service.getById(${pkColumn.javaField});
        mmap.put("${className}", ${className});
        return prefix + "/edit";
    }

    /**
     * 修改保存${functionName}
     */
    @RequiresPermissions("${permissionPrefix}:edit")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(${ClassName} ${className}) {
        return toAjax(${className}Service.updateById(${className}));
    }

#if($table.crud)
    /**
     * 删除${functionName}
     */
    @RequiresPermissions("${permissionPrefix}:remove")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(${className}Service.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
#elseif($table.tree)
    /**
     * 删除
     */
    @RequiresPermissions("${permissionPrefix}:remove")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @GetMapping("/remove/{${pkColumn.javaField}}")
    @ResponseBody
    public AjaxResult remove(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        return toAjax(${className}Service.delete${ClassName}ById(${pkColumn.javaField}));
    }
#end
#if($table.tree)

    /**
     * 选择${functionName}树
     */
#set($BusinessName=$businessName.substring(0,1).toUpperCase() + ${businessName.substring(1)})
    @GetMapping(value = { "/select${BusinessName}Tree/{${pkColumn.javaField}}", "/select${BusinessName}Tree/" })
    public String select${BusinessName}Tree(@PathVariable(value = "${pkColumn.javaField}", required = false) Long ${pkColumn.javaField}, ModelMap mmap) {
        if (StringUtils.isNotNull(${pkColumn.javaField})) {
            mmap.put("${className}", ${className}Service.select${ClassName}ById(${pkColumn.javaField}));
        }
        return prefix + "/tree";
    }

    /**
     * 加载${functionName}树列表
     */
    @GetMapping("/treeData")
    @ResponseBody
    public List<Ztree> treeData() {
        List<Ztree> ztrees = ${className}Service.select${ClassName}Tree();
        return ztrees;
    }
#end
}

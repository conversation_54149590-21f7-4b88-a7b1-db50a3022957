package ${packageName}.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
#foreach ($column in $columns)
    #if($column.attrType == 'Date')
    import java.util.Date;
        #break
    #end
#end
import com.jeefast.common.core.entity.CatBaseEntity;
#if($table.crud)
import com.jeefast.common.core.domain.BaseEntity;
#elseif($table.tree)
import com.jeefast.common.core.domain.TreeEntity;
#end
#foreach ($import in $importList)
import ${import};
#end

/**
 * ${functionName}表 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
 #if($table.crud)
#set($Entity="CatBaseEntity")
#elseif($table.tree)
#set($Entity="TreeEntity")
#end
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("${tableName}")
public class ${ClassName} extends ${Entity} implements Serializable {
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if(!$column.superColumn)
    /** $column.columnComment */
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($parentheseIndex != -1)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
#elseif($column.javaType == 'Date')
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
#else
    @Excel(name = "${comment}")
#end
#end
#if($column.isPk==1)
    @TableId(value = "$column.columnName", type = IdType.AUTO)
    private $column.javaType $column.javaField;
#else
    private $column.javaType $column.javaField;
#end
#end
#end
}

package ${packageName}.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import ${basePackage}.${moduleName}.mapper.${ClassName}Mapper;
import ${basePackage}.${moduleName}.domain.${ClassName};
import ${basePackage}.${moduleName}.service.I${ClassName}Service;
import com.baomidou.dynamic.datasource.annotation.DS;

/**
 * ${functionName} 服务层实现
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Service
//@DS("slave")去掉多数据源
public class ${ClassName}ServiceImpl extends ServiceImpl<${ClassName}Mapper, ${ClassName}> implements I${ClassName}Service {

}
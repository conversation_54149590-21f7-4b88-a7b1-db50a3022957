package com.jeefast.quartz.mapper;

import java.util.List;

import com.jeefast.quartz.domain.SysJobLog;


public interface SysJobLogMapper
{
    
    public List<SysJobLog> selectJobLogList(SysJobLog jobLog);

    
    public List<SysJobLog> selectJobLogAll();

    
    public SysJobLog selectJobLogById(Long jobLogId);

    
    public int insertJobLog(SysJobLog jobLog);

    
    public int deleteJobLogByIds(String[] ids);

    
    public int deleteJobLogById(Long jobId);

    
    public void cleanJobLog();
}

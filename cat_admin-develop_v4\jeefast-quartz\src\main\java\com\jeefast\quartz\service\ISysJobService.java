package com.jeefast.quartz.service;

import java.util.List;
import org.quartz.SchedulerException;

import com.jeefast.quartz.domain.SysJob;
import com.jeefast.common.exception.job.TaskException;


public interface ISysJobService
{
    
    public List<SysJob> selectJob<PERSON>ist(SysJob job);

    
    public SysJob selectJobById(Long jobId);

    
    public int pauseJob(SysJob job) throws SchedulerException;

    
    public int resumeJob(SysJob job) throws SchedulerException;

    
    public int deleteJob(SysJob job) throws SchedulerException;

    
    public void deleteJobByIds(String ids) throws SchedulerException;

    
    public int changeStatus(SysJob job) throws SchedulerException;

    
    public void run(Sys<PERSON><PERSON> job) throws SchedulerException;

    
    public int insertJob(SysJob job) throws SchedulerException, TaskException;

    
    public int updateJob(Sys<PERSON>ob job) throws SchedulerException, TaskException;

    
    public boolean checkCronExpressionIsValid(String cronExpression);
}
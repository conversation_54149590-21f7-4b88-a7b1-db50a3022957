<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('新增定时任务')" />
</head>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-job-add">
		    <input type="hidden" name="createBy" th:value="${@permission.getPrincipalProperty('userName')}">
			<div class="form-group">
				<label class="col-sm-3 control-label">任务名称：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="jobName" id="jobName" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">任务分组：</label>
				<div class="col-sm-8">
					<select name="jobGroup" class="form-control m-b" th:with="type=${@dict.getType('sys_job_group')}">
		                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
		            </select>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label ">调用目标字符串：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="invokeTarget" id="invokeTarget" required>
				    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> Bean调用示例：ryTask.ryParams('ry')</span>
				    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> Class类调用示例：com.jeefast.quartz.task.RyTask.ryParams('ry')</span>
				    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 参数说明：支持字符串，布尔类型，长整型，浮点型，整型</span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label ">cron表达式：</label>
				<div class="col-sm-8">
					<input class="form-control" type="text" name="cronExpression" id="cronExpression" required>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">执行策略：</label>
				<div class="col-sm-8">
					<label class="radio-box"> <input type="radio" name="misfirePolicy" value="1" th:checked="true"/> 立即执行 </label> 
					<label class="radio-box"> <input type="radio" name="misfirePolicy" value="2" /> 执行一次 </label> 
					<label class="radio-box"> <input type="radio" name="misfirePolicy" value="3" /> 放弃执行 </label>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">并发执行：</label>
				<div class="col-sm-8">
					<label class="radio-box"> <input type="radio" name="concurrent" value="0"/> 允许 </label> 
					<label class="radio-box"> <input type="radio" name="concurrent" value="1" th:checked="true"/> 禁止 </label> 
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">状态：</label>
				<div class="col-sm-8">
				    <div class="radio-box" th:each="dict : ${@dict.getType('sys_job_status')}">
						<input type="radio" th:id="${dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
						<label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
					</div>
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label">备注：</label>
				<div class="col-sm-8">
					<textarea id="remark" name="remark" class="form-control"></textarea>
				</div>
			</div>
		</form>
	</div>
	<th:block th:include="include :: footer" />
	<script type="text/javascript">
		var prefix = ctx + "monitor/job";
	
		$("#form-job-add").validate({
			onkeyup: false,
			rules:{
				cronExpression:{
					remote: {
	                    url: prefix + "/checkCronExpressionIsValid",
	                    type: "post",
	                    dataType: "json",
	                    data: {
	                        "cronExpression": function() {
	                            return $.common.trim($("#cronExpression").val());
	                        }
	                    },
	                    dataFilter: function(data, type) {
	                    	return data;
	                    }
	                }
				},
			},
			messages: {
	            "cronExpression": {
	                remote: "表达式不正确"
	            }
	        },
	        focusCleanup: true
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	        	$.operate.save(prefix + "/add", $('#form-job-add').serialize());
	        }
	    }
	</script>
</body>
</html>

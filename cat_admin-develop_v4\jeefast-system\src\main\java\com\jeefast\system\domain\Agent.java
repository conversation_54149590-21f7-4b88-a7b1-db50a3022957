package com.jeefast.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.core.domain.PyBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("agent")
public class Agent extends PyBaseModel {

    @Excel(name = "agent_id")
    private Long agentId;

    public String getAgentIdString(){
        return getAgentId().toString();
    }

    @Excel(name = "name")
    private String name;

    @Excel(name = "public")
    private Boolean isPublic;

    @Excel(name = "_type")
    private String type;

    @Excel(name = "intro")
    private String intro;

    @Excel(name = "external_url")
    private String externalUrl;

    @Excel(name = "token")
    private String token;

    @Excel(name = "use_token")
    private Boolean useToken;

    @Excel(name = "enable")
    private Boolean enable;

    @Excel(name = "system")
    private Boolean system;

    @Excel(name = "code")
    private String code;

    @Excel(name = "patient_intro_required")
    private Boolean patientIntroRequired;

    @Excel(name = "expand")
    private String expand;

    @Excel(name = "opening_remarks")
    private String openingRemarks;

    @Excel(name = "agent_avatar_url")
    private String agentAvatarUrl;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("agent_id", getAgentId())
                .append("name", getName())
                .append("public", getIsPublic())
                .append("_type", getType())
                .append("token", getToken())
                .append("enable", getEnable())
                .append("system", getSystem())
                .append("code", getCode())
                .append("patient_intro_required", getPatientIntroRequired())
                .append("expand", getExpand())
                .append("opening_remarks", getOpeningRemarks())
                .append("agent_avatar_url", getAgentAvatarUrl())
                .toString();
    }
}
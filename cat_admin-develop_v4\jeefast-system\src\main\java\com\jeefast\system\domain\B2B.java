package com.jeefast.system.domain;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.core.domain.PyBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.Size;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("b_to_b")
public class B2B extends PyBaseModel {

    @Excel(name = "user_id")
    @Size(max = 32)
    private String userId;

    @Excel(name = "sign")
    @Size(max = 64)
    private String sign;

}
package com.jeefast.system.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.jeefast.common.core.domain.BaseEntity;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_demo")
public class SysDemo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    
    @TableId(value = "user_id", type = IdType.AUTO)
    private Long userId;
    
    private String loginName;
    
    private String userName;
    
    private String remark;
}

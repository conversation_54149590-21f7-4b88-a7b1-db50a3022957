package com.jeefast.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@Slf4j
@Data
@Accessors(chain = true)
public class SysFileUpload implements Serializable {

    private static final long serialVersionUID = 1L;


    @TableId(value = "id",type = IdType.UUID)
    @TableField("id")
    private String id;

    

    @TableField("file_md5")
    private String fileMd5;

    

    @TableField("file_path")
    private String filePath;

    

    @TableField("file_content_type")
    private String fileContentType;

    

    @TableField("file_extension")
    private String fileExtension;

    

    @TableField("file_size")
    private BigDecimal fileSize;

    

    @TableField("file_name")
    private String fileName;

    

    @TableField("file_type")
    private String fileType;

    

    @TableField("storage_type")
    private String storageType;

    

    @TableField("bucket_name")
    private String bucketName;

    

    @TableField("file_key")
    private String fileKey;


    @TableField("user_id")
    private String userId;

    @TableField("truename")
    private String truename;


    @TableField("file_source")
    private String fileSource;

    @TableField("code")
    private String code;

    @TableField("create_date")
    private LocalDateTime createDate;


}

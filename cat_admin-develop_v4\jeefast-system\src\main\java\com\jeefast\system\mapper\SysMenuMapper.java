package com.jeefast.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.jeefast.system.domain.SysMenu;


public interface SysMenuMapper
{
    
    public List<SysMenu> selectMenuAll();

    
    public List<SysMenu> selectMenuAllByUserId(Long userId);

    
    public List<SysMenu> selectMenuNormalAll();

    
    public List<SysMenu> selectMenusByUserId(Long userId);

    
    public List<String> selectPermsByUserId(Long userId);

    
    public List<String> selectMenuTree(Long roleId);

    
    public List<SysMenu> selectMenuList(SysMenu menu);

    
    public List<SysMenu> selectMenuListByUserId(SysMenu menu);

    
    public int deleteMenuById(Long menuId);

    
    public SysMenu selectMenuById(Long menuId);

    
    public int selectCountMenuByParentId(Long parentId);

    
    public int insertMenu(SysMenu menu);

    
    public int updateMenu(SysMenu menu);

    
    public SysMenu checkMenuNameUnique(@Param("menuName") String menuName, @Param("parentId") Long parentId);
}

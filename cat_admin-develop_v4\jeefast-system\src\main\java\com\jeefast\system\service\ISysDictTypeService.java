package com.jeefast.system.service;

import java.util.List;

import com.jeefast.system.domain.SysDictType;
import com.jeefast.common.core.domain.Ztree;


public interface ISysDictTypeService
{
    
    public List<SysDictType> selectDictTypeList(SysDictType dictType);

    
    public List<SysDictType> selectDictTypeAll();

    
    public SysDictType selectDictTypeById(Long dictId);

    
    public SysDictType selectDictTypeByType(String dictType);

    
    public int deleteDictTypeById(Long dictId);

    
    public int deleteDictTypeByIds(String ids) throws Exception;

    
    public int insertDictType(SysDictType dictType);

    
    public int updateDictType(SysDictType dictType);

    
    public String checkDictTypeUnique(SysDictType dictType);

    
    public List<Ztree> selectDictTree(SysDictType dictType);
}

package com.jeefast.system.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.jeefast.system.domain.SysMenu;
import com.jeefast.system.domain.SysRole;
import com.jeefast.system.domain.SysUser;
import com.jeefast.common.core.domain.Ztree;


public interface ISysMenuService
{
    
    public List<SysMenu> selectMenusByUser(SysUser user);

    
    public List<SysMenu> selectMenuList(SysMenu menu, Long userId);

    
    public List<SysMenu> selectMenuAll(Long userId);

    
    public Set<String> selectPermsByUserId(Long userId);

    
    public List<Ztree> roleMenuTreeData(SysR<PERSON> role, Long userId);

    
    public List<Ztree> menuTreeData(Long userId);

    
    public Map<String, String> selectPermsAll(Long userId);

    
    public int deleteMenuById(Long menuId);

    
    public SysMenu selectMenuById(Long menuId);

    
    public int selectCountMenuByParentId(Long parentId);

    
    public int selectCountRoleMenuByMenuId(Long menuId);

    
    public int insertMenu(SysMenu menu);

    
    public int updateMenu(SysMenu menu);

    
    public String checkMenuNameUnique(SysMenu menu);
}

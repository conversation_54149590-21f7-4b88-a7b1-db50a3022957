package com.jeefast.system.service.impl;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.jeefast.common.enums.CachEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.system.domain.B2B;
import com.jeefast.system.mapper.SysAgentMapper;
import com.jeefast.system.mapper.SysB2BMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeefast.system.domain.Agent;
import com.jeefast.system.domain.SysConfig;
import com.jeefast.system.mapper.SysConfigMapper;
import com.jeefast.system.service.ISysConfigService;
import com.jeefast.common.constant.UserConstants;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.utils.StringUtils;


@Service
public class SysConfigServiceImpl implements ISysConfigService
{
    @Autowired
    private SysConfigMapper configMapper;

    @Autowired
    private SysAgentMapper sysAgentMapper;

    @Autowired
    private SysB2BMapper sysB2BMapper;

    @Autowired
    private RedisUtil redisUtil;

    private String hiddenKey = "jd.pay.params";

    
    @Override
    public SysConfig selectConfigById(Long configId)
    {
        SysConfig config = new SysConfig();
        config.setConfigId(configId);
        SysConfig sysConfig = configMapper.selectConfig(config);
        if (hiddenKey.contains(sysConfig.getConfigKey())) {
            sysConfig.setConfigValue(null);
        }
        return sysConfig;
    }

    
    @Override
    public String selectConfigByKey(String configKey)
    {
        SysConfig config = new SysConfig();
        config.setConfigKey(configKey);
        SysConfig retConfig = configMapper.selectConfig(config);
        return StringUtils.isNotNull(retConfig) ? retConfig.getConfigValue() : "";
    }

    @Override
    public SysConfig selectSysConfigByKey(String configKey) {
        SysConfig config = new SysConfig();
        config.setConfigKey(configKey);
        return configMapper.selectConfig(config);
    }

    @Override
    public List<SysConfig> selectConfigList(SysConfig config)
    {
        return configMapper.selectConfigList(config);
    }

    
    @Override
    public int insertConfig(SysConfig config)
    {
        return configMapper.insertConfig(config);
    }

    
    @Override
    public int updateConfig(SysConfig config)
    {
        return configMapper.updateConfig(config);
    }

    
    @Override
    public int deleteConfigByIds(String ids)
    {
        return configMapper.deleteConfigByIds(Convert.toStrArray(ids));
    }

    
    @Override
    public String checkConfigKeyUnique(SysConfig config)
    {
        Long configId = StringUtils.isNull(config.getConfigId()) ? -1L : config.getConfigId();
        SysConfig info = configMapper.checkConfigKeyUnique(config.getConfigKey());
        if (StringUtils.isNotNull(info) && info.getConfigId().longValue() != configId.longValue())
        {
            return UserConstants.CONFIG_KEY_NOT_UNIQUE;
        }
        return UserConstants.CONFIG_KEY_UNIQUE;
    }

    @Override
    public void initCachData() {
        delCachData();
//        initAgent();
        initB2B();
        List<SysConfig> sysConfigList = this.selectConfigList(new SysConfig());
        Map<String,String> clientMap = new LinkedHashMap();
        for (SysConfig sysConfig : sysConfigList) {
            redisUtil.set(CachEnum.SYS_CONFIG.getCode() + sysConfig.getConfigKey(),sysConfig.getConfigValue());
            
            if("N".equals(sysConfig.getConfigType())){
                clientMap.put(sysConfig.getConfigKey(),sysConfig.getConfigValue());
            }
        }
        redisUtil.hmset(CachEnum.WE_CHAT_MINI_CONFIG.getCode(),clientMap);
    }

    public void delCachData() {
        redisUtil.del("activity_dict");
    }

    public void initAgent() {
        List<Agent> agentList = sysAgentMapper.selectAgent();
        Map<String,String> clientMap = new LinkedHashMap();
        for (Agent agent : agentList) {
            clientMap.put(agent.getAgentIdString(), agent.toString());
        }
        redisUtil.hmset(CachEnum.AGENT_CONFIG.getCode(), clientMap);
    }

    public void initB2B() {
        List<B2B> b2bList = sysB2BMapper.selectB2B();
        Map<String,String> clientMap = new LinkedHashMap();
        for (B2B b2b : b2bList) {
            clientMap.put(b2b.getUserId(), b2b.getSign());
        }
        redisUtil.hmset(CachEnum.B2B_SIGN.getCode(), clientMap);
    }
}

package com.jeefast.system.service.impl;

import java.util.Arrays;
import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.SpringContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jeefast.system.domain.SysDictData;
import com.jeefast.system.mapper.SysDictDataMapper;
import com.jeefast.system.service.ISysDictDataService;
import com.jeefast.common.core.text.Convert;


@Service
public class SysDictDataServiceImpl implements ISysDictDataService
{
    @Autowired
    private SysDictDataMapper dictDataMapper;

    
    @Override
    public List<SysDictData> selectDictDataList(SysDictData dictData)
    {
        return dictDataMapper.selectDictDataList(dictData);
    }

    
    @Override
    public List<SysDictData> selectDictDataByType(String dictType)
    {
        return dictDataMapper.selectDictDataByType(dictType);
    }

    
    @Override
    public String selectDictLabel(String dictType, String dictValue)
    {
        return dictDataMapper.selectDictLabel(dictType, dictValue);
    }

    
    @Override
    public SysDictData selectDictDataById(Long dictCode)
    {
        return dictDataMapper.selectDictDataById(dictCode);
    }

    
    @Override
    public int deleteDictDataById(Long dictCode)
    {
        return dictDataMapper.deleteDictDataById(dictCode);
    }

    
    @Override
    public int deleteDictDataByIds(String ids)
    {
        return dictDataMapper.deleteDictDataByIds(Convert.toStrArray(ids));
    }

    
    @Override
    public int insertDictData(SysDictData dictData)
    {
        return dictDataMapper.insertDictData(dictData);
    }

    
    @Override
    public int updateDictData(SysDictData dictData)
    {
        return dictDataMapper.updateDictData(dictData);
    }

    
    public static String cacheDictDataByLabel(String dictType, String dictLabel,String defaultValue) {
        RedisUtil redisUtil = SpringContextHolder.getBean(RedisUtil.class);
        String strJson = redisUtil.get(RedisEnum.SYS_DICT_TYPE.getCode()+dictType);
        JSONArray jsonArray = JSONObject.parseArray(strJson);
        for (int i = 0; i < jsonArray.size(); i++) {
            String dictValue = jsonArray.getJSONObject(i).getString("dict_label");
            if(dictLabel.equals(dictValue)){
                defaultValue = dictValue;
                break;
            }
        }
        return defaultValue;
    }

    @Override
    public  Boolean dictValueInDictType(String dictType, String dictValue) {
        List<SysDictData> sysDictDataList = this.selectDictDataByType(dictType);
        Boolean result = false;
        for (int i = 0; i < sysDictDataList.size(); i++) {
            String dValue = sysDictDataList.get(i).getDictValue();
            if(dValue.equals(dictValue)){
                result = true;
                break;
            }
        }
        return result;
    }

    @Override
    public void initCachData() {
        RedisUtil redisUtil = SpringContextHolder.getBean(RedisUtil.class);
        
        List<String> sysDictType = Arrays.asList("cat_shop_error_list","cat_check_config","cat_vip_level_code","cat_avatar_pendent","cat_dynamic_security_no_need");
        sysDictType.forEach(dictType->{
            List<SysDictData> sysDictDataList = this.selectDictDataByType(dictType);
            redisUtil.set(RedisEnum.SYS_DICT_TYPE.getCode() + dictType, JSONObject.toJSONString(sysDictDataList));
        });
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.jeefast</groupId>
	<artifactId>jeefast</artifactId>
	<version>2.0</version>

	<name>jeefast</name>
	<url>http://www.jeefast.vip</url>
	<description>JeeFast管理系统</description>

	<properties>
		<jeefast.version>2.0</jeefast.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<shiro.version>1.6.0</shiro.version>
		<thymeleaf.extras.shiro.version>2.0.0</thymeleaf.extras.shiro.version>
		<druid.version>1.1.22</druid.version>
		<bitwalker.version>1.19</bitwalker.version>
		<kaptcha.version>2.3.2</kaptcha.version>
		<swagger.version>2.9.2</swagger.version>
		<pagehelper.boot.version>1.2.5</pagehelper.boot.version>
		<fastjson.version>1.2.60</fastjson.version>
		<oshi.version>3.9.1</oshi.version>
		<commons.io.version>2.5</commons.io.version>
		<commons.fileupload.version>1.3.3</commons.fileupload.version>
		<jsoup.version>1.11.3</jsoup.version>
		<poi.version>3.17</poi.version>
		<velocity.version>1.7</velocity.version>
	</properties>

	<!-- 依赖声明 -->
	<dependencyManagement>
		<dependencies>

			<!-- SpringBoot的依赖配置 -->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>2.1.1.RELEASE</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!--阿里数据库连接池 -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid-spring-boot-starter</artifactId>
				<version>${druid.version}</version>
			</dependency>

			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
				<version>2.2.3</version>
			</dependency>

			<!--验证码 -->
			<dependency>
				<groupId>com.github.penggle</groupId>
				<artifactId>kaptcha</artifactId>
				<version>${kaptcha.version}</version>
			</dependency>

			<!--Shiro核心框架 -->
			<dependency>
				<groupId>org.apache.shiro</groupId>
				<artifactId>shiro-core</artifactId>
				<version>${shiro.version}</version>
			</dependency>

			<!-- Shiro使用Srping框架 -->
			<dependency>
				<groupId>org.apache.shiro</groupId>
				<artifactId>shiro-spring</artifactId>
				<version>${shiro.version}</version>
			</dependency>

			<!-- Shiro使用EhCache缓存框架 -->
			<dependency>
				<groupId>org.apache.shiro</groupId>
				<artifactId>shiro-ehcache</artifactId>
				<version>${shiro.version}</version>
			</dependency>

			<!-- thymeleaf模板引擎和shiro框架的整合 -->
			<dependency>
				<groupId>com.github.theborakompanioni</groupId>
				<artifactId>thymeleaf-extras-shiro</artifactId>
				<version>${thymeleaf.extras.shiro.version}</version>
			</dependency>

			<!-- 解析客户端操作系统、浏览器等 -->
			<dependency>
				<groupId>eu.bitwalker</groupId>
				<artifactId>UserAgentUtils</artifactId>
				<version>${bitwalker.version}</version>
			</dependency>

			<!-- pagehelper 分页插件 -->
			<dependency>
				<groupId>com.github.pagehelper</groupId>
				<artifactId>pagehelper-spring-boot-starter</artifactId>
				<version>${pagehelper.boot.version}</version>
			</dependency>

			<!-- 获取系统信息 -->
			<dependency>
				<groupId>com.github.oshi</groupId>
				<artifactId>oshi-core</artifactId>
				<version>${oshi.version}</version>
			</dependency>

			<!-- swagger2 -->
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger2</artifactId>
				<version>${swagger.version}</version>
				<exclusions>
					<exclusion>
						<groupId>io.swagger</groupId>
						<artifactId>swagger-annotations</artifactId>
					</exclusion>
					<exclusion>
						<groupId>io.swagger</groupId>
						<artifactId>swagger-models</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<!-- swagger2-UI -->
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger-ui</artifactId>
				<version>${swagger.version}</version>
			</dependency>

			<!--io常用工具类 -->
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>${commons.io.version}</version>
			</dependency>

			<!--文件上传工具类 -->
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons.fileupload.version}</version>
			</dependency>

			<!-- HTML解析器 -->
			<dependency>
				<groupId>org.jsoup</groupId>
				<artifactId>jsoup</artifactId>
				<version>${jsoup.version}</version>
			</dependency>

			<!-- excel工具 -->
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi.version}</version>
			</dependency>

			<!--velocity代码生成使用模板 -->
			<dependency>
				<groupId>org.apache.velocity</groupId>
				<artifactId>velocity</artifactId>
				<version>${velocity.version}</version>
			</dependency>

			<!-- 阿里JSON解析器 -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>

			<!-- 定时任务 -->
			<dependency>
				<groupId>com.jeefast</groupId>
				<artifactId>jeefast-quartz</artifactId>
				<version>${jeefast.version}</version>
			</dependency>

			<!-- 代码生成 -->
			<dependency>
				<groupId>com.jeefast</groupId>
				<artifactId>jeefast-generator</artifactId>
				<version>${jeefast.version}</version>
			</dependency>

			<!-- 核心模块 -->
			<dependency>
				<groupId>com.jeefast</groupId>
				<artifactId>jeefast-framework</artifactId>
				<version>${jeefast.version}</version>
			</dependency>

			<!-- 系统模块 -->
			<dependency>
				<groupId>com.jeefast</groupId>
				<artifactId>jeefast-system</artifactId>
				<version>${jeefast.version}</version>
			</dependency>

			<!-- 通用工具 -->
			<dependency>
				<groupId>com.jeefast</groupId>
				<artifactId>jeefast-common</artifactId>
				<version>${jeefast.version}</version>
			</dependency>

			<dependency>
				<groupId>com.jeefast</groupId>
				<artifactId>cat-app</artifactId>
				<version>${jeefast.version}</version>
			</dependency>

		</dependencies>
	</dependencyManagement>

	<modules>
		<module>jeefast-admin</module>
		<module>jeefast-framework</module>
		<module>jeefast-system</module>
		<module>jeefast-quartz</module>
		<module>jeefast-generator</module>
		<module>jeefast-common</module>
		<!--<module>jeefast-app</module>-->
        <module>cat-app</module>
    </modules>
	<packaging>pom</packaging>

	<dependencies>

		<!--Spring框架基本的核心工具 -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
		</dependency>

		<!-- mybatis-plus begin -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>3.2.0</version>
		</dependency>
		<!-- JavaBean映射工具库 -->
		<dependency>
			<groupId>net.sf.dozer</groupId>
			<artifactId>dozer</artifactId>
			<version>5.4.0</version>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>20.0</version>
		</dependency>

		<!-- lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

	</dependencies>


	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>${project.build.sourceEncoding}</encoding>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<repositories>
		<repository>
			<id>public</id>
			<name>aliyun nexus</name>
			<url>http://maven.aliyun.com/nexus/content/groups/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>public</id>
			<name>aliyun nexus</name>
			<url>http://maven.aliyun.com/nexus/content/groups/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

</project>
-- 一个热版定时任务

CREATE TABLE `hot_search` (
                              `id` VARCHAR(32) NOT NULL COLLATE 'utf8mb4_general_ci',
                              `dynamic_id` VARCHAR(32) NULL DEFAULT NULL COMMENT '动态内容id' COLLATE 'utf8mb4_general_ci',
                              `title` VARCHAR(128) NULL DEFAULT NULL COMMENT '标题' COLLATE 'utf8mb4_general_ci',
                              `be_advertising` CHAR(1) NULL DEFAULT '0' COMMENT '是否广告0否1是' COLLATE 'utf8mb4_general_ci',
                              `hot_type` VARCHAR(16) NULL DEFAULT NULL COMMENT '热版类型 爆 热 新' COLLATE 'utf8mb4_general_ci',
                              `total_read_no` INT(11) NULL DEFAULT '0' COMMENT '最新阅读量',
                              `total_read_no_update_date` DATETIME NULL DEFAULT NULL COMMENT '最新阅读量更新时间',
                              `real_read_no` INT(11) NULL DEFAULT '0' COMMENT '实际阅读量',
                              `invented_read_no` INT(11) NULL DEFAULT '0' COMMENT '虚拟阅读量',
                              `sort_no` INT(11) NULL DEFAULT '0' COMMENT '排序值',
                              `begin_date` DATETIME NULL DEFAULT NULL COMMENT '时效开始时间',
                              `end_date` DATETIME NULL DEFAULT NULL COMMENT '时效结束时间',
                              `route_url` VARCHAR(512) NULL DEFAULT NULL COMMENT '跳转url' COLLATE 'utf8mb4_general_ci',
                              `is_delete` CHAR(1) NULL DEFAULT '0' COMMENT '是否删除0否1是' COLLATE 'utf8mb4_general_ci',
                              `create_date` DATETIME NULL DEFAULT NULL,
                              `update_date` DATETIME NULL DEFAULT NULL,
                              PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='热搜词条'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
ROW_FORMAT=DYNAMIC
;


CREATE TABLE `user_search_history` (
                                       `id` VARCHAR(32) NOT NULL COLLATE 'utf8mb4_general_ci',
                                       `keyword` VARCHAR(128) NULL DEFAULT NULL COMMENT '搜索词' COLLATE 'utf8mb4_general_ci',
                                       `user_id` VARCHAR(32) NOT NULL COMMENT '用户id' COLLATE 'utf8mb4_general_ci',
                                       `create_date` DATETIME NULL DEFAULT NULL,
                                       `update_date` DATETIME NULL DEFAULT NULL,
                                       PRIMARY KEY (`id`) USING BTREE,
                                       INDEX `idx_user_id` (`user_id`) USING BTREE
)
    COMMENT='用户搜索历史'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
ROW_FORMAT=DYNAMIC
;



CREATE TABLE `dynamic_category` (
                                    `id` VARCHAR(32) NOT NULL COLLATE 'utf8mb4_general_ci',
                                    `title` VARCHAR(32) NULL DEFAULT NULL COMMENT '标题' COLLATE 'utf8mb4_general_ci',
                                    `keyword` VARCHAR(512) NULL DEFAULT NULL COMMENT '关键词' COLLATE 'utf8mb4_general_ci',
                                    `sort_no` INT(11) NULL DEFAULT '0' COMMENT '排序',
                                    `be_default` CHAR(1) NULL DEFAULT '0' COMMENT '是否默认0否1是' COLLATE 'utf8mb4_general_ci',
                                    `be_disabled` CHAR(1) NULL DEFAULT '0' COMMENT '是否禁用0否1是' COLLATE 'utf8mb4_general_ci',
                                    `create_date` DATETIME NULL DEFAULT NULL,
                                    `is_delete` CHAR(1) NULL DEFAULT '0' COMMENT '是否删除0否1是' COLLATE 'utf8mb4_general_ci',
                                    PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='动态分类'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
ROW_FORMAT=DYNAMIC
;

CREATE TABLE `dynamic_category_user_sort` (
                                              `id` VARCHAR(32) NOT NULL COLLATE 'utf8mb4_general_ci',
                                              `user_id` VARCHAR(32) NULL DEFAULT NULL COMMENT '用户id' COLLATE 'utf8_bin',
                                              `sort_no` INT(11) NULL DEFAULT '0' COMMENT '排序',
                                              `dynamic_category_id` CHAR(32) NULL DEFAULT NULL COMMENT '动态分类id' COLLATE 'utf8mb4_general_ci',
                                              `create_date` DATETIME NULL DEFAULT NULL,
                                              `is_delete` CHAR(1) NULL DEFAULT '0' COMMENT '是否删除0否1是' COLLATE 'utf8mb4_general_ci',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              INDEX `idx_user_id` (`user_id`) USING BTREE
)
    COMMENT='用户自定义感兴趣动态'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
ROW_FORMAT=DYNAMIC
;



ALTER TABLE `dynamic_info`
    ADD COLUMN `sys_user_id` BIGINT(20) NULL DEFAULT NULL COMMENT '后台用户id',
    ADD COLUMN `need_security` CHAR(1) NULL DEFAULT '1' COMMENT '是否需要内容安全审核 0否 1是',
    ADD COLUMN `be_draft` CHAR(1) NULL DEFAULT '1' COMMENT '是否草稿0否 1是' COLLATE 'utf8mb4_general_ci' ,
	ADD COLUMN `third_type` VARCHAR(8) NULL DEFAULT NULL COMMENT '连接类型 1 商品 2店铺 3群聊' COLLATE 'utf8mb4_general_ci' ,
	ADD COLUMN `third_url` VARCHAR(256) NULL DEFAULT NULL COMMENT '链接地址' COLLATE 'utf8mb4_general_ci' ,
	ADD COLUMN `dynamic_category_id` VARCHAR(32) NULL DEFAULT NULL COMMENT '所属分类id' COLLATE 'utf8mb4_general_ci' ,
	ADD COLUMN `release_type` CHAR(1) NULL DEFAULT '1' COMMENT '是否立即发布0否（定时发布） 1是（立即发布）' COLLATE 'utf8mb4_general_ci' ,
	ADD COLUMN `release_time` DATETIME NULL COMMENT '发布时间',
	ADD COLUMN `be_top` CHAR(1) NULL DEFAULT '0' COMMENT '动态是否置顶首页0否1是' COLLATE 'utf8mb4_general_ci' ,
	ADD COLUMN `be_extend` CHAR(1) NULL DEFAULT '0' COMMENT '是否推广0否1是' COLLATE 'utf8mb4_general_ci' ,
	ADD COLUMN `extend_begin_time` DATETIME NULL COMMENT '推广开始时间'  ,
	ADD COLUMN `extend_end_time` DATETIME NULL COMMENT '推广结束时间',
	ADD COLUMN `audit_status` VARCHAR(32) NULL DEFAULT NULL COMMENT '最终审核状态 0审核中 1通过 2不通过 3待复审' COLLATE 'utf8_bin' ;
update dynamic_info set need_security = 0 where 1=1;
update dynamic_info set be_draft = 0 where 1=1;
update dynamic_info set release_time =now() where 1=1;
update dynamic_info set audit_status =1 where 1=1;

ALTER TABLE `dynamic_info`
    ADD INDEX `idx_category_id` (`dynamic_category_id`);




--字典
INSERT INTO `sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '热版类型', 'cat_hot_type', '0', 'admin', '2025-03-11 10:52:58', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 3, '新', '3', 'cat_hot_type', NULL, NULL, 'Y', '0', 'admin', '2025-03-11 10:54:10', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 2, '热', '2', 'cat_hot_type', NULL, NULL, 'Y', '0', 'admin', '2025-03-11 10:54:04', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 1, '爆', '1', 'cat_hot_type', NULL, NULL, 'Y', '0', 'admin', '2025-03-11 10:53:55', '', NULL, NULL);

INSERT INTO `sys_dict_type` ( `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '连接类型', 'cat_link_type', '0', 'admin', '2025-03-11 10:52:58', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 3, '群聊', '3', 'cat_link_type', NULL, NULL, 'Y', '0', 'admin', '2025-03-11 10:54:10', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 2, '店铺', '2', 'cat_link_type', NULL, NULL, 'Y', '0', 'admin', '2025-03-11 10:54:04', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 1, '商品', '1', 'cat_link_type', NULL, NULL, 'Y', '0', 'admin', '2025-03-11 10:53:55', '', NULL, NULL);



-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url,menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('热搜词条', '2317', '1', '/cat/search', 'C', '0', 'cat:search:view', '#', 'admin', '2018-03-01', 'jeefast', '2018-03-01', '热搜词条菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu  (menu_name, parent_id, order_num, url,menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('热搜词条查询', @parentId, '1',  '#',  'F', '0', 'cat:search:list',         '#', 'admin', '2018-03-01', 'jeefast', '2018-03-01', '');

insert into sys_menu  (menu_name, parent_id, order_num, url,menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('热搜词条新增', @parentId, '2',  '#',  'F', '0', 'cat:search:add',          '#', 'admin', '2018-03-01', 'jeefast', '2018-03-01', '');

insert into sys_menu  (menu_name, parent_id, order_num, url,menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('热搜词条修改', @parentId, '3',  '#',  'F', '0', 'cat:search:edit',         '#', 'admin', '2018-03-01', 'jeefast', '2018-03-01', '');

insert into sys_menu  (menu_name, parent_id, order_num, url,menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('热搜词条删除', @parentId, '4',  '#',  'F', '0', 'cat:search:remove',       '#', 'admin', '2018-03-01', 'jeefast', '2018-03-01', '');


-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, url,menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('动态分类', '2317', '1', '/cat/category', 'C', '0', 'cat:category:view', '#', 'admin', '2018-03-01', 'jeefast', '2018-03-01', '动态分类菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu  (menu_name, parent_id, order_num, url,menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('动态分类查询', @parentId, '1',  '#',  'F', '0', 'cat:category:list',         '#', 'admin', '2018-03-01', 'jeefast', '2018-03-01', '');

insert into sys_menu  (menu_name, parent_id, order_num, url,menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('动态分类新增', @parentId, '2',  '#',  'F', '0', 'cat:category:add',          '#', 'admin', '2018-03-01', 'jeefast', '2018-03-01', '');

insert into sys_menu  (menu_name, parent_id, order_num, url,menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('动态分类修改', @parentId, '3',  '#',  'F', '0', 'cat:category:edit',         '#', 'admin', '2018-03-01', 'jeefast', '2018-03-01', '');

insert into sys_menu  (menu_name, parent_id, order_num, url,menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('动态分类删除', @parentId, '4',  '#',  'F', '0', 'cat:category:remove',       '#', 'admin', '2018-03-01', 'jeefast', '2018-03-01', '');


insert into sys_menu (menu_name, parent_id, order_num, url,menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('批量修改动态分类', 2026, '5', '#', 'F', '0', 'cat:dynamicBackstage:batchEditCategory', '#', 'admin', '2018-03-01', 'jeefast', '2018-03-01', '批量修改动态分类');
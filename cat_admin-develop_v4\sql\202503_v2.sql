
ALTER TABLE `attention_info`
    ADD COLUMN `to_user_new_dynamic_num` INT NULL DEFAULT '0' COMMENT '被关注者是否有新动态',
	ADD COLUMN `to_user_last_dynamic_date` DATETIME NULL DEFAULT NULL COMMENT '被关注者最后动态发表时间',
	ADD COLUMN `user_last_view_date` DATETIME NULL DEFAULT NULL COMMENT '关注者最后查看被关注者主页时间';
UPDATE `attention_info` SET `to_user_last_dynamic_date`=NOW(), `user_last_view_date`=NOW() WHERE 1=1;


update dynamic_info set need_security = 0 where 1=1;
update dynamic_info set be_draft = 0 where 1=1;
update dynamic_info set release_time =now() where 1=1;
update dynamic_info set audit_status =1 where 1=1;


UPDATE `sys_config` SET `config_value`='5' WHERE  `config_key`='sys.cat.upload.type';
UPDATE `sys_config` SET `remark`=CONCAT(remark,',5-腾讯云点播') WHERE  `config_key`='sys.cat.upload.type';



CREATE TABLE `dynamic_family_recommend` (
                                            `id` VARCHAR(32) NOT NULL COMMENT '主键ID' COLLATE 'utf8mb4_general_ci',
                                            `user_id` VARCHAR(32) NOT NULL COMMENT '推荐人ID' COLLATE 'utf8mb4_general_ci',
                                            `receiver_id` VARCHAR(32) NOT NULL COMMENT '接收人ID' COLLATE 'utf8mb4_general_ci',
                                            `content_type` TINYINT(4) NULL DEFAULT '1' COMMENT '推荐内容类型（1-动态等）',
                                            `dynamic_id` VARCHAR(32) NOT NULL COMMENT '动态ID' COLLATE 'utf8mb4_general_ci',
                                            `reason` VARCHAR(500) NULL DEFAULT NULL COMMENT '推荐理由' COLLATE 'utf8mb4_general_ci',
                                            `is_read` CHAR(1) NOT NULL DEFAULT '0' COMMENT '是否已读（0-否，1-是）' COLLATE 'utf8mb4_general_ci',
                                            `read_time` DATETIME NULL DEFAULT NULL COMMENT '阅读时间',
                                            `create_date` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `update_date` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                            `is_delete` CHAR(1) NOT NULL DEFAULT '0' COMMENT '是否删除（0-否，1-是）' COLLATE 'utf8mb4_general_ci',
                                            PRIMARY KEY (`id`) USING BTREE,
                                            INDEX `idx_user_id` (`user_id`) USING BTREE COMMENT '推荐人索引',
                                            INDEX `idx_receiver_id` (`receiver_id`) USING BTREE COMMENT '接收人索引',
                                            INDEX `idx_dynamic_id` (`dynamic_id`) USING BTREE COMMENT '内容索引'
)
    COMMENT='家人推荐记录表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;
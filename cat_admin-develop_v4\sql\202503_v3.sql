
ALTER TABLE `family`
    ADD COLUMN `family_user_new_dynamic_num` INT NULL DEFAULT '0' COMMENT '家庭成员是否有新动态',
	ADD COLUMN `family_user_last_dynamic_date` DATETIME NULL DEFAULT NULL COMMENT '家庭成员最后动态发表时间',
	ADD COLUMN `user_last_view_date` DATETIME NULL DEFAULT NULL COMMENT '主成员最后查看家庭成员者主页时间';
UPDATE `family` SET `family_user_last_dynamic_date`=NOW(), `user_last_view_date`=NOW() WHERE 1=1;


ALTER TABLE `dynamic_info`
    DROP COLUMN `be_draft`;
ALTER TABLE `dynamic_info`
    DROP COLUMN `be_top`;

update dynamic_info set is_top = 0 where 1=1;
update dynamic_info set need_security = 0 where 1=1;
update dynamic_info set release_time =now() where 1=1;
update dynamic_info set audit_status =1 where 1=1;

ALTER TABLE `dynamic_info`
    CHANGE COLUMN `is_top` `is_top` CHAR(1) NULL DEFAULT '0' COMMENT '是否上首页' COLLATE 'utf8mb4_general_ci';


INSERT INTO `sys_menu` ( `menu_name`, `parent_id`, `order_num`, `url`, `target`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '内容发布', 2317, 5, '/cat/dynamicBackstage/new/addPage', 'menuItem', 'C', '0', 'cat:dynamicBackstage:add:page', '#', 'admin', '2025-03-12 10:08:18', 'admin', '2025-04-03 10:28:13', '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `url`, `target`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '草稿箱', 2317, 6, '/cat/dynamicBackstage/drafts', 'menuItem', 'C', '0', 'cat:dynamicBackstage:drafts', '#', 'admin', '2025-04-01 16:23:16', 'admin', '2025-04-03 10:27:04', '');


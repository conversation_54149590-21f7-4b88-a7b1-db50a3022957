-- 一个内容安全定时任务

CREATE TABLE `dynamic_content_security` (
                                            `id` VARCHAR(32) NOT NULL COMMENT '主键id' COLLATE 'utf8_bin',
                                            `dynamic_id` VARCHAR(32) NULL DEFAULT NULL COMMENT '动态内容id' COLLATE 'utf8mb4_general_ci',
                                            `type` CHAR(1) NULL DEFAULT NULL COMMENT '类型 动态标题、动态文字内容、图、视频' COLLATE 'utf8_bin',
                                            `task_id` VARCHAR(64) NULL DEFAULT NULL COMMENT '任务taskId' COLLATE 'utf8_bin',
                                            `relate_id` VARCHAR(32) NULL DEFAULT NULL COMMENT '动态内容媒体id' COLLATE 'utf8_bin',
                                            `relate_url` VARCHAR(286) NULL DEFAULT NULL COMMENT '媒体内容url' COLLATE 'utf8_bin',
                                            `content` TEXT NULL DEFAULT NULL COMMENT '文字内容' COLLATE 'utf8mb4_general_ci',
                                            `biz_type` VARCHAR(128) NULL DEFAULT NULL COMMENT '识别策略' COLLATE 'utf8_bin',
                                            `status` VARCHAR(32) NULL DEFAULT NULL COMMENT '任务状态：FINISH（任务已完成）、PENDING （任务等待中）、RUNNING （任务进行中）、ERROR （任务出错）、CANCELLED （任务已取消）' COLLATE 'utf8_bin',
                                            `suggestion` VARCHAR(128) NULL DEFAULT NULL COMMENT '后续操作建议：Block：建议屏蔽，Review ：建议人工复审，Pass：建议通过' COLLATE 'utf8_bin',
                                            `labels` VARCHAR(32) NULL DEFAULT NULL COMMENT '返回检测结果：Normal：正常，Porn：色情，Abuse：谩骂，Ad：广告；以及其他令人反感、不安全或不适宜的内容类型。' COLLATE 'utf8_bin',
                                            `keywords` VARCHAR(1024) NULL DEFAULT NULL COMMENT '返回检测结果，命中的关键词。' COLLATE 'utf8_bin',
                                            `score` INT(11) NULL DEFAULT NULL COMMENT '置信度,取值范围：0（置信度最低）-100（置信度最高 ），越高代表越有可能属于当前返回的标签',
                                            `resp_content` TEXT NULL DEFAULT NULL COMMENT '内容安全检查返回信息' COLLATE 'utf8_bin',
                                            `audit_status` VARCHAR(32) NULL DEFAULT NULL COMMENT '最终审核状态 0待审核 1通过 2不通过' COLLATE 'utf8_bin',
                                            `audit_reason` VARCHAR(32) NULL DEFAULT NULL COMMENT '审核原因' COLLATE 'utf8_bin',
                                            `sort_no` INT(11) NULL DEFAULT NULL,
                                            `create_date` DATETIME NULL DEFAULT NULL,
                                            PRIMARY KEY (`id`) USING BTREE,
                                            INDEX `idx_dynamic_id_type` (`dynamic_id`, `type`) USING BTREE
)
    COMMENT='动态内容安全审核记录'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
ROW_FORMAT=DYNAMIC
;

update dynamic_info set need_security = 0 where 1=1;
update dynamic_info set audit_status =1 where 1=1;


-- 系统配置参数
INSERT INTO `sys_config` ( `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '动态标题文本内容安全审核策略的编号', 'text_security_biz_type1', 'title', 'Y', 'admin', '2025-03-14 17:49:33', '', NULL, '动态标题文本内容安全审核策略的编号');
INSERT INTO `sys_config` ( `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '动态内容文本内容安全审核策略的编号', 'text_security_biz_type2', 'content', 'Y', 'admin', '2025-03-14 17:49:33', '', NULL, '动态内容文本内容安全审核策略的编号');
INSERT INTO `sys_config` ( `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '动态图片内容安全审核策略的编号', 'img_security_biz_type', 'dynamic_image', 'Y', 'admin', '2025-03-14 17:49:33', '', NULL, '动态图片内容安全审核策略的编号');
INSERT INTO `sys_config` ( `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '动态视频内容安全审核策略的编号', 'video_security_biz_type', 'dynamic_video', 'Y', 'admin', '2025-03-14 17:49:33', '', NULL, '动态视频内容安全审核策略的编号');



-- 字典
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '内容审核类型', 'cat_dynamic_security', '0', 'admin', '2025-03-14 15:18:36', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 1, '标题', '1', 'cat_dynamic_security', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:18:56', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 2, '内容', '2', 'cat_dynamic_security', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:10', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 3, '图片', '3', 'cat_dynamic_security', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:19', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 4, '视频', '4', 'cat_dynamic_security', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:27', '', NULL, NULL);



INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '内容审核任务状态', 'cat_dynamic_security_status', '0', 'admin', '2025-03-14 15:18:36', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 1, '任务已完成', 'FINISH', 'cat_dynamic_security_status', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:18:56', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 2, '任务等待中', 'PENDING', 'cat_dynamic_security_status', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:10', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 3, '任务进行中', 'RUNNING', 'cat_dynamic_security_status', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:19', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 4, '任务出错', 'ERROR', 'cat_dynamic_security_status', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:27', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 5, '任务已取消', 'CANCELLED', 'cat_dynamic_security_status', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:27', '', NULL, NULL);

INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '内容审核操作建议', 'cat_dynamic_security_suggestion', '0', 'admin', '2025-03-14 15:18:36', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 1, '建议通过', 'Pass', 'cat_dynamic_security_suggestion', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:18:56', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 2, '建议人工复审', 'Review', 'cat_dynamic_security_suggestion', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:10', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 3, '建议屏蔽', 'Block', 'cat_dynamic_security_suggestion', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:19', '', NULL, NULL);



INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '内容审核返回检测结果类型', 'cat_dynamic_security_label', '0', 'admin', '2025-03-14 15:18:36', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 1, '正常', 'Normal', 'cat_dynamic_security_label', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:18:56', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 2, '色情', 'Porn', 'cat_dynamic_security_label', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:10', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 3, '谩骂', 'Abuse', 'cat_dynamic_security_label', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:19', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 4, '广告', 'AD', 'cat_dynamic_security_label', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:27', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 5, '涉政', 'Polity', 'cat_dynamic_security_label', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:27', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 6, '涉毒', 'Illegal', 'cat_dynamic_security_label', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:27', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 7, '性感', 'Sexy', 'cat_dynamic_security_label', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:27', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 8, '暴恐', 'Terror', 'cat_dynamic_security_label', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:27', '', NULL, NULL);

INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '动态内容免审核角色', 'cat_dynamic_security_no_need', '0', 'admin', '2025-03-14 15:18:36', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 1, '管理员', 'admin', 'cat_dynamic_security_no_need', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:18:56', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 2, '运营', 'operation', 'cat_dynamic_security_no_need', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:10', '', NULL, NULL);
INSERT INTO `sys_dict_data` ( `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 3, '测试', 'test', 'cat_dynamic_security_no_need', NULL, NULL, 'Y', '0', 'admin', '2025-03-14 15:19:19', '', NULL, NULL);


INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ('内容审核状态', 'cat_dynamic_audit_status', '0', 'admin', '2025-04-07 09:41:04', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 3, '待复审', '3', 'cat_dynamic_audit_status', NULL, NULL, 'Y', '0', 'admin', '2025-04-07 09:43:52', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 2, '审核不通过', '2', 'cat_dynamic_audit_status', '', '', 'Y', '0', 'admin', '2025-04-07 09:43:17', 'admin', '2025-04-07 09:43:35', '');
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 1, '审核通过', '1', 'cat_dynamic_audit_status', NULL, NULL, 'Y', '0', 'admin', '2025-04-07 09:43:07', '', NULL, NULL);
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 0, '待审核', '0', 'cat_dynamic_audit_status', NULL, NULL, 'Y', '0', 'admin', '2025-04-07 09:42:29', '', NULL, NULL);



INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `url`, `target`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '内容审核', 2317, 8, '#', 'menuItem', 'M', '0', NULL, '#', 'admin', '2025-04-03 10:40:57', '', NULL, '');
SELECT @parentId := LAST_INSERT_ID();
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `url`, `target`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '审核通过', @parentId, 3, '/cat/dynamicBackstage/audit/approved', 'menuItem', 'C', '0', 'cat:dynamicBackstage:audit:approved', '#', 'admin', '2025-04-03 10:44:23', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `url`, `target`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( '人工复审', @parentId, 2, '/cat/dynamicBackstage/audit/manual', 'menuItem', 'C', '0', 'cat:dynamicBackstage:audit:manual', '#', 'admin', '2025-04-03 10:43:47', '', NULL, '');
INSERT INTO `sys_menu` (`menu_name`, `parent_id`, `order_num`, `url`, `target`, `menu_type`, `visible`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES ( 'AI审核中', @parentId, 1, '/cat/dynamicBackstage/audit/ai', 'menuItem', 'C', '0', 'cat:dynamicBackstage:audit:ai', '#', 'admin', '2025-04-03 10:42:55', '', NULL, '');
